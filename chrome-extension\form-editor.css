/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    min-height: 100vh;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo img {
    width: 48px;
    height: 48px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn.primary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.btn.secondary {
    background: rgba(255,255,255,0.1);
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 主要内容区域 */
.main-content {
    display: flex;
    min-height: calc(100vh - 80px);
}

/* 侧边栏 */
.sidebar {
    width: 320px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    padding: 20px;
    overflow-y: auto;
}

/* AI助手 */
.ai-assistant {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.ai-title {
    font-size: 16px;
    font-weight: 600;
}

.ai-content {
    font-size: 14px;
}

.ai-suggestions {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    min-height: 80px;
    font-size: 13px;
    line-height: 1.4;
}

.ai-actions {
    display: flex;
    gap: 8px;
}

.ai-btn {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.ai-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* 快速操作 */
.quick-actions {
    margin-bottom: 20px;
}

.quick-actions h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #333;
}

.action-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.action-item:hover {
    background: #f8f9fa;
    border-color: #667eea;
    transform: translateY(-1px);
}

.action-item .icon {
    font-size: 18px;
}

/* 智能内容解析面板 */
.content-parser-panel {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    margin-bottom: 20px;
    overflow: hidden;
}

.parser-panel-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.parser-panel-header h3 {
    font-size: 14px;
    margin: 0;
}

.close-panel-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.close-panel-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.parser-panel-content {
    padding: 16px;
}

.parser-panel-content textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
    line-height: 1.4;
    margin-bottom: 12px;
}

.parser-panel-content textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.parser-panel-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.parser-panel-actions .btn {
    flex: 1;
    padding: 10px 16px;
    font-size: 13px;
}

.parser-panel-actions .btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.parser-panel-actions .btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

/* 解析状态 */
.parse-status {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}

.status-message {
    font-size: 13px;
    color: #666;
    margin-bottom: 12px;
}

.progress-bar-container {
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.3s ease;
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 验证状态 */
.validation-status {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 16px;
}

.validation-status h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #333;
}

.status-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
}

.status-item.pending {
    background: #fff3cd;
    color: #856404;
}

.status-item.success {
    background: #d4edda;
    color: #155724;
}

.status-item.error {
    background: #f8d7da;
    color: #721c24;
}

/* 表单区域 */
.form-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }
}

@media (max-width: 768px) {
    .header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-actions {
        width: 100%;
        justify-content: center;
    }
    
    .sidebar {
        padding: 15px;
    }
    
    .form-area {
        padding: 15px;
    }
}
