<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC增强版自动填充工具</title>
    <style>
        /* 简化的样式，专注于功能 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f7fa;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .content {
            padding: 30px;
        }

        .method-selector {
            margin-bottom: 30px;
            text-align: center;
        }

        .method-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .method-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .method-btn.active {
            background: #48bb78;
        }

        .method-content {
            display: none;
            margin-top: 30px;
        }

        .method-content.active {
            display: block;
        }

        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }

        .copy-btn:hover {
            background: #218838;
        }

        .instruction {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
        }

        .warning {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
        }

        .bookmarklet {
            background: #333;
            color: #fff;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }

        .step {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 MDAC增强版自动填充工具</h1>
            <p>提供多种填充方案，确保在任何情况下都能成功填充表单</p>
        </div>

        <div class="content">
            <div class="method-selector">
                <button class="method-btn active" onclick="showMethod('bookmarklet')">书签工具法</button>
                <button class="method-btn" onclick="showMethod('manual')">手动复制法</button>
                <button class="method-btn" onclick="showMethod('script')">脚本注入法</button>
            </div>

            <!-- 书签工具法 -->
            <div id="bookmarklet" class="method-content active">
                <h2>📖 方法一：书签工具法（推荐）</h2>
                
                <div class="instruction">
                    <strong>这是最可靠的方法，不受跨域限制影响</strong>
                </div>

                <div class="step">
                    <span class="step-number">1</span>
                    <strong>创建书签：</strong>
                    <p>将下面的代码拖拽到浏览器书签栏，或者复制代码手动创建书签：</p>
                    <div class="bookmarklet" id="bookmarkletCode">
                        javascript:(function(){var data=JSON.parse(localStorage.getItem('mdacFormData')||'{}');if(!data.name){alert('请先在MDAC工具中填写数据！');return;}function fillField(selector,value){var el=document.querySelector(selector);if(el){if(el.tagName==='SELECT'){for(var i=0;i<el.options.length;i++){if(el.options[i].value===value||el.options[i].text.includes(value)){el.value=el.options[i].value;break;}}}else{el.value=value;}el.dispatchEvent(new Event('change',{bubbles:true}));el.dispatchEvent(new Event('input',{bubbles:true}));return true;}return false;}setTimeout(function(){var count=0;count+=fillField('input[name*="name"]',data.name)?1:0;count+=fillField('input[name*="passport"]',data.passportNo)?1:0;count+=fillField('input[name*="birth"]',data.dateOfBirth)?1:0;count+=fillField('select[name*="nationality"]',data.nationality)?1:0;count+=fillField('select[name*="sex"]',data.sex)?1:0;count+=fillField('input[name*="expiry"]',data.passportExpiry)?1:0;count+=fillField('input[name*="email"]',data.email)?1:0;count+=fillField('input[name*="mobile"]',data.mobileNo)?1:0;count+=fillField('input[name*="arrival"]',data.arrivalDate)?1:0;count+=fillField('input[name*="departure"]',data.departureDate)?1:0;count+=fillField('input[name*="flight"]',data.flightNo)?1:0;count+=fillField('textarea[name*="address"]',data.address)?1:0;count+=fillField('input[name*="postcode"]',data.postcode)?1:0;count+=fillField('input[name*="city"]',data.city)?1:0;alert('填充完成！已填充'+count+'个字段。请检查信息并提交。');},1000);})();
                    </div>
                    <button class="copy-btn" onclick="copyBookmarklet()">复制书签代码</button>
                </div>

                <div class="step">
                    <span class="step-number">2</span>
                    <strong>填写数据：</strong>
                    <p>在下方填写您的MDAC信息：</p>
                    <div id="quickForm">
                        <input type="text" id="qName" placeholder="姓名（英文大写）" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="text" id="qPassport" placeholder="护照号码" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="date" id="qBirth" style="width:100%;margin:5px 0;padding:10px;">
                        <select id="qNationality" style="width:100%;margin:5px 0;padding:10px;">
                            <option value="">选择国籍</option>
                            <option value="CHINA">中国</option>
                            <option value="SINGAPORE">新加坡</option>
                            <option value="THAILAND">泰国</option>
                        </select>
                        <select id="qSex" style="width:100%;margin:5px 0;padding:10px;">
                            <option value="">选择性别</option>
                            <option value="MALE">男性</option>
                            <option value="FEMALE">女性</option>
                        </select>
                        <input type="date" id="qPassportExpiry" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="email" id="qEmail" value="<EMAIL>" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="text" id="qMobile" value="*********" placeholder="手机号码" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="date" id="qArrival" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="date" id="qDeparture" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="text" id="qFlight" placeholder="航班号" style="width:100%;margin:5px 0;padding:10px;">
                        <textarea id="qAddress" placeholder="马来西亚地址" style="width:100%;margin:5px 0;padding:10px;height:60px;"></textarea>
                        <input type="text" id="qPostcode" placeholder="邮政编码" style="width:100%;margin:5px 0;padding:10px;">
                        <input type="text" id="qCity" placeholder="城市" style="width:100%;margin:5px 0;padding:10px;">
                        <button class="method-btn" onclick="saveQuickData()" style="margin:10px 0;">保存数据</button>
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">3</span>
                    <strong>使用书签：</strong>
                    <p>1. 打开MDAC官方网站：<a href="https://imigresen-online.imi.gov.my/mdac/main?registerMain" target="_blank">点击这里</a></p>
                    <p>2. 在MDAC页面上点击您创建的书签</p>
                    <p>3. 系统将自动填充所有字段</p>
                </div>
            </div>

            <!-- 手动复制法 -->
            <div id="manual" class="method-content">
                <h2>📋 方法二：手动复制法</h2>
                
                <div class="instruction">
                    <strong>适用于所有情况，100%可靠</strong>
                </div>

                <div class="step">
                    <span class="step-number">1</span>
                    <strong>生成填充数据：</strong>
                    <button class="method-btn" onclick="generateManualData()">生成复制数据</button>
                </div>

                <div class="step">
                    <span class="step-number">2</span>
                    <strong>复制数据：</strong>
                    <div id="manualData" class="data-display" style="display:none;">
                        <!-- 数据将在这里显示 -->
                    </div>
                    <button class="copy-btn" onclick="copyManualData()" style="display:none;" id="copyManualBtn">复制所有数据</button>
                </div>

                <div class="step">
                    <span class="step-number">3</span>
                    <strong>手动填写：</strong>
                    <p>打开MDAC网站，按照上面的数据逐个填写到对应字段中</p>
                </div>
            </div>

            <!-- 脚本注入法 -->
            <div id="script" class="method-content">
                <h2>⚡ 方法三：脚本注入法</h2>
                
                <div class="warning">
                    <strong>注意：</strong>此方法需要浏览器开发者工具，适合有一定技术基础的用户
                </div>

                <div class="step">
                    <span class="step-number">1</span>
                    <strong>准备数据：</strong>
                    <button class="method-btn" onclick="generateScriptData()">生成注入脚本</button>
                </div>

                <div class="step">
                    <span class="step-number">2</span>
                    <strong>注入脚本：</strong>
                    <div id="scriptData" class="data-display" style="display:none;">
                        <!-- 脚本将在这里显示 -->
                    </div>
                    <button class="copy-btn" onclick="copyScriptData()" style="display:none;" id="copyScriptBtn">复制脚本</button>
                </div>

                <div class="step">
                    <span class="step-number">3</span>
                    <strong>执行脚本：</strong>
                    <p>1. 打开MDAC网站</p>
                    <p>2. 按F12打开开发者工具</p>
                    <p>3. 切换到Console标签</p>
                    <p>4. 粘贴脚本并按回车执行</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局数据存储
        let currentData = {};

        // 显示不同的方法
        function showMethod(method) {
            // 隐藏所有内容
            document.querySelectorAll('.method-content').forEach(el => {
                el.classList.remove('active');
            });
            
            // 移除所有按钮的active状态
            document.querySelectorAll('.method-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的内容
            document.getElementById(method).classList.add('active');
            
            // 激活对应按钮
            event.target.classList.add('active');
        }

        // 保存快速表单数据
        function saveQuickData() {
            const data = {
                name: document.getElementById('qName').value.toUpperCase(),
                passportNo: document.getElementById('qPassport').value.toUpperCase(),
                dateOfBirth: formatDate(document.getElementById('qBirth').value),
                nationality: document.getElementById('qNationality').value,
                sex: document.getElementById('qSex').value,
                passportExpiry: formatDate(document.getElementById('qPassportExpiry').value),
                email: document.getElementById('qEmail').value.toLowerCase(),
                mobileNo: document.getElementById('qMobile').value,
                arrivalDate: formatDate(document.getElementById('qArrival').value),
                departureDate: formatDate(document.getElementById('qDeparture').value),
                flightNo: document.getElementById('qFlight').value.toUpperCase(),
                address: document.getElementById('qAddress').value.toUpperCase(),
                postcode: document.getElementById('qPostcode').value,
                city: document.getElementById('qCity').value.toUpperCase()
            };

            // 验证必填字段
            const required = ['name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 'passportExpiry', 'email', 'mobileNo', 'arrivalDate', 'departureDate', 'flightNo', 'address', 'postcode', 'city'];
            for (let field of required) {
                if (!data[field]) {
                    alert(`请填写：${getFieldName(field)}`);
                    return;
                }
            }

            // 保存到localStorage和全局变量
            localStorage.setItem('mdacFormData', JSON.stringify(data));
            currentData = data;
            
            alert('数据已保存！现在可以使用书签工具进行自动填充。');
        }

        // 格式化日期
        function formatDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        // 获取字段中文名
        function getFieldName(field) {
            const names = {
                'name': '姓名',
                'passportNo': '护照号码',
                'dateOfBirth': '出生日期',
                'nationality': '国籍',
                'sex': '性别',
                'passportExpiry': '护照到期日期',
                'email': '电子邮箱',
                'mobileNo': '手机号码',
                'arrivalDate': '到达日期',
                'departureDate': '离开日期',
                'flightNo': '航班号',
                'address': '地址',
                'postcode': '邮政编码',
                'city': '城市'
            };
            return names[field] || field;
        }

        // 复制书签代码
        function copyBookmarklet() {
            const code = document.getElementById('bookmarkletCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                alert('书签代码已复制！请在浏览器中创建新书签并粘贴此代码。');
            });
        }

        // 生成手动复制数据
        function generateManualData() {
            if (!currentData.name) {
                alert('请先在书签工具法中填写并保存数据！');
                return;
            }

            const manualText = `
=== MDAC表单填充数据 ===

个人信息：
姓名: ${currentData.name}
护照号码: ${currentData.passportNo}
出生日期: ${currentData.dateOfBirth}
国籍: ${currentData.nationality}
性别: ${currentData.sex}
护照到期日期: ${currentData.passportExpiry}
电子邮箱: ${currentData.email}
确认邮箱: ${currentData.email}
国家代码: +60
手机号码: ${currentData.mobileNo}

旅行信息：
到达日期: ${currentData.arrivalDate}
离开日期: ${currentData.departureDate}
航班号: ${currentData.flightNo}
旅行方式: AIR
最后港口: Singapore Changi Airport
住宿安排: HOTEL

地址信息：
地址: ${currentData.address}
州: KUALA LUMPUR
邮政编码: ${currentData.postcode}
城市: ${currentData.city}
            `;

            document.getElementById('manualData').textContent = manualText;
            document.getElementById('manualData').style.display = 'block';
            document.getElementById('copyManualBtn').style.display = 'inline-block';
        }

        // 复制手动数据
        function copyManualData() {
            const text = document.getElementById('manualData').textContent;
            navigator.clipboard.writeText(text).then(() => {
                alert('数据已复制到剪贴板！');
            });
        }

        // 生成脚本注入数据
        function generateScriptData() {
            if (!currentData.name) {
                alert('请先在书签工具法中填写并保存数据！');
                return;
            }

            const script = `
// MDAC自动填充脚本
(function() {
    const data = ${JSON.stringify(currentData, null, 2)};
    
    function fillField(selector, value) {
        const el = document.querySelector(selector);
        if (el) {
            if (el.tagName === 'SELECT') {
                for (let i = 0; i < el.options.length; i++) {
                    if (el.options[i].value === value || el.options[i].text.includes(value)) {
                        el.value = el.options[i].value;
                        break;
                    }
                }
            } else {
                el.value = value;
            }
            el.dispatchEvent(new Event('change', {bubbles: true}));
            el.dispatchEvent(new Event('input', {bubbles: true}));
            return true;
        }
        return false;
    }
    
    setTimeout(() => {
        let count = 0;
        count += fillField('input[name*="name"]', data.name) ? 1 : 0;
        count += fillField('input[name*="passport"]', data.passportNo) ? 1 : 0;
        count += fillField('input[name*="birth"]', data.dateOfBirth) ? 1 : 0;
        count += fillField('select[name*="nationality"]', data.nationality) ? 1 : 0;
        count += fillField('select[name*="sex"]', data.sex) ? 1 : 0;
        count += fillField('input[name*="expiry"]', data.passportExpiry) ? 1 : 0;
        count += fillField('input[name*="email"]', data.email) ? 1 : 0;
        count += fillField('input[name*="mobile"]', data.mobileNo) ? 1 : 0;
        count += fillField('input[name*="arrival"]', data.arrivalDate) ? 1 : 0;
        count += fillField('input[name*="departure"]', data.departureDate) ? 1 : 0;
        count += fillField('input[name*="flight"]', data.flightNo) ? 1 : 0;
        count += fillField('textarea[name*="address"]', data.address) ? 1 : 0;
        count += fillField('input[name*="postcode"]', data.postcode) ? 1 : 0;
        count += fillField('input[name*="city"]', data.city) ? 1 : 0;
        
        alert('填充完成！已填充 ' + count + ' 个字段。请检查信息并提交。');
    }, 1000);
})();
            `;

            document.getElementById('scriptData').textContent = script;
            document.getElementById('scriptData').style.display = 'block';
            document.getElementById('copyScriptBtn').style.display = 'inline-block';
        }

        // 复制脚本数据
        function copyScriptData() {
            const script = document.getElementById('scriptData').textContent;
            navigator.clipboard.writeText(script).then(() => {
                alert('脚本已复制到剪贴板！');
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('qArrival').value = tomorrow.toISOString().split('T')[0];
            
            const dayAfter = new Date();
            dayAfter.setDate(dayAfter.getDate() + 3);
            document.getElementById('qDeparture').value = dayAfter.toISOString().split('T')[0];
            
            // 尝试从localStorage恢复数据
            const savedData = localStorage.getItem('mdacFormData');
            if (savedData) {
                currentData = JSON.parse(savedData);
            }
        });
    </script>
</body>
</html>
