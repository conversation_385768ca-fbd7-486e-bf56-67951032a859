# MDAC AI智能填充工具 - Chrome扩展版

基于Gemini AI的马来西亚数字入境卡智能填充Chrome扩展，提供完整的自动化填写、验证和API分析功能。

## 🚀 功能特性

### 核心功能
- **🤖 AI智能填充** - 基于Gemini AI的智能表单填写和验证
- **📋 多模板管理** - 支持多个个人信息模板的保存和管理
- **⚡ API直接提交** - 实验性的API接口直接提交功能
- **🔒 数据加密保护** - 本地数据加密存储，保护隐私安全
- **📊 填充历史记录** - 完整的操作历史和统计分析
- **🌐 多语言支持** - 支持中文、英文、马来语界面

### 技术特性
- **Manifest V3** - 使用最新的Chrome扩展规范
- **实时网络分析** - 集成Chrome Debugger API进行网络请求分析
- **智能表单识别** - 自动识别和适配MDAC官网表单结构
- **安全权限管理** - 最小化权限原则，确保用户数据安全
- **响应式设计** - 适配不同屏幕尺寸的设备

## 📦 安装方法

### 开发者模式安装
1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本项目的 `chrome-extension` 文件夹
5. 扩展安装完成，图标会出现在浏览器工具栏

### 配置API密钥
1. 点击扩展图标，选择"设置"
2. 在"AI配置"标签页中输入您的Gemini API密钥
3. 点击"测试AI连接"验证配置
4. 保存设置

## 🎯 使用指南

### 快速开始
1. **打开MDAC网站** - 访问 https://imigresen-online.imi.gov.my/mdac/main?registerMain
2. **点击扩展图标** - 在浏览器工具栏点击MDAC AI图标
3. **选择填充模式** - 选择AI智能模式、模板填充或API直接提交
4. **一键填充** - 点击"一键智能填充"按钮开始自动填写
5. **验证提交** - AI会自动验证数据并提供优化建议

### 高级功能
- **表单编辑器** - 点击"打开表单编辑器"进入完整的表单编辑界面
- **模板管理** - 在设置页面保存和管理多个个人信息模板
- **网络分析** - 使用API分析功能研究MDAC网站的接口结构
- **数据导入导出** - 备份和恢复您的设置和模板数据

## 🛠️ 技术架构

### 文件结构
```
chrome-extension/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面
├── popup.css              # 弹窗样式
├── popup.js               # 弹窗逻辑
├── background.js          # 后台服务脚本
├── content-script.js      # 内容脚本
├── content-styles.css     # 内容脚本样式
├── options.html           # 设置页面
├── options.css            # 设置页面样式
├── options.js             # 设置页面逻辑
├── form-editor.html       # 表单编辑器
├── form-editor.css        # 表单编辑器样式
├── form-editor.js         # 表单编辑器逻辑
├── icons/                 # 扩展图标
└── README.md             # 说明文档
```

### 核心组件
- **Popup界面** - 扩展的主要交互界面，提供快速操作和状态显示
- **Background Service** - 后台服务处理API调用、数据存储和消息传递
- **Content Script** - 注入到MDAC网站的脚本，处理表单填充和页面交互
- **Options页面** - 完整的设置和配置管理界面
- **Form Editor** - 独立的表单编辑器，提供完整的表单编辑功能

### 权限说明
- `activeTab` - 访问当前活动标签页，用于表单填充
- `storage` - 本地数据存储，保存设置和模板
- `debugger` - 网络请求分析，用于API研究
- `scripting` - 脚本注入，用于页面交互
- `tabs` - 标签页管理，用于打开新页面
- `background` - 后台服务，处理长期运行的任务

## 🔒 隐私与安全

### 数据保护
- **本地存储** - 所有个人数据仅存储在用户本地，不上传到任何服务器
- **数据加密** - 敏感信息使用加密算法保护
- **最小权限** - 仅请求必要的浏览器权限
- **透明操作** - 所有操作都有详细的日志记录

### 安全特性
- **HTTPS通信** - 所有网络请求使用HTTPS加密
- **API密钥保护** - Gemini API密钥本地加密存储
- **会话隔离** - 每个标签页的操作相互独立
- **安全验证** - 多层数据验证确保操作安全

## ⚠️ 重要提醒

### 使用须知
1. **合规使用** - 请遵守MDAC官网的使用条款和当地法律法规
2. **数据准确性** - 请确保填写的信息真实准确，AI建议仅供参考
3. **实验性功能** - API直接提交功能为实验性质，建议优先使用官方网站
4. **备份数据** - 定期导出备份您的模板和设置数据

### 技术限制
- **网站更新** - MDAC官网更新可能影响扩展功能，我们会及时适配
- **浏览器兼容** - 仅支持Chrome浏览器及基于Chromium的浏览器
- **网络依赖** - AI功能需要稳定的网络连接访问Gemini API

## 🆘 故障排除

### 常见问题
1. **扩展无法加载** - 检查是否开启开发者模式，重新加载扩展
2. **AI功能不可用** - 验证Gemini API密钥是否正确配置
3. **表单填充失败** - 确认当前页面为MDAC官网，刷新页面重试
4. **数据丢失** - 检查浏览器存储权限，恢复备份数据

### 调试模式
1. 在设置中开启"调试模式"
2. 打开浏览器开发者工具查看控制台日志
3. 检查扩展的后台页面错误信息
4. 联系开发者获取技术支持

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- **GitHub Issues** - 提交bug报告和功能请求
- **邮箱支持** - 发送详细问题描述
- **在线文档** - 查看完整的使用指南和API文档

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**MDAC AI智能填充工具** - 让入境卡填写变得简单智能 🚀
