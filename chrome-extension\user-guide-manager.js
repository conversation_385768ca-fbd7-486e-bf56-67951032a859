/**
 * MDAC AI智能分析工具 - 用户操作引导系统
 * 提供新手引导、操作提示和交互演示功能
 */

class UserGuideManager {
    constructor() {
        // 引导步骤定义
        this.guideSteps = {
            // 首次使用引导
            firstTime: [
                {
                    id: 'welcome',
                    target: 'body',
                    title: '欢迎使用MDAC AI智能分析工具！',
                    content: '这是一个强大的AI驱动的表单填充助手，让我们开始快速导览。',
                    position: 'center',
                    showSkip: true,
                    showPrevious: false,
                    showNext: true,
                    nextText: '开始导览',
                    icon: '👋'
                },
                {
                    id: 'extension-icon',
                    target: '.mdac-ai-assistant',
                    title: 'AI助手面板',
                    content: '这是您的AI助手控制面板，点击可以展开更多功能。',
                    position: 'bottom',
                    showSkip: true,
                    showPrevious: true,
                    showNext: true,
                    icon: '🤖'
                },
                {
                    id: 'content-input',
                    target: '#contentInput',
                    title: '智能内容解析',
                    content: '在这里粘贴您的个人信息（邮件、文档等），AI会自动提取表单所需数据。',
                    position: 'top',
                    showSkip: true,
                    showPrevious: true,
                    showNext: true,
                    icon: '📝'
                },
                {
                    id: 'parse-button',
                    target: '#parseContentBtn',
                    title: '开始AI解析',
                    content: '点击此按钮，AI将分析您的内容并提取结构化数据。',
                    position: 'top',
                    showSkip: true,
                    showPrevious: true,
                    showNext: true,
                    icon: '🔍'
                },
                {
                    id: 'preview-fill',
                    target: '#previewAndFillBtn',
                    title: '预览并填充',
                    content: '解析完成后，您可以预览数据、进行编辑，然后一键填充表单。',
                    position: 'top',
                    showSkip: true,
                    showPrevious: true,
                    showNext: true,
                    icon: '🎯'
                },
                {
                    id: 'complete',
                    target: 'body',
                    title: '导览完成！',
                    content: '您已经了解了基本操作流程。现在可以开始使用AI助手来快速填充MDAC表单了！',
                    position: 'center',
                    showSkip: false,
                    showPrevious: true,
                    showNext: false,
                    nextText: '开始使用',
                    icon: '🎉'
                }
            ],
            
            // 表单填充引导
            formFilling: [
                {
                    id: 'form-detected',
                    target: 'form',
                    title: '检测到MDAC表单',
                    content: '系统已自动检测到表单字段，您可以使用AI助手来快速填充。',
                    position: 'top',
                    showSkip: true,
                    showPrevious: false,
                    showNext: true,
                    icon: '📋'
                },
                {
                    id: 'open-assistant',
                    target: '.mdac-ai-assistant',
                    title: '打开AI助手',
                    content: '点击这里打开AI助手面板，开始智能填充流程。',
                    position: 'bottom',
                    showSkip: true,
                    showPrevious: true,
                    showNext: true,
                    icon: '🚀'
                }
            ],
            
            // 数据预览引导
            dataPreview: [
                {
                    id: 'preview-intro',
                    target: '.preview-container',
                    title: '数据预览界面',
                    content: '这里显示了AI解析的数据，您可以检查和编辑后再填充表单。',
                    position: 'top',
                    showSkip: true,
                    showPrevious: false,
                    showNext: true,
                    icon: '👀'
                },
                {
                    id: 'edit-mode',
                    target: '.summary-btn',
                    title: '编辑模式',
                    content: '点击"编辑模式"可以修改任何字段的值。',
                    position: 'bottom',
                    showSkip: true,
                    showPrevious: true,
                    showNext: true,
                    icon: '✏️'
                },
                {
                    id: 'confirm-fill',
                    target: '.preview-btn.primary',
                    title: '确认填充',
                    content: '检查无误后，点击"确认填充"开始自动填充表单。',
                    position: 'top',
                    showSkip: true,
                    showPrevious: true,
                    showNext: false,
                    icon: '✅'
                }
            ]
        };
        
        // 提示气泡配置
        this.tooltips = {
            smartParsing: {
                target: '#parseContentBtn',
                title: '智能解析',
                content: '支持多种格式：邮件、PDF文本、聊天记录等',
                trigger: 'hover',
                position: 'top'
            },
            dataPreview: {
                target: '#previewAndFillBtn',
                title: '数据预览',
                content: '填充前可以预览和编辑所有数据',
                trigger: 'hover',
                position: 'top'
            },
            fieldDetection: {
                target: '.mdac-detection-status',
                title: '智能字段检测',
                content: '自动检测表单字段，适应网站更新',
                trigger: 'hover',
                position: 'left'
            }
        };
        
        // 引导状态
        this.currentGuide = null;
        this.currentStep = 0;
        this.isGuideActive = false;
        this.userPreferences = {
            showTooltips: true,
            autoStartGuide: true,
            skipAnimations: false
        };
        
        // 统计信息
        this.stats = {
            guidesCompleted: 0,
            guidesSkipped: 0,
            stepsCompleted: 0,
            averageCompletionTime: 0
        };
        
        this.init();
    }
    
    /**
     * 初始化引导管理器
     */
    async init() {
        try {
            // 加载用户偏好设置
            await this.loadUserPreferences();
            
            // 加载统计信息
            await this.loadStats();
            
            // 检查是否为首次使用
            await this.checkFirstTimeUser();
            
            // 设置提示气泡
            this.setupTooltips();
            
            console.log('✅ 用户引导管理器初始化完成');
        } catch (error) {
            console.error('❌ 用户引导管理器初始化失败:', error);
        }
    }
    
    /**
     * 检查是否为首次使用
     */
    async checkFirstTimeUser() {
        try {
            const result = await chrome.storage.local.get(['hasUsedBefore']);
            
            if (!result.hasUsedBefore && this.userPreferences.autoStartGuide) {
                // 延迟启动首次引导，确保页面完全加载
                setTimeout(() => {
                    this.startGuide('firstTime');
                }, 2000);
            }
        } catch (error) {
            console.error('检查首次使用状态失败:', error);
        }
    }
    
    /**
     * 开始引导
     * @param {string} guideType 引导类型
     */
    startGuide(guideType) {
        if (this.isGuideActive) {
            console.log('引导已在进行中');
            return;
        }
        
        const steps = this.guideSteps[guideType];
        if (!steps || steps.length === 0) {
            console.error('未找到引导步骤:', guideType);
            return;
        }
        
        this.currentGuide = guideType;
        this.currentStep = 0;
        this.isGuideActive = true;
        this.guideStartTime = Date.now();
        
        console.log(`🎯 开始引导: ${guideType}`);
        
        // 添加引导样式
        this.addGuideStyles();
        
        // 显示第一步
        this.showStep(0);
    }
    
    /**
     * 显示引导步骤
     * @param {number} stepIndex 步骤索引
     */
    showStep(stepIndex) {
        const steps = this.guideSteps[this.currentGuide];
        if (!steps || stepIndex >= steps.length || stepIndex < 0) {
            return;
        }
        
        const step = steps[stepIndex];
        this.currentStep = stepIndex;
        
        // 移除现有的引导元素
        this.removeGuideElements();
        
        // 创建引导覆盖层
        this.createGuideOverlay();
        
        // 高亮目标元素
        this.highlightTarget(step.target);
        
        // 显示引导气泡
        this.showGuideBubble(step);
        
        // 更新统计
        this.stats.stepsCompleted++;
        
        console.log(`📍 显示步骤 ${stepIndex + 1}/${steps.length}: ${step.title}`);
    }
    
    /**
     * 创建引导覆盖层
     */
    createGuideOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'mdac-guide-overlay';
        overlay.className = 'mdac-guide-overlay';
        
        // 点击覆盖层不关闭引导（防止误操作）
        overlay.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
        });
        
        document.body.appendChild(overlay);
    }
    
    /**
     * 高亮目标元素
     * @param {string} target 目标选择器
     */
    highlightTarget(target) {
        let targetElement;
        
        if (target === 'body') {
            // 中心显示，不高亮特定元素
            return;
        }
        
        targetElement = document.querySelector(target);
        if (!targetElement) {
            console.warn('未找到目标元素:', target);
            return;
        }
        
        // 创建高亮框
        const highlight = document.createElement('div');
        highlight.id = 'mdac-guide-highlight';
        highlight.className = 'mdac-guide-highlight';
        
        // 获取元素位置
        const rect = targetElement.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        // 设置高亮框位置
        highlight.style.position = 'absolute';
        highlight.style.top = (rect.top + scrollTop - 4) + 'px';
        highlight.style.left = (rect.left + scrollLeft - 4) + 'px';
        highlight.style.width = (rect.width + 8) + 'px';
        highlight.style.height = (rect.height + 8) + 'px';
        highlight.style.zIndex = '10004';
        
        document.body.appendChild(highlight);
        
        // 滚动到目标元素
        targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'center'
        });
    }
    
    /**
     * 显示引导气泡
     * @param {Object} step 步骤配置
     */
    showGuideBubble(step) {
        const bubble = document.createElement('div');
        bubble.id = 'mdac-guide-bubble';
        bubble.className = 'mdac-guide-bubble';
        
        const steps = this.guideSteps[this.currentGuide];
        const progress = ((this.currentStep + 1) / steps.length * 100).toFixed(0);
        
        bubble.innerHTML = `
            <div class="guide-bubble-header">
                <div class="guide-bubble-icon">${step.icon}</div>
                <div class="guide-bubble-progress">
                    <span class="progress-text">${this.currentStep + 1} / ${steps.length}</span>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                </div>
                ${step.showSkip ? '<button class="guide-skip-btn" onclick="window.userGuideManager.skipGuide()">跳过</button>' : ''}
            </div>
            
            <div class="guide-bubble-content">
                <h3 class="guide-bubble-title">${step.title}</h3>
                <p class="guide-bubble-text">${step.content}</p>
            </div>
            
            <div class="guide-bubble-actions">
                ${step.showPrevious ? '<button class="guide-btn secondary" onclick="window.userGuideManager.previousStep()">上一步</button>' : ''}
                ${step.showNext ? `<button class="guide-btn primary" onclick="window.userGuideManager.nextStep()">${step.nextText || '下一步'}</button>` : ''}
                ${!step.showNext ? '<button class="guide-btn primary" onclick="window.userGuideManager.completeGuide()">完成</button>' : ''}
            </div>
        `;
        
        // 设置气泡位置
        this.positionBubble(bubble, step);
        
        document.body.appendChild(bubble);
        
        // 暴露到全局
        window.userGuideManager = this;
        
        // 添加动画效果
        setTimeout(() => {
            bubble.classList.add('show');
        }, 100);
    }
    
    /**
     * 设置气泡位置
     * @param {Element} bubble 气泡元素
     * @param {Object} step 步骤配置
     */
    positionBubble(bubble, step) {
        if (step.position === 'center') {
            bubble.style.position = 'fixed';
            bubble.style.top = '50%';
            bubble.style.left = '50%';
            bubble.style.transform = 'translate(-50%, -50%)';
            bubble.style.zIndex = '10005';
            return;
        }
        
        const targetElement = document.querySelector(step.target);
        if (!targetElement) {
            // 降级到中心显示
            bubble.style.position = 'fixed';
            bubble.style.top = '50%';
            bubble.style.left = '50%';
            bubble.style.transform = 'translate(-50%, -50%)';
            bubble.style.zIndex = '10005';
            return;
        }
        
        const rect = targetElement.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        bubble.style.position = 'absolute';
        bubble.style.zIndex = '10005';
        
        switch (step.position) {
            case 'top':
                bubble.style.top = (rect.top + scrollTop - 10) + 'px';
                bubble.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                bubble.style.transform = 'translate(-50%, -100%)';
                bubble.classList.add('position-top');
                break;
                
            case 'bottom':
                bubble.style.top = (rect.bottom + scrollTop + 10) + 'px';
                bubble.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                bubble.style.transform = 'translate(-50%, 0)';
                bubble.classList.add('position-bottom');
                break;
                
            case 'left':
                bubble.style.top = (rect.top + scrollTop + rect.height / 2) + 'px';
                bubble.style.left = (rect.left + scrollLeft - 10) + 'px';
                bubble.style.transform = 'translate(-100%, -50%)';
                bubble.classList.add('position-left');
                break;
                
            case 'right':
                bubble.style.top = (rect.top + scrollTop + rect.height / 2) + 'px';
                bubble.style.left = (rect.right + scrollLeft + 10) + 'px';
                bubble.style.transform = 'translate(0, -50%)';
                bubble.classList.add('position-right');
                break;
                
            default:
                bubble.style.top = (rect.bottom + scrollTop + 10) + 'px';
                bubble.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                bubble.style.transform = 'translate(-50%, 0)';
                bubble.classList.add('position-bottom');
        }
    }
    
    /**
     * 下一步
     */
    nextStep() {
        const steps = this.guideSteps[this.currentGuide];
        if (this.currentStep < steps.length - 1) {
            this.showStep(this.currentStep + 1);
        } else {
            this.completeGuide();
        }
    }
    
    /**
     * 上一步
     */
    previousStep() {
        if (this.currentStep > 0) {
            this.showStep(this.currentStep - 1);
        }
    }
    
    /**
     * 跳过引导
     */
    skipGuide() {
        this.stats.guidesSkipped++;
        this.endGuide();
        console.log('⏭️ 用户跳过引导');
    }
    
    /**
     * 完成引导
     */
    completeGuide() {
        this.stats.guidesCompleted++;
        
        // 计算完成时间
        if (this.guideStartTime) {
            const completionTime = Date.now() - this.guideStartTime;
            this.updateAverageCompletionTime(completionTime);
        }
        
        // 标记用户已使用过
        if (this.currentGuide === 'firstTime') {
            chrome.storage.local.set({ hasUsedBefore: true });
        }
        
        this.endGuide();
        console.log('✅ 引导完成');
    }
    
    /**
     * 结束引导
     */
    endGuide() {
        this.removeGuideElements();
        this.isGuideActive = false;
        this.currentGuide = null;
        this.currentStep = 0;
        
        // 保存统计信息
        this.saveStats();
        
        // 清理全局引用
        if (window.userGuideManager === this) {
            delete window.userGuideManager;
        }
    }
    
    /**
     * 移除引导元素
     */
    removeGuideElements() {
        const overlay = document.getElementById('mdac-guide-overlay');
        const highlight = document.getElementById('mdac-guide-highlight');
        const bubble = document.getElementById('mdac-guide-bubble');

        if (overlay) overlay.remove();
        if (highlight) highlight.remove();
        if (bubble) bubble.remove();
    }

    /**
     * 设置提示气泡
     */
    setupTooltips() {
        if (!this.userPreferences.showTooltips) return;

        for (const [key, tooltip] of Object.entries(this.tooltips)) {
            this.createTooltip(tooltip);
        }
    }

    /**
     * 创建提示气泡
     * @param {Object} tooltip 提示配置
     */
    createTooltip(tooltip) {
        const targetElement = document.querySelector(tooltip.target);
        if (!targetElement) return;

        let tooltipElement = null;
        let showTimeout = null;
        let hideTimeout = null;

        const showTooltip = () => {
            if (tooltipElement) return;

            clearTimeout(hideTimeout);
            showTimeout = setTimeout(() => {
                tooltipElement = document.createElement('div');
                tooltipElement.className = 'mdac-tooltip';
                tooltipElement.innerHTML = `
                    <div class="tooltip-title">${tooltip.title}</div>
                    <div class="tooltip-content">${tooltip.content}</div>
                `;

                // 设置位置
                this.positionTooltip(tooltipElement, targetElement, tooltip.position);

                document.body.appendChild(tooltipElement);

                // 添加显示动画
                setTimeout(() => {
                    tooltipElement.classList.add('show');
                }, 10);
            }, 500);
        };

        const hideTooltip = () => {
            clearTimeout(showTimeout);
            if (tooltipElement) {
                hideTimeout = setTimeout(() => {
                    if (tooltipElement) {
                        tooltipElement.remove();
                        tooltipElement = null;
                    }
                }, 200);
            }
        };

        // 绑定事件
        if (tooltip.trigger === 'hover') {
            targetElement.addEventListener('mouseenter', showTooltip);
            targetElement.addEventListener('mouseleave', hideTooltip);
        } else if (tooltip.trigger === 'click') {
            targetElement.addEventListener('click', showTooltip);
            document.addEventListener('click', (e) => {
                if (!targetElement.contains(e.target) && !tooltipElement?.contains(e.target)) {
                    hideTooltip();
                }
            });
        }
    }

    /**
     * 设置提示气泡位置
     * @param {Element} tooltip 提示元素
     * @param {Element} target 目标元素
     * @param {string} position 位置
     */
    positionTooltip(tooltip, target, position) {
        const rect = target.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        tooltip.style.position = 'absolute';
        tooltip.style.zIndex = '10001';

        switch (position) {
            case 'top':
                tooltip.style.top = (rect.top + scrollTop - 10) + 'px';
                tooltip.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                tooltip.style.transform = 'translate(-50%, -100%)';
                break;

            case 'bottom':
                tooltip.style.top = (rect.bottom + scrollTop + 10) + 'px';
                tooltip.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                tooltip.style.transform = 'translate(-50%, 0)';
                break;

            case 'left':
                tooltip.style.top = (rect.top + scrollTop + rect.height / 2) + 'px';
                tooltip.style.left = (rect.left + scrollLeft - 10) + 'px';
                tooltip.style.transform = 'translate(-100%, -50%)';
                break;

            case 'right':
                tooltip.style.top = (rect.top + scrollTop + rect.height / 2) + 'px';
                tooltip.style.left = (rect.right + scrollLeft + 10) + 'px';
                tooltip.style.transform = 'translate(0, -50%)';
                break;

            default:
                tooltip.style.top = (rect.bottom + scrollTop + 10) + 'px';
                tooltip.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                tooltip.style.transform = 'translate(-50%, 0)';
        }
    }

    /**
     * 显示快速提示
     * @param {string} message 提示消息
     * @param {Element} target 目标元素
     * @param {number} duration 显示时长
     */
    showQuickTip(message, target, duration = 3000) {
        const tip = document.createElement('div');
        tip.className = 'mdac-quick-tip';
        tip.textContent = message;

        if (target) {
            this.positionTooltip(tip, target, 'top');
        } else {
            tip.style.position = 'fixed';
            tip.style.top = '20px';
            tip.style.right = '20px';
            tip.style.zIndex = '10001';
        }

        document.body.appendChild(tip);

        // 显示动画
        setTimeout(() => {
            tip.classList.add('show');
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            tip.classList.remove('show');
            setTimeout(() => {
                tip.remove();
            }, 300);
        }, duration);
    }

    /**
     * 触发特定场景的引导
     * @param {string} scenario 场景名称
     */
    triggerScenarioGuide(scenario) {
        switch (scenario) {
            case 'formDetected':
                if (!this.hasSeenGuide('formFilling')) {
                    this.startGuide('formFilling');
                    this.markGuideAsSeen('formFilling');
                }
                break;

            case 'dataPreviewShown':
                if (!this.hasSeenGuide('dataPreview')) {
                    this.startGuide('dataPreview');
                    this.markGuideAsSeen('dataPreview');
                }
                break;

            case 'firstParseSuccess':
                this.showQuickTip('🎉 AI解析成功！现在可以预览和编辑数据了。');
                break;

            case 'firstFillSuccess':
                this.showQuickTip('✅ 表单填充完成！您可以检查并提交表单。');
                break;
        }
    }

    /**
     * 检查是否已看过引导
     * @param {string} guideType 引导类型
     * @returns {boolean}
     */
    hasSeenGuide(guideType) {
        const seenGuides = JSON.parse(localStorage.getItem('mdac-seen-guides') || '[]');
        return seenGuides.includes(guideType);
    }

    /**
     * 标记引导为已看过
     * @param {string} guideType 引导类型
     */
    markGuideAsSeen(guideType) {
        const seenGuides = JSON.parse(localStorage.getItem('mdac-seen-guides') || '[]');
        if (!seenGuides.includes(guideType)) {
            seenGuides.push(guideType);
            localStorage.setItem('mdac-seen-guides', JSON.stringify(seenGuides));
        }
    }

    /**
     * 加载用户偏好设置
     */
    async loadUserPreferences() {
        try {
            const result = await chrome.storage.sync.get(['userGuidePreferences']);
            if (result.userGuidePreferences) {
                this.userPreferences = { ...this.userPreferences, ...result.userGuidePreferences };
            }
        } catch (error) {
            console.error('加载用户偏好设置失败:', error);
        }
    }

    /**
     * 保存用户偏好设置
     */
    async saveUserPreferences() {
        try {
            await chrome.storage.sync.set({
                userGuidePreferences: this.userPreferences
            });
        } catch (error) {
            console.error('保存用户偏好设置失败:', error);
        }
    }

    /**
     * 加载统计信息
     */
    async loadStats() {
        try {
            const result = await chrome.storage.local.get(['userGuideStats']);
            if (result.userGuideStats) {
                this.stats = { ...this.stats, ...result.userGuideStats };
            }
        } catch (error) {
            console.error('加载统计信息失败:', error);
        }
    }

    /**
     * 保存统计信息
     */
    async saveStats() {
        try {
            await chrome.storage.local.set({
                userGuideStats: this.stats
            });
        } catch (error) {
            console.error('保存统计信息失败:', error);
        }
    }

    /**
     * 更新平均完成时间
     * @param {number} completionTime 完成时间（毫秒）
     */
    updateAverageCompletionTime(completionTime) {
        const totalGuides = this.stats.guidesCompleted;
        if (totalGuides === 1) {
            this.stats.averageCompletionTime = completionTime;
        } else {
            this.stats.averageCompletionTime =
                (this.stats.averageCompletionTime * (totalGuides - 1) + completionTime) / totalGuides;
        }
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            completionRate: this.stats.guidesCompleted + this.stats.guidesSkipped > 0
                ? (this.stats.guidesCompleted / (this.stats.guidesCompleted + this.stats.guidesSkipped) * 100).toFixed(2)
                : 0,
            averageCompletionTimeFormatted: this.formatTime(this.stats.averageCompletionTime)
        };
    }

    /**
     * 格式化时间
     * @param {number} milliseconds 毫秒
     * @returns {string} 格式化的时间字符串
     */
    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);

        if (minutes > 0) {
            return `${minutes}分${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 重置引导状态
     */
    async resetGuideState() {
        localStorage.removeItem('mdac-seen-guides');
        await chrome.storage.local.remove(['hasUsedBefore']);
        console.log('🔄 引导状态已重置');
    }

    /**
     * 添加引导样式
     */
    addGuideStyles() {
        if (document.getElementById('mdac-guide-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-guide-styles';
        styles.textContent = `
            .mdac-guide-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                z-index: 10003;
                backdrop-filter: blur(2px);
            }

            .mdac-guide-highlight {
                border: 3px solid #007bff;
                border-radius: 8px;
                box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.3), 0 0 20px rgba(0, 123, 255, 0.5);
                background: rgba(255, 255, 255, 0.1);
                animation: guideHighlight 2s ease-in-out infinite;
            }

            @keyframes guideHighlight {
                0%, 100% {
                    box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.3), 0 0 20px rgba(0, 123, 255, 0.5);
                }
                50% {
                    box-shadow: 0 0 0 8px rgba(0, 123, 255, 0.5), 0 0 30px rgba(0, 123, 255, 0.7);
                }
            }

            .mdac-guide-bubble {
                position: absolute;
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                max-width: 350px;
                min-width: 280px;
                opacity: 0;
                transform: scale(0.8);
                transition: all 0.3s ease-out;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                z-index: 10005;
            }

            .mdac-guide-bubble.show {
                opacity: 1;
                transform: scale(1);
            }

            .mdac-guide-bubble::before {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border: 8px solid transparent;
            }

            .mdac-guide-bubble.position-top::before {
                bottom: -16px;
                left: 50%;
                transform: translateX(-50%);
                border-top-color: white;
            }

            .mdac-guide-bubble.position-bottom::before {
                top: -16px;
                left: 50%;
                transform: translateX(-50%);
                border-bottom-color: white;
            }

            .mdac-guide-bubble.position-left::before {
                right: -16px;
                top: 50%;
                transform: translateY(-50%);
                border-left-color: white;
            }

            .mdac-guide-bubble.position-right::before {
                left: -16px;
                top: 50%;
                transform: translateY(-50%);
                border-right-color: white;
            }

            .guide-bubble-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px 12px 20px;
                border-bottom: 1px solid #e9ecef;
            }

            .guide-bubble-icon {
                font-size: 24px;
                margin-right: 12px;
            }

            .guide-bubble-progress {
                flex: 1;
                margin-right: 12px;
            }

            .progress-text {
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 4px;
                display: block;
            }

            .progress-bar {
                width: 100%;
                height: 4px;
                background: #e9ecef;
                border-radius: 2px;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #0056b3);
                border-radius: 2px;
                transition: width 0.3s ease;
            }

            .guide-skip-btn {
                background: none;
                border: none;
                color: #6c757d;
                font-size: 12px;
                cursor: pointer;
                padding: 4px 8px;
                border-radius: 4px;
                transition: all 0.2s ease;
            }

            .guide-skip-btn:hover {
                background: #f8f9fa;
                color: #495057;
            }

            .guide-bubble-content {
                padding: 16px 20px;
            }

            .guide-bubble-title {
                margin: 0 0 8px 0;
                font-size: 16px;
                font-weight: 600;
                color: #333;
                line-height: 1.3;
            }

            .guide-bubble-text {
                margin: 0;
                font-size: 14px;
                color: #666;
                line-height: 1.5;
            }

            .guide-bubble-actions {
                display: flex;
                gap: 8px;
                padding: 12px 20px 16px 20px;
                justify-content: flex-end;
            }

            .guide-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 70px;
            }

            .guide-btn.primary {
                background: #007bff;
                color: white;
            }

            .guide-btn.primary:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .guide-btn.secondary {
                background: #6c757d;
                color: white;
            }

            .guide-btn.secondary:hover {
                background: #545b62;
            }

            .mdac-tooltip {
                position: absolute;
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                max-width: 200px;
                opacity: 0;
                transform: scale(0.8);
                transition: all 0.2s ease;
                z-index: 10001;
                pointer-events: none;
            }

            .mdac-tooltip.show {
                opacity: 1;
                transform: scale(1);
            }

            .tooltip-title {
                font-weight: 600;
                margin-bottom: 2px;
            }

            .tooltip-content {
                font-size: 11px;
                opacity: 0.9;
            }

            .mdac-quick-tip {
                position: fixed;
                background: #28a745;
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                opacity: 0;
                transform: translateY(-10px);
                transition: all 0.3s ease;
                z-index: 10001;
                max-width: 300px;
            }

            .mdac-quick-tip.show {
                opacity: 1;
                transform: translateY(0);
            }

            @media (max-width: 768px) {
                .mdac-guide-bubble {
                    max-width: 90vw;
                    min-width: 280px;
                }

                .guide-bubble-header {
                    padding: 12px 16px 8px 16px;
                }

                .guide-bubble-content {
                    padding: 12px 16px;
                }

                .guide-bubble-actions {
                    padding: 8px 16px 12px 16px;
                }

                .guide-bubble-title {
                    font-size: 15px;
                }

                .guide-bubble-text {
                    font-size: 13px;
                }
            }
        `;

        document.head.appendChild(styles);
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserGuideManager;
} else {
    window.UserGuideManager = UserGuideManager;
}
