/**
 * MDAC AI智能分析工具 - 填充成功率统计和反馈系统
 * 实时监控填充过程，统计成功率，收集用户反馈
 */

class FillMonitor {
    constructor() {
        // 填充状态定义
        this.fillStates = {
            PENDING: 'pending',         // 等待填充
            IN_PROGRESS: 'in_progress', // 填充中
            SUCCESS: 'success',         // 成功
            FAILED: 'failed',           // 失败
            SKIPPED: 'skipped',         // 跳过
            PARTIAL: 'partial'          // 部分成功
        };
        
        // 字段类型权重（用于计算成功率）
        this.fieldWeights = {
            // 必填字段权重更高
            name: 10,
            passportNo: 10,
            email: 10,
            mobileNo: 10,
            arrivalDate: 10,
            departureDate: 10,
            
            // 重要字段
            dateOfBirth: 8,
            nationality: 8,
            sex: 8,
            passportExpiry: 8,
            accommodation: 8,
            address: 8,
            
            // 一般字段
            confirmEmail: 6,
            countryCode: 6,
            flightNo: 6,
            modeOfTravel: 6,
            lastPort: 6,
            state: 6,
            city: 6,
            postcode: 6,
            
            // 可选字段
            address2: 3
        };
        
        // 当前填充会话
        this.currentSession = null;
        
        // 填充历史记录
        this.fillHistory = [];
        this.maxHistorySize = 100;
        
        // 统计数据
        this.stats = {
            totalSessions: 0,
            successfulSessions: 0,
            partialSessions: 0,
            failedSessions: 0,
            totalFields: 0,
            successfulFields: 0,
            failedFields: 0,
            averageFillTime: 0,
            averageSuccessRate: 0,
            lastUpdated: Date.now()
        };
        
        // 用户反馈数据
        this.feedbackData = {
            ratings: [],
            comments: [],
            suggestions: [],
            bugReports: []
        };
        
        // 实时监控配置
        this.monitorConfig = {
            updateInterval: 100,        // 更新间隔（毫秒）
            timeoutThreshold: 5000,     // 超时阈值（毫秒）
            retryAttempts: 3,           // 重试次数
            enableRealTimeUpdates: true // 启用实时更新
        };
        
        // 监控状态
        this.isMonitoring = false;
        this.monitorInterval = null;
        this.progressCallback = null;
        
        this.init();
    }
    
    /**
     * 初始化填充监控器
     */
    async init() {
        try {
            // 加载历史数据
            await this.loadHistoryData();
            
            // 加载统计数据
            await this.loadStats();
            
            // 加载反馈数据
            await this.loadFeedbackData();
            
            console.log('✅ 填充监控器初始化完成');
        } catch (error) {
            console.error('❌ 填充监控器初始化失败:', error);
        }
    }
    
    /**
     * 开始填充会话
     * @param {Object} data 要填充的数据
     * @param {Object} fields 目标字段映射
     * @param {Function} progressCallback 进度回调函数
     */
    startFillSession(data, fields, progressCallback = null) {
        // 创建新的填充会话
        this.currentSession = {
            id: this.generateSessionId(),
            startTime: Date.now(),
            endTime: null,
            data: JSON.parse(JSON.stringify(data)),
            fields: JSON.parse(JSON.stringify(fields)),
            fieldResults: {},
            totalFields: Object.keys(data).length,
            successfulFields: 0,
            failedFields: 0,
            skippedFields: 0,
            status: this.fillStates.IN_PROGRESS,
            errors: [],
            warnings: [],
            retryAttempts: 0
        };
        
        // 初始化字段结果
        for (const fieldKey of Object.keys(data)) {
            this.currentSession.fieldResults[fieldKey] = {
                status: this.fillStates.PENDING,
                value: data[fieldKey],
                element: fields[fieldKey] || null,
                attempts: 0,
                errors: [],
                startTime: null,
                endTime: null,
                weight: this.fieldWeights[fieldKey] || 1
            };
        }
        
        // 设置进度回调
        this.progressCallback = progressCallback;
        
        // 开始监控
        this.startMonitoring();
        
        // 更新统计
        this.stats.totalSessions++;
        
        console.log(`🚀 开始填充会话: ${this.currentSession.id}`);
        console.log(`📊 总字段数: ${this.currentSession.totalFields}`);
        
        return this.currentSession.id;
    }
    
    /**
     * 开始监控填充字段
     * @param {string} fieldKey 字段键
     */
    startFieldFill(fieldKey) {
        if (!this.currentSession || !this.currentSession.fieldResults[fieldKey]) {
            console.warn('无效的字段或会话:', fieldKey);
            return;
        }
        
        const fieldResult = this.currentSession.fieldResults[fieldKey];
        fieldResult.status = this.fillStates.IN_PROGRESS;
        fieldResult.startTime = Date.now();
        fieldResult.attempts++;
        
        console.log(`📝 开始填充字段: ${fieldKey}`);
        
        // 触发进度更新
        this.updateProgress();
    }
    
    /**
     * 标记字段填充成功
     * @param {string} fieldKey 字段键
     * @param {string} actualValue 实际填充的值
     */
    markFieldSuccess(fieldKey, actualValue = null) {
        if (!this.currentSession || !this.currentSession.fieldResults[fieldKey]) {
            console.warn('无效的字段或会话:', fieldKey);
            return;
        }
        
        const fieldResult = this.currentSession.fieldResults[fieldKey];
        fieldResult.status = this.fillStates.SUCCESS;
        fieldResult.endTime = Date.now();
        
        if (actualValue !== null) {
            fieldResult.actualValue = actualValue;
        }
        
        // 更新会话统计
        this.currentSession.successfulFields++;
        this.stats.totalFields++;
        this.stats.successfulFields++;
        
        console.log(`✅ 字段填充成功: ${fieldKey}`);
        
        // 触发进度更新
        this.updateProgress();
    }
    
    /**
     * 标记字段填充失败
     * @param {string} fieldKey 字段键
     * @param {string} error 错误信息
     */
    markFieldFailure(fieldKey, error) {
        if (!this.currentSession || !this.currentSession.fieldResults[fieldKey]) {
            console.warn('无效的字段或会话:', fieldKey);
            return;
        }
        
        const fieldResult = this.currentSession.fieldResults[fieldKey];
        fieldResult.status = this.fillStates.FAILED;
        fieldResult.endTime = Date.now();
        fieldResult.errors.push({
            message: error,
            timestamp: Date.now()
        });
        
        // 更新会话统计
        this.currentSession.failedFields++;
        this.currentSession.errors.push({
            field: fieldKey,
            error: error,
            timestamp: Date.now()
        });
        
        this.stats.totalFields++;
        this.stats.failedFields++;
        
        console.log(`❌ 字段填充失败: ${fieldKey} - ${error}`);
        
        // 触发进度更新
        this.updateProgress();
    }
    
    /**
     * 跳过字段填充
     * @param {string} fieldKey 字段键
     * @param {string} reason 跳过原因
     */
    skipField(fieldKey, reason = '用户跳过') {
        if (!this.currentSession || !this.currentSession.fieldResults[fieldKey]) {
            console.warn('无效的字段或会话:', fieldKey);
            return;
        }
        
        const fieldResult = this.currentSession.fieldResults[fieldKey];
        fieldResult.status = this.fillStates.SKIPPED;
        fieldResult.endTime = Date.now();
        fieldResult.skipReason = reason;
        
        // 更新会话统计
        this.currentSession.skippedFields++;
        
        console.log(`⏭️ 跳过字段: ${fieldKey} - ${reason}`);
        
        // 触发进度更新
        this.updateProgress();
    }
    
    /**
     * 结束填充会话
     * @param {string} finalStatus 最终状态
     */
    endFillSession(finalStatus = null) {
        if (!this.currentSession) {
            console.warn('没有活动的填充会话');
            return;
        }
        
        this.currentSession.endTime = Date.now();
        
        // 计算最终状态
        if (!finalStatus) {
            const { successfulFields, failedFields, totalFields } = this.currentSession;
            
            if (successfulFields === totalFields) {
                finalStatus = this.fillStates.SUCCESS;
            } else if (successfulFields > 0) {
                finalStatus = this.fillStates.PARTIAL;
            } else {
                finalStatus = this.fillStates.FAILED;
            }
        }
        
        this.currentSession.status = finalStatus;
        
        // 计算填充时间
        const fillTime = this.currentSession.endTime - this.currentSession.startTime;
        this.currentSession.fillTime = fillTime;
        
        // 计算成功率
        const successRate = this.calculateSessionSuccessRate(this.currentSession);
        this.currentSession.successRate = successRate;
        
        // 更新统计
        this.updateSessionStats(this.currentSession);
        
        // 保存到历史记录
        this.addToHistory(this.currentSession);
        
        // 停止监控
        this.stopMonitoring();
        
        console.log(`🏁 填充会话结束: ${this.currentSession.id}`);
        console.log(`📊 成功率: ${successRate.toFixed(2)}%`);
        console.log(`⏱️ 填充时间: ${fillTime}ms`);
        
        // 触发最终进度更新
        this.updateProgress();
        
        // 显示反馈界面
        setTimeout(() => {
            this.showFeedbackInterface();
        }, 1000);
        
        const completedSession = this.currentSession;
        this.currentSession = null;
        
        return completedSession;
    }
    
    /**
     * 开始实时监控
     */
    startMonitoring() {
        if (this.isMonitoring || !this.monitorConfig.enableRealTimeUpdates) {
            return;
        }
        
        this.isMonitoring = true;
        
        this.monitorInterval = setInterval(() => {
            this.checkFieldTimeouts();
            this.updateProgress();
        }, this.monitorConfig.updateInterval);
        
        console.log('📡 开始实时监控');
    }
    
    /**
     * 停止实时监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }
        
        this.isMonitoring = false;
        
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
        }
        
        console.log('📡 停止实时监控');
    }
    
    /**
     * 检查字段超时
     */
    checkFieldTimeouts() {
        if (!this.currentSession) return;
        
        const now = Date.now();
        
        for (const [fieldKey, fieldResult] of Object.entries(this.currentSession.fieldResults)) {
            if (fieldResult.status === this.fillStates.IN_PROGRESS && 
                fieldResult.startTime && 
                (now - fieldResult.startTime) > this.monitorConfig.timeoutThreshold) {
                
                this.markFieldFailure(fieldKey, '填充超时');
            }
        }
    }
    
    /**
     * 更新进度
     */
    updateProgress() {
        if (!this.currentSession || !this.progressCallback) {
            return;
        }
        
        const progress = this.calculateCurrentProgress();
        
        try {
            this.progressCallback(progress);
        } catch (error) {
            console.error('进度回调执行失败:', error);
        }
    }
    
    /**
     * 计算当前进度
     */
    calculateCurrentProgress() {
        if (!this.currentSession) {
            return {
                percentage: 0,
                completed: 0,
                total: 0,
                successful: 0,
                failed: 0,
                skipped: 0,
                inProgress: 0,
                status: 'idle'
            };
        }
        
        const { fieldResults, totalFields } = this.currentSession;
        let completed = 0;
        let successful = 0;
        let failed = 0;
        let skipped = 0;
        let inProgress = 0;
        
        for (const fieldResult of Object.values(fieldResults)) {
            switch (fieldResult.status) {
                case this.fillStates.SUCCESS:
                    completed++;
                    successful++;
                    break;
                case this.fillStates.FAILED:
                    completed++;
                    failed++;
                    break;
                case this.fillStates.SKIPPED:
                    completed++;
                    skipped++;
                    break;
                case this.fillStates.IN_PROGRESS:
                    inProgress++;
                    break;
            }
        }
        
        const percentage = totalFields > 0 ? (completed / totalFields * 100) : 0;
        
        return {
            percentage: Math.round(percentage),
            completed,
            total: totalFields,
            successful,
            failed,
            skipped,
            inProgress,
            status: this.currentSession.status,
            sessionId: this.currentSession.id,
            startTime: this.currentSession.startTime,
            currentTime: Date.now()
        };
    }
    
    /**
     * 计算会话成功率
     * @param {Object} session 填充会话
     */
    calculateSessionSuccessRate(session) {
        let totalWeight = 0;
        let successWeight = 0;
        
        for (const [fieldKey, fieldResult] of Object.entries(session.fieldResults)) {
            const weight = fieldResult.weight;
            totalWeight += weight;
            
            if (fieldResult.status === this.fillStates.SUCCESS) {
                successWeight += weight;
            }
        }
        
        return totalWeight > 0 ? (successWeight / totalWeight * 100) : 0;
    }
    
    /**
     * 更新会话统计
     * @param {Object} session 填充会话
     */
    updateSessionStats(session) {
        // 更新会话统计
        switch (session.status) {
            case this.fillStates.SUCCESS:
                this.stats.successfulSessions++;
                break;
            case this.fillStates.PARTIAL:
                this.stats.partialSessions++;
                break;
            case this.fillStates.FAILED:
                this.stats.failedSessions++;
                break;
        }
        
        // 更新平均填充时间
        this.updateAverageFillTime(session.fillTime);
        
        // 更新平均成功率
        this.updateAverageSuccessRate(session.successRate);
        
        // 更新时间戳
        this.stats.lastUpdated = Date.now();
        
        // 保存统计数据
        this.saveStats();
    }
    
    /**
     * 更新平均填充时间
     * @param {number} fillTime 填充时间
     */
    updateAverageFillTime(fillTime) {
        const totalSessions = this.stats.totalSessions;
        if (totalSessions === 1) {
            this.stats.averageFillTime = fillTime;
        } else {
            this.stats.averageFillTime = 
                (this.stats.averageFillTime * (totalSessions - 1) + fillTime) / totalSessions;
        }
    }
    
    /**
     * 更新平均成功率
     * @param {number} successRate 成功率
     */
    updateAverageSuccessRate(successRate) {
        const totalSessions = this.stats.totalSessions;
        if (totalSessions === 1) {
            this.stats.averageSuccessRate = successRate;
        } else {
            this.stats.averageSuccessRate = 
                (this.stats.averageSuccessRate * (totalSessions - 1) + successRate) / totalSessions;
        }
    }
    
    /**
     * 添加到历史记录
     * @param {Object} session 填充会话
     */
    addToHistory(session) {
        // 创建历史记录条目
        const historyEntry = {
            id: session.id,
            timestamp: session.endTime,
            status: session.status,
            successRate: session.successRate,
            fillTime: session.fillTime,
            totalFields: session.totalFields,
            successfulFields: session.successfulFields,
            failedFields: session.failedFields,
            skippedFields: session.skippedFields,
            errors: session.errors.length,
            summary: this.generateSessionSummary(session)
        };
        
        // 添加到历史记录
        this.fillHistory.unshift(historyEntry);
        
        // 限制历史记录大小
        if (this.fillHistory.length > this.maxHistorySize) {
            this.fillHistory = this.fillHistory.slice(0, this.maxHistorySize);
        }
        
        // 保存历史记录
        this.saveHistoryData();
    }
    
    /**
     * 生成会话摘要
     * @param {Object} session 填充会话
     */
    generateSessionSummary(session) {
        const { successfulFields, failedFields, skippedFields, totalFields } = session;
        
        if (successfulFields === totalFields) {
            return '完美填充，所有字段成功';
        } else if (failedFields === 0 && skippedFields > 0) {
            return `成功填充，跳过${skippedFields}个字段`;
        } else if (failedFields > 0 && successfulFields > 0) {
            return `部分成功，${failedFields}个字段失败`;
        } else {
            return '填充失败，需要手动处理';
        }
    }
    
    /**
     * 生成会话ID
     */
    generateSessionId() {
        return 'fill_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 显示反馈界面
     */
    showFeedbackInterface() {
        if (!this.currentSession && this.fillHistory.length === 0) {
            return;
        }

        const lastSession = this.fillHistory[0] || this.currentSession;

        // 移除现有的反馈界面
        this.removeFeedbackInterface();

        const feedbackModal = document.createElement('div');
        feedbackModal.id = 'mdac-feedback-modal';
        feedbackModal.className = 'mdac-feedback-modal';

        const successRate = lastSession.successRate || 0;
        const statusIcon = this.getStatusIcon(lastSession.status);
        const statusColor = this.getStatusColor(lastSession.status);

        feedbackModal.innerHTML = `
            <div class="feedback-overlay" onclick="window.fillMonitor.closeFeedbackInterface()"></div>
            <div class="feedback-container">
                <div class="feedback-header" style="background: ${statusColor}">
                    <div class="feedback-title">
                        <span class="feedback-icon">${statusIcon}</span>
                        <h2>填充完成反馈</h2>
                    </div>
                    <button class="feedback-close" onclick="window.fillMonitor.closeFeedbackInterface()">×</button>
                </div>

                <div class="feedback-content">
                    <div class="session-summary">
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-label">成功率</span>
                                <span class="stat-value">${successRate.toFixed(1)}%</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">成功字段</span>
                                <span class="stat-value">${lastSession.successfulFields || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">失败字段</span>
                                <span class="stat-value">${lastSession.failedFields || 0}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">填充时间</span>
                                <span class="stat-value">${this.formatTime(lastSession.fillTime || 0)}</span>
                            </div>
                        </div>
                        <div class="summary-message">
                            <p>${lastSession.summary || '填充过程已完成'}</p>
                        </div>
                    </div>

                    <div class="feedback-section">
                        <h3>请为本次填充体验评分</h3>
                        <div class="rating-stars">
                            ${[1, 2, 3, 4, 5].map(star => `
                                <span class="star" data-rating="${star}" onclick="window.fillMonitor.setRating(${star})">⭐</span>
                            `).join('')}
                        </div>
                        <div class="rating-text" id="ratingText">点击星星评分</div>
                    </div>

                    <div class="feedback-section">
                        <h3>意见和建议（可选）</h3>
                        <textarea id="feedbackComment" placeholder="请分享您的使用体验、遇到的问题或改进建议..." rows="3"></textarea>
                    </div>

                    <div class="feedback-actions">
                        <button class="feedback-btn secondary" onclick="window.fillMonitor.closeFeedbackInterface()">
                            跳过反馈
                        </button>
                        <button class="feedback-btn primary" onclick="window.fillMonitor.submitFeedback()">
                            提交反馈
                        </button>
                        <button class="feedback-btn info" onclick="window.fillMonitor.showDetailedStats()">
                            查看详细统计
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addFeedbackStyles();

        // 插入到页面
        document.body.appendChild(feedbackModal);

        // 暴露到全局
        window.fillMonitor = this;

        console.log('📝 显示反馈界面');
    }

    /**
     * 设置评分
     * @param {number} rating 评分（1-5）
     */
    setRating(rating) {
        this.currentRating = rating;

        // 更新星星显示
        const stars = document.querySelectorAll('.star');
        stars.forEach((star, index) => {
            if (index < rating) {
                star.classList.add('active');
            } else {
                star.classList.remove('active');
            }
        });

        // 更新评分文本
        const ratingTexts = ['很差', '较差', '一般', '良好', '优秀'];
        const ratingText = document.getElementById('ratingText');
        if (ratingText) {
            ratingText.textContent = `${rating}星 - ${ratingTexts[rating - 1]}`;
        }
    }

    /**
     * 提交反馈
     */
    async submitFeedback() {
        const comment = document.getElementById('feedbackComment')?.value || '';
        const rating = this.currentRating || 0;

        if (rating === 0) {
            alert('请先为本次体验评分');
            return;
        }

        const feedback = {
            id: Date.now().toString(),
            timestamp: Date.now(),
            sessionId: this.fillHistory[0]?.id || 'unknown',
            rating: rating,
            comment: comment.trim(),
            sessionStats: this.fillHistory[0] || {},
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // 保存反馈
        this.feedbackData.ratings.push({
            rating: rating,
            timestamp: feedback.timestamp,
            sessionId: feedback.sessionId
        });

        if (comment) {
            this.feedbackData.comments.push({
                comment: comment,
                rating: rating,
                timestamp: feedback.timestamp,
                sessionId: feedback.sessionId
            });
        }

        // 保存反馈数据
        await this.saveFeedbackData();

        // 显示感谢消息
        this.showThankYouMessage();

        // 关闭反馈界面
        setTimeout(() => {
            this.closeFeedbackInterface();
        }, 2000);

        console.log('📝 反馈已提交:', feedback);
    }

    /**
     * 显示感谢消息
     */
    showThankYouMessage() {
        const container = document.querySelector('.feedback-content');
        if (container) {
            container.innerHTML = `
                <div class="thank-you-message">
                    <div class="thank-you-icon">🙏</div>
                    <h3>感谢您的反馈！</h3>
                    <p>您的意见对我们改进产品非常重要</p>
                    <div class="thank-you-stats">
                        <p>您是第 <strong>${this.feedbackData.ratings.length}</strong> 位提供反馈的用户</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 显示详细统计
     */
    showDetailedStats() {
        const statsModal = document.createElement('div');
        statsModal.className = 'stats-modal';
        statsModal.innerHTML = `
            <div class="stats-overlay" onclick="this.parentElement.remove()"></div>
            <div class="stats-container">
                <div class="stats-header">
                    <h3>详细统计信息</h3>
                    <button class="stats-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="stats-content">
                    <div class="stats-grid">
                        <div class="stats-card">
                            <h4>总体统计</h4>
                            <div class="stats-list">
                                <div class="stats-item">
                                    <span>总填充次数</span>
                                    <span>${this.stats.totalSessions}</span>
                                </div>
                                <div class="stats-item">
                                    <span>成功次数</span>
                                    <span>${this.stats.successfulSessions}</span>
                                </div>
                                <div class="stats-item">
                                    <span>部分成功</span>
                                    <span>${this.stats.partialSessions}</span>
                                </div>
                                <div class="stats-item">
                                    <span>失败次数</span>
                                    <span>${this.stats.failedSessions}</span>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <h4>字段统计</h4>
                            <div class="stats-list">
                                <div class="stats-item">
                                    <span>总字段数</span>
                                    <span>${this.stats.totalFields}</span>
                                </div>
                                <div class="stats-item">
                                    <span>成功字段</span>
                                    <span>${this.stats.successfulFields}</span>
                                </div>
                                <div class="stats-item">
                                    <span>失败字段</span>
                                    <span>${this.stats.failedFields}</span>
                                </div>
                                <div class="stats-item">
                                    <span>字段成功率</span>
                                    <span>${this.getFieldSuccessRate()}%</span>
                                </div>
                            </div>
                        </div>

                        <div class="stats-card">
                            <h4>性能统计</h4>
                            <div class="stats-list">
                                <div class="stats-item">
                                    <span>平均填充时间</span>
                                    <span>${this.formatTime(this.stats.averageFillTime)}</span>
                                </div>
                                <div class="stats-item">
                                    <span>平均成功率</span>
                                    <span>${this.stats.averageSuccessRate.toFixed(1)}%</span>
                                </div>
                                <div class="stats-item">
                                    <span>用户反馈数</span>
                                    <span>${this.feedbackData.ratings.length}</span>
                                </div>
                                <div class="stats-item">
                                    <span>平均评分</span>
                                    <span>${this.getAverageRating().toFixed(1)}⭐</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="recent-history">
                        <h4>最近填充记录</h4>
                        <div class="history-list">
                            ${this.fillHistory.slice(0, 5).map(entry => `
                                <div class="history-item">
                                    <div class="history-status ${entry.status}">
                                        ${this.getStatusIcon(entry.status)}
                                    </div>
                                    <div class="history-info">
                                        <div class="history-summary">${entry.summary}</div>
                                        <div class="history-details">
                                            ${entry.successRate.toFixed(1)}% 成功率 •
                                            ${this.formatTime(entry.fillTime)} •
                                            ${new Date(entry.timestamp).toLocaleString()}
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(statsModal);
    }

    /**
     * 关闭反馈界面
     */
    closeFeedbackInterface() {
        this.removeFeedbackInterface();

        // 清理全局引用
        if (window.fillMonitor === this) {
            delete window.fillMonitor;
        }
    }

    /**
     * 移除反馈界面
     */
    removeFeedbackInterface() {
        const existingModal = document.getElementById('mdac-feedback-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const existingStats = document.querySelector('.stats-modal');
        if (existingStats) {
            existingStats.remove();
        }
    }

    /**
     * 获取状态图标
     * @param {string} status 状态
     */
    getStatusIcon(status) {
        const icons = {
            [this.fillStates.SUCCESS]: '✅',
            [this.fillStates.PARTIAL]: '⚠️',
            [this.fillStates.FAILED]: '❌',
            [this.fillStates.IN_PROGRESS]: '⏳'
        };
        return icons[status] || '❓';
    }

    /**
     * 获取状态颜色
     * @param {string} status 状态
     */
    getStatusColor(status) {
        const colors = {
            [this.fillStates.SUCCESS]: '#28a745',
            [this.fillStates.PARTIAL]: '#ffc107',
            [this.fillStates.FAILED]: '#dc3545',
            [this.fillStates.IN_PROGRESS]: '#007bff'
        };
        return colors[status] || '#6c757d';
    }

    /**
     * 格式化时间
     * @param {number} milliseconds 毫秒
     */
    formatTime(milliseconds) {
        if (!milliseconds) return '0秒';

        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);

        if (minutes > 0) {
            return `${minutes}分${seconds % 60}秒`;
        } else {
            return `${seconds}秒`;
        }
    }

    /**
     * 获取字段成功率
     */
    getFieldSuccessRate() {
        if (this.stats.totalFields === 0) return 0;
        return ((this.stats.successfulFields / this.stats.totalFields) * 100).toFixed(1);
    }

    /**
     * 获取平均评分
     */
    getAverageRating() {
        if (this.feedbackData.ratings.length === 0) return 0;

        const totalRating = this.feedbackData.ratings.reduce((sum, item) => sum + item.rating, 0);
        return totalRating / this.feedbackData.ratings.length;
    }

    /**
     * 加载历史数据
     */
    async loadHistoryData() {
        try {
            const result = await chrome.storage.local.get(['fillHistory']);
            if (result.fillHistory) {
                this.fillHistory = result.fillHistory;
            }
        } catch (error) {
            console.error('加载填充历史失败:', error);
        }
    }

    /**
     * 保存历史数据
     */
    async saveHistoryData() {
        try {
            await chrome.storage.local.set({
                fillHistory: this.fillHistory.slice(0, 50) // 只保存最近50条
            });
        } catch (error) {
            console.error('保存填充历史失败:', error);
        }
    }

    /**
     * 加载统计数据
     */
    async loadStats() {
        try {
            const result = await chrome.storage.local.get(['fillStats']);
            if (result.fillStats) {
                this.stats = { ...this.stats, ...result.fillStats };
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    /**
     * 保存统计数据
     */
    async saveStats() {
        try {
            await chrome.storage.local.set({
                fillStats: this.stats
            });
        } catch (error) {
            console.error('保存统计数据失败:', error);
        }
    }

    /**
     * 加载反馈数据
     */
    async loadFeedbackData() {
        try {
            const result = await chrome.storage.local.get(['feedbackData']);
            if (result.feedbackData) {
                this.feedbackData = { ...this.feedbackData, ...result.feedbackData };
            }
        } catch (error) {
            console.error('加载反馈数据失败:', error);
        }
    }

    /**
     * 保存反馈数据
     */
    async saveFeedbackData() {
        try {
            await chrome.storage.local.set({
                feedbackData: {
                    ratings: this.feedbackData.ratings.slice(-100), // 只保存最近100条评分
                    comments: this.feedbackData.comments.slice(-50), // 只保存最近50条评论
                    suggestions: this.feedbackData.suggestions.slice(-50),
                    bugReports: this.feedbackData.bugReports.slice(-50)
                }
            });
        } catch (error) {
            console.error('保存反馈数据失败:', error);
        }
    }

    /**
     * 获取统计摘要
     */
    getStatsSummary() {
        return {
            ...this.stats,
            sessionSuccessRate: this.stats.totalSessions > 0
                ? ((this.stats.successfulSessions / this.stats.totalSessions) * 100).toFixed(2)
                : 0,
            fieldSuccessRate: this.getFieldSuccessRate(),
            averageRating: this.getAverageRating().toFixed(1),
            totalFeedbacks: this.feedbackData.ratings.length,
            recentSessions: this.fillHistory.slice(0, 5)
        };
    }

    /**
     * 清除所有数据
     */
    async clearAllData() {
        this.fillHistory = [];
        this.stats = {
            totalSessions: 0,
            successfulSessions: 0,
            partialSessions: 0,
            failedSessions: 0,
            totalFields: 0,
            successfulFields: 0,
            failedFields: 0,
            averageFillTime: 0,
            averageSuccessRate: 0,
            lastUpdated: Date.now()
        };
        this.feedbackData = {
            ratings: [],
            comments: [],
            suggestions: [],
            bugReports: []
        };

        try {
            await chrome.storage.local.remove(['fillHistory', 'fillStats', 'feedbackData']);
            console.log('🗑️ 所有填充数据已清除');
        } catch (error) {
            console.error('清除数据失败:', error);
        }
    }

    /**
     * 添加反馈界面样式
     */
    addFeedbackStyles() {
        if (document.getElementById('mdac-feedback-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-feedback-styles';
        styles.textContent = `
            .mdac-feedback-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10006;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .feedback-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(3px);
            }

            .feedback-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 500px;
                max-height: 80%;
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                overflow: hidden;
                animation: feedbackSlideIn 0.3s ease-out;
            }

            @keyframes feedbackSlideIn {
                from {
                    opacity: 0;
                    transform: translate(-50%, -60%);
                }
                to {
                    opacity: 1;
                    transform: translate(-50%, -50%);
                }
            }

            .feedback-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20px 24px;
                color: white;
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            }

            .feedback-title {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .feedback-icon {
                font-size: 24px;
            }

            .feedback-title h2 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
            }

            .feedback-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .feedback-content {
                padding: 24px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .session-summary {
                margin-bottom: 24px;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
            }

            .summary-stats {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
                margin-bottom: 12px;
            }

            .stat-item {
                text-align: center;
                padding: 8px;
                background: white;
                border-radius: 6px;
            }

            .stat-label {
                display: block;
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 4px;
            }

            .stat-value {
                display: block;
                font-size: 16px;
                font-weight: 600;
                color: #495057;
            }

            .summary-message {
                text-align: center;
                color: #495057;
                font-size: 14px;
            }

            .feedback-section {
                margin-bottom: 20px;
            }

            .feedback-section h3 {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #333;
                font-weight: 600;
            }

            .rating-stars {
                display: flex;
                gap: 8px;
                justify-content: center;
                margin-bottom: 8px;
            }

            .star {
                font-size: 24px;
                cursor: pointer;
                transition: all 0.2s ease;
                opacity: 0.3;
            }

            .star:hover,
            .star.active {
                opacity: 1;
                transform: scale(1.1);
            }

            .rating-text {
                text-align: center;
                font-size: 14px;
                color: #6c757d;
                margin-bottom: 16px;
            }

            #feedbackComment {
                width: 100%;
                padding: 12px;
                border: 1px solid #ced4da;
                border-radius: 6px;
                font-size: 14px;
                font-family: inherit;
                resize: vertical;
                min-height: 80px;
            }

            #feedbackComment:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }

            .feedback-actions {
                display: flex;
                gap: 8px;
                justify-content: flex-end;
                flex-wrap: wrap;
            }

            .feedback-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 80px;
            }

            .feedback-btn.primary {
                background: #007bff;
                color: white;
            }

            .feedback-btn.primary:hover {
                background: #0056b3;
                transform: translateY(-1px);
            }

            .feedback-btn.secondary {
                background: #6c757d;
                color: white;
            }

            .feedback-btn.secondary:hover {
                background: #545b62;
            }

            .feedback-btn.info {
                background: #17a2b8;
                color: white;
            }

            .feedback-btn.info:hover {
                background: #138496;
            }

            .thank-you-message {
                text-align: center;
                padding: 40px 20px;
            }

            .thank-you-icon {
                font-size: 48px;
                margin-bottom: 16px;
            }

            .thank-you-message h3 {
                margin: 0 0 8px 0;
                color: #28a745;
                font-size: 20px;
            }

            .thank-you-message p {
                margin: 0 0 16px 0;
                color: #6c757d;
            }

            .thank-you-stats {
                padding: 12px;
                background: #f8f9fa;
                border-radius: 6px;
                color: #495057;
                font-size: 14px;
            }

            .stats-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10007;
                font-family: inherit;
            }

            .stats-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.4);
            }

            .stats-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 800px;
                max-height: 80%;
                background: white;
                border-radius: 8px;
                overflow: hidden;
            }

            .stats-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                background: #f8f9fa;
                border-bottom: 1px solid #e9ecef;
            }

            .stats-header h3 {
                margin: 0;
                font-size: 16px;
                color: #333;
            }

            .stats-close {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .stats-content {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 16px;
                margin-bottom: 24px;
            }

            .stats-card {
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }

            .stats-card h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #333;
                font-weight: 600;
            }

            .stats-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .stats-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 13px;
            }

            .stats-item span:first-child {
                color: #6c757d;
            }

            .stats-item span:last-child {
                font-weight: 600;
                color: #495057;
            }

            .recent-history h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #333;
                font-weight: 600;
            }

            .history-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .history-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 8px;
                background: #f8f9fa;
                border-radius: 6px;
            }

            .history-status {
                font-size: 16px;
                width: 24px;
                text-align: center;
            }

            .history-info {
                flex: 1;
            }

            .history-summary {
                font-size: 13px;
                color: #495057;
                margin-bottom: 2px;
            }

            .history-details {
                font-size: 11px;
                color: #6c757d;
            }

            @media (max-width: 768px) {
                .feedback-container {
                    width: 95%;
                    max-height: 90%;
                }

                .summary-stats {
                    grid-template-columns: 1fr 1fr;
                }

                .feedback-actions {
                    justify-content: center;
                }

                .stats-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;

        document.head.appendChild(styles);
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FillMonitor;
} else {
    window.FillMonitor = FillMonitor;
}
