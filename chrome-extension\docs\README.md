# MDAC AI智能分析工具 - Chrome扩展版

基于Gemini AI的智能内容解析和表单验证Chrome扩展，专注于AI驱动的数据分析和处理功能。

## 🚀 功能特性

### AI核心功能

- **🧠 智能内容解析** - 从任意格式文本中提取结构化表单数据
- **✅ AI数据验证** - 智能验证数据格式、逻辑关系和完整性
- **🌐 智能地址翻译** - 中英文地址自动翻译和格式标准化
- **🤖 实时AI助手** - 提供智能建议、错误诊断和优化方案
- **🔍 格式智能检查** - 自动检测和修正数据格式错误
- **💡 上下文理解** - 基于语境的智能分析和推理

### 技术特性

- **Gemini AI集成** - 使用Google Gemini 2.5 Flash Lite模型
- **Manifest V3** - 使用最新的Chrome扩展规范
- **智能缓存系统** - 优化AI响应性能和用户体验
- **安全权限管理** - 最小化权限原则，确保数据安全
- **响应式设计** - 适配不同屏幕尺寸的设备

## 📦 安装方法

### 开发者模式安装
1. 打开Chrome浏览器，进入 `chrome://extensions/`
2. 开启右上角的"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择本项目的 `chrome-extension` 文件夹
5. 扩展安装完成，图标会出现在浏览器工具栏

### 配置API密钥
1. 点击扩展图标，选择"设置"
2. 在"AI配置"标签页中输入您的Gemini API密钥
3. 点击"测试AI连接"验证配置
4. 保存设置

**注意**: 扩展已预配置了默认的Gemini API密钥，可以直接使用。如需使用自己的API密钥，请在设置中更换。

## 🤖 Gemini AI 集成特性

### 从原始HTML工具继承的AI功能
本Chrome扩展完全集成了原始 `mdac-ai-enhanced.html` 文件中的所有Gemini AI功能：

#### **智能验证系统**
- **实时字段验证** - 输入时自动验证数据格式和逻辑
- **表单完整性检查** - AI分析表单数据的完整性和准确性
- **智能错误检测** - 自动识别和提示潜在错误

#### **内容解析引擎**
- **智能信息提取** - 从任意格式文本中提取表单数据
- **多语言支持** - 自动识别和翻译中文内容
- **结构化数据输出** - 将非结构化信息转换为表单字段

#### **地址翻译服务**
- **中英文地址翻译** - 专业的马来西亚地址翻译
- **格式标准化** - 确保地址符合官方表单要求
- **自动检测翻译** - 检测到中文地址时自动翻译

#### **AI配置管理**
- **预配置API密钥** - 使用原文件中的有效Gemini API密钥
- **模型选择** - 支持多个Gemini模型版本
- **参数调优** - 可调节AI创造性和响应质量
- **缓存优化** - 智能缓存AI响应，提高性能

### AI功能配置文件
扩展包含专门的 `ai-config.js` 配置文件，包含：
- **提示词模板** - 预定义的专业提示词
- **上下文设置** - 针对不同场景的AI上下文
- **功能开关** - 可控制的AI功能启用/禁用
- **安全设置** - Gemini AI的安全过滤配置

## 🎯 使用指南

### 快速开始

1. **打开MDAC网站** - 访问 https://imigresen-online.imi.gov.my/mdac/main?registerMain
2. **点击扩展图标** - 在浏览器工具栏点击MDAC AI图标
3. **使用智能解析** - 点击"智能解析"按钮打开内容解析面板
4. **粘贴内容** - 将任意格式的文本粘贴到解析框中
5. **AI自动分析** - AI会自动提取和验证表单数据
6. **一键填充** - 验证通过后点击"自动填充"完成表单填写

### AI智能功能

- **智能内容解析** - 支持邮件、文档、聊天记录等多种格式
- **实时数据验证** - AI驱动的格式检查和逻辑验证
- **智能地址翻译** - 中文地址自动翻译为英文标准格式
- **错误智能诊断** - 自动识别问题并提供解决方案
- **上下文理解** - 基于语境的智能推理和建议

## 🧠 智能内容解析功能

### 功能特性
- **多格式支持** - 支持邮件、文档、聊天记录等任意格式内容
- **AI智能提取** - 自动识别和提取21个表单字段数据
- **中英文混合** - 自动识别中文内容并翻译为英文
- **数据验证** - 实时验证提取数据的完整性和格式正确性
- **缺失字段提示** - 明确显示需要补充的必填字段
- **一键填充** - 验证通过后可直接填充到MDAC表单

### 使用方法
1. **打开解析面板** - 在弹窗或表单编辑器中点击"智能解析"按钮
2. **粘贴内容** - 将任意格式的文本粘贴到输入框中
3. **开始解析** - 点击"开始解析"，AI将自动提取数据
4. **查看结果** - 查看提取的字段和完整度指示器
5. **补充缺失** - 根据提示补充缺失的必填字段
6. **自动填充** - 数据完整后点击"自动填充"完成表单填写

### 支持的内容类型
- **护照信息** - 护照扫描件的文字内容
- **酒店预订** - 酒店确认邮件和预订信息
- **航班信息** - 机票确认邮件和行程单
- **个人资料** - 简历、身份证明等文档
- **聊天记录** - 包含个人信息的对话内容
- **混合文档** - 包含多种信息的复合文档

## 🛠️ 技术架构

### 文件结构
```
chrome-extension/
├── manifest.json          # 扩展配置文件
├── popup.html             # 弹窗界面
├── popup.css              # 弹窗样式
├── popup.js               # 弹窗逻辑
├── background.js          # 后台服务脚本
├── content-script.js      # 内容脚本
├── content-styles.css     # 内容脚本样式
├── options.html           # 设置页面
├── options.css            # 设置页面样式
├── options.js             # 设置页面逻辑
├── form-editor.html       # 表单编辑器
├── form-editor.css        # 表单编辑器样式
├── form-editor.js         # 表单编辑器逻辑
├── icons/                 # 扩展图标
└── README.md             # 说明文档
```

### 核心组件
- **Popup界面** - 扩展的主要交互界面，提供快速操作和状态显示
- **Background Service** - 后台服务处理API调用、数据存储和消息传递
- **Content Script** - 注入到MDAC网站的脚本，处理表单填充和页面交互
- **Options页面** - 完整的设置和配置管理界面
- **Form Editor** - 独立的表单编辑器，提供完整的表单编辑功能

### 权限说明
- `activeTab` - 访问当前活动标签页，用于AI智能填充
- `storage` - 本地数据存储，保存AI设置和缓存
- `scripting` - 脚本注入，用于页面交互和数据提取
- `tabs` - 标签页管理，用于打开表单编辑器

## 🔒 隐私与安全

### 数据保护
- **本地存储** - 所有个人数据仅存储在用户本地，不上传到任何服务器
- **数据加密** - 敏感信息使用加密算法保护
- **最小权限** - 仅请求必要的浏览器权限
- **透明操作** - 所有操作都有详细的日志记录

### 安全特性
- **HTTPS通信** - 所有网络请求使用HTTPS加密
- **API密钥保护** - Gemini API密钥本地加密存储
- **会话隔离** - 每个标签页的操作相互独立
- **安全验证** - 多层数据验证确保操作安全

## ⚠️ 重要提醒

### 使用须知
1. **合规使用** - 请遵守MDAC官网的使用条款和当地法律法规
2. **AI辅助性质** - AI分析结果仅供参考，请人工核实数据准确性
3. **数据隐私** - 所有AI分析在本地进行，不上传个人信息到服务器
4. **官方提交** - 建议最终通过MDAC官方网站提交表单

### 技术限制
- **网站更新** - MDAC官网更新可能影响扩展功能，我们会及时适配
- **浏览器兼容** - 仅支持Chrome浏览器及基于Chromium的浏览器
- **网络依赖** - AI功能需要稳定的网络连接访问Gemini API
- **AI准确性** - AI分析结果可能存在误差，请仔细核实

## 🆘 故障排除

### 常见问题
1. **扩展无法加载** - 检查是否开启开发者模式，重新加载扩展
2. **AI功能不可用** - 验证Gemini API密钥是否正确配置
3. **表单填充失败** - 确认当前页面为MDAC官网，刷新页面重试
4. **数据丢失** - 检查浏览器存储权限，恢复备份数据

### 调试模式
1. 在设置中开启"调试模式"
2. 打开浏览器开发者工具查看控制台日志
3. 检查扩展的后台页面错误信息
4. 联系开发者获取技术支持

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- **GitHub Issues** - 提交bug报告和功能请求
- **邮箱支持** - 发送详细问题描述
- **在线文档** - 查看完整的使用指南和API文档

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

---

**MDAC AI智能分析工具** - 让数据分析变得简单智能 🧠
