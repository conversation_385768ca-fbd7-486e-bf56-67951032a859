/**
 * MDAC AI智能填充工具 - 后台服务脚本
 * 处理扩展的后台逻辑、消息传递和API调用
 */

class MDACBackground {
    constructor() {
        this.init();
    }

    /**
     * 初始化后台服务
     */
    init() {
        this.setupEventListeners();
        this.initializeStorage();
        console.log('MDAC AI扩展后台服务已启动');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 扩展安装/更新事件
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 消息传递
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 快捷键命令
        chrome.commands.onCommand.addListener((command) => {
            this.handleCommand(command);
        });

        // 标签页更新事件
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdated(tabId, changeInfo, tab);
        });

        // 网络请求拦截（用于API分析）
        chrome.webRequest.onBeforeRequest.addListener(
            (details) => this.handleNetworkRequest(details),
            { urls: ["https://imigresen-online.imi.gov.my/*"] },
            ["requestBody"]
        );
    }

    /**
     * 初始化存储
     */
    async initializeStorage() {
        try {
            const storage = await chrome.storage.sync.get(['mdacSettings']);
            if (!storage.mdacSettings) {
                await chrome.storage.sync.set({
                    mdacSettings: {
                        defaultMode: 'smart',
                        autoFill: false,
                        aiEnabled: true,
                        debugMode: false,
                        geminiApiKey: '',
                        encryptData: true
                    }
                });
            }
        } catch (error) {
            console.error('初始化存储失败:', error);
        }
    }

    /**
     * 处理扩展安装/更新
     */
    async handleInstalled(details) {
        if (details.reason === 'install') {
            // 首次安装
            console.log('MDAC AI扩展首次安装');
            await this.showWelcomePage();
        } else if (details.reason === 'update') {
            // 扩展更新
            console.log('MDAC AI扩展已更新到版本:', chrome.runtime.getManifest().version);
            await this.handleUpdate(details.previousVersion);
        }
    }

    /**
     * 显示欢迎页面
     */
    async showWelcomePage() {
        try {
            await chrome.tabs.create({
                url: chrome.runtime.getURL('welcome.html')
            });
        } catch (error) {
            console.error('打开欢迎页面失败:', error);
        }
    }

    /**
     * 处理扩展更新
     */
    async handleUpdate(previousVersion) {
        // 处理版本迁移逻辑
        console.log(`从版本 ${previousVersion} 更新`);
    }

    /**
     * 处理消息传递
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'callGeminiAI':
                    const aiResponse = await this.callGeminiAI(message.prompt, message.context);
                    sendResponse({ success: true, data: aiResponse });
                    break;

                case 'analyzeAPI':
                    const apiAnalysis = await this.analyzeAPI(message.requests);
                    sendResponse({ success: true, data: apiAnalysis });
                    break;

                case 'saveFormData':
                    await this.saveFormData(message.data);
                    sendResponse({ success: true });
                    break;

                case 'getFormTemplates':
                    const templates = await this.getFormTemplates();
                    sendResponse({ success: true, data: templates });
                    break;

                case 'startNetworkCapture':
                    await this.startNetworkCapture(sender.tab.id);
                    sendResponse({ success: true });
                    break;

                case 'stopNetworkCapture':
                    const capturedData = await this.stopNetworkCapture(sender.tab.id);
                    sendResponse({ success: true, data: capturedData });
                    break;

                case 'encryptData':
                    const encrypted = await this.encryptData(message.data);
                    sendResponse({ success: true, data: encrypted });
                    break;

                case 'decryptData':
                    const decrypted = await this.decryptData(message.data);
                    sendResponse({ success: true, data: decrypted });
                    break;

                case 'directAPISubmit':
                    const submitResult = await this.directAPISubmit(message.data);
                    sendResponse({ success: true, data: submitResult });
                    break;

                case 'getUserData':
                    const userData = await this.getUserData();
                    sendResponse({ success: true, data: userData });
                    break;

                default:
                    sendResponse({ success: false, error: '未知的操作类型' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * 处理快捷键命令
     */
    async handleCommand(command) {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            switch (command) {
                case 'fill-form':
                    if (tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
                        await chrome.tabs.sendMessage(tab.id, { action: 'quickFill' });
                    }
                    break;

                case 'analyze-api':
                    if (tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
                        await chrome.tabs.sendMessage(tab.id, { action: 'analyzeAPI' });
                    }
                    break;
            }
        } catch (error) {
            console.error('处理快捷键命令失败:', error);
        }
    }

    /**
     * 处理标签页更新
     */
    handleTabUpdated(tabId, changeInfo, tab) {
        if (changeInfo.status === 'complete' && tab.url && tab.url.includes('imigresen-online.imi.gov.my')) {
            // MDAC页面加载完成，更新扩展图标
            chrome.action.setBadgeText({
                text: '✓',
                tabId: tabId
            });
            chrome.action.setBadgeBackgroundColor({
                color: '#28a745',
                tabId: tabId
            });
        }
    }

    /**
     * 处理网络请求（用于API分析）
     */
    handleNetworkRequest(details) {
        if (details.method === 'POST' && details.url.includes('/mdac/register')) {
            // 捕获MDAC表单提交请求
            this.logNetworkRequest(details);
        }
    }

    /**
     * 调用Gemini AI API
     */
    async callGeminiAI(prompt, context = '') {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            const apiKey = settings.mdacSettings?.geminiApiKey;
            
            if (!apiKey) {
                throw new Error('请先在设置中配置Gemini API密钥');
            }

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: context ? `${context}\n\n${prompt}` : prompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 1024,
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status}`);
            }

            const data = await response.json();
            return data.candidates[0].content.parts[0].text;
        } catch (error) {
            console.error('Gemini AI调用失败:', error);
            throw error;
        }
    }

    /**
     * 分析API请求
     */
    async analyzeAPI(requests) {
        // 实现API分析逻辑
        const analysis = {
            endpoints: [],
            security: {},
            parameters: {},
            recommendations: []
        };

        requests.forEach(request => {
            if (request.url.includes('/mdac/register')) {
                analysis.endpoints.push({
                    url: request.url,
                    method: request.method,
                    headers: request.headers,
                    body: request.body
                });
            }
        });

        return analysis;
    }

    /**
     * 保存表单数据
     */
    async saveFormData(data) {
        const timestamp = Date.now();
        const historyItem = {
            ...data,
            timestamp: timestamp,
            id: `mdac_${timestamp}`
        };

        const storage = await chrome.storage.local.get(['mdacHistory']);
        const history = storage.mdacHistory || [];
        history.unshift(historyItem);

        // 限制历史记录数量
        if (history.length > 50) {
            history.splice(50);
        }

        await chrome.storage.local.set({ mdacHistory: history });
    }

    /**
     * 获取表单模板
     */
    async getFormTemplates() {
        const storage = await chrome.storage.local.get(['mdacTemplates']);
        return storage.mdacTemplates || [];
    }

    /**
     * 开始网络捕获
     */
    async startNetworkCapture(tabId) {
        try {
            await chrome.debugger.attach({ tabId }, "1.0");
            await chrome.debugger.sendCommand({ tabId }, "Network.enable");
            console.log('网络捕获已启动');
        } catch (error) {
            console.error('启动网络捕获失败:', error);
            throw error;
        }
    }

    /**
     * 停止网络捕获
     */
    async stopNetworkCapture(tabId) {
        try {
            await chrome.debugger.detach({ tabId });
            console.log('网络捕获已停止');
            return { captured: true };
        } catch (error) {
            console.error('停止网络捕获失败:', error);
            throw error;
        }
    }

    /**
     * 加密数据
     */
    async encryptData(data) {
        // 实现数据加密逻辑
        return btoa(JSON.stringify(data));
    }

    /**
     * 解密数据
     */
    async decryptData(encryptedData) {
        // 实现数据解密逻辑
        return JSON.parse(atob(encryptedData));
    }

    /**
     * 记录网络请求
     */
    logNetworkRequest(details) {
        console.log('捕获到MDAC网络请求:', details);
        // 可以将请求数据保存到存储中供后续分析
    }

    /**
     * 直接API提交
     */
    async directAPISubmit(userData) {
        try {
            // 这里实现API直接提交逻辑
            // 注意：这是实验性功能，需要谨慎使用
            console.log('尝试API直接提交:', userData);

            // 模拟API提交结果
            return {
                success: false,
                message: 'API直接提交功能暂未完全实现，建议使用书签工具',
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('API直接提交失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户数据
     */
    async getUserData() {
        try {
            const storage = await chrome.storage.local.get(['mdacTemplates']);
            const templates = storage.mdacTemplates || [];

            if (templates.length > 0) {
                // 返回最新的模板数据
                const latest = templates[templates.length - 1];
                return {
                    name: latest.name,
                    passportNo: latest.passport,
                    dateOfBirth: latest.dob,
                    nationality: latest.nationality,
                    email: latest.email,
                    mobileNo: latest.phone,
                    // 添加默认的其他字段
                    sex: '1',
                    countryCode: '+60',
                    arrivalDate: this.getDefaultArrivalDate(),
                    departureDate: this.getDefaultDepartureDate(),
                    flightNo: 'MH123',
                    modeOfTravel: 'AIR',
                    lastPort: 'SGP',
                    accommodation: '01',
                    address: 'KUALA LUMPUR CITY CENTER',
                    state: '14',
                    postcode: '50000',
                    city: '1400'
                };
            }

            return null;
        } catch (error) {
            console.error('获取用户数据失败:', error);
            return null;
        }
    }

    /**
     * 获取默认到达日期（明天）
     */
    getDefaultArrivalDate() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    /**
     * 获取默认离开日期（后天）
     */
    getDefaultDepartureDate() {
        const dayAfterTomorrow = new Date();
        dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
        return dayAfterTomorrow.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }
}

// 初始化后台服务
new MDACBackground();
