# MDAC表单字段规格详细分析

## 概述
本文档详细分析马来西亚数字入境卡(MDAC)官方网站的所有表单字段规格要求，为AI智能填充工具提供准确的数据验证和格式化标准。

## 1. 个人信息字段 (Personal Information)

### 1.1 姓名字段 (Name)
- **字段ID**: `name`
- **类型**: 文本输入框
- **格式要求**: 
  - 必须为英文字符
  - 支持字母、空格、连字符(-)、撇号(')
  - 不允许数字和特殊符号
  - 大小写不敏感，但建议首字母大写
- **字符限制**: 最大50个字符
- **必填**: 是
- **验证规则**: 
  - 至少包含姓和名
  - 格式：`SURNAME, Given Name` 或 `Given Name SURNAME`
- **示例**: `ZHANG, San` 或 `John SMITH`

### 1.2 护照号码 (Passport Number)
- **字段ID**: `passportNo`
- **类型**: 文本输入框
- **格式要求**: 
  - 字母数字组合
  - 通常为1-2个字母 + 6-9位数字
  - 不允许空格和特殊符号
- **字符限制**: 最大15个字符
- **必填**: 是
- **验证规则**: 
  - 中国护照：1个字母 + 8位数字 (如: E12345678)
  - 美国护照：9位数字或字母数字组合
  - 马来西亚护照：1个字母 + 8位数字
- **示例**: `A12345678`, `*********`

### 1.3 出生日期 (Date of Birth)
- **字段ID**: `dateOfBirth`
- **类型**: 日期选择器
- **格式要求**: DD/MM/YYYY
- **必填**: 是
- **验证规则**: 
  - 日期必须早于当前日期
  - 年龄必须在合理范围内(通常0-120岁)
  - 不能是未来日期
- **示例**: `01/01/1990`

### 1.4 国籍 (Nationality)
- **字段ID**: `nationality`
- **类型**: 下拉选择框
- **格式要求**: 3位ISO国家代码
- **必填**: 是
- **常用选项值**:
  - `CHN` - 中国 (China)
  - `USA` - 美国 (United States)
  - `SGP` - 新加坡 (Singapore)
  - `MYS` - 马来西亚 (Malaysia)
  - `GBR` - 英国 (United Kingdom)
  - `AUS` - 澳大利亚 (Australia)
  - `CAN` - 加拿大 (Canada)
  - `JPN` - 日本 (Japan)
  - `KOR` - 韩国 (South Korea)
  - `THA` - 泰国 (Thailand)
  - `IDN` - 印度尼西亚 (Indonesia)
  - `VNM` - 越南 (Vietnam)
  - `PHL` - 菲律宾 (Philippines)

### 1.5 性别 (Gender)
- **字段ID**: `sex`
- **类型**: 下拉选择框
- **格式要求**: 数字代码
- **必填**: 是
- **选项值**:
  - `1` - 男性 (Male)
  - `2` - 女性 (Female)

### 1.6 护照到期日期 (Passport Expiry Date)
- **字段ID**: `passportExpiry`
- **类型**: 日期选择器
- **格式要求**: DD/MM/YYYY
- **必填**: 是
- **验证规则**: 
  - 必须晚于当前日期
  - 必须晚于出生日期
  - 通常在入境日期后至少6个月有效
- **示例**: `31/12/2030`

### 1.7 电子邮箱 (Email)
- **字段ID**: `email`
- **类型**: 邮箱输入框
- **格式要求**: 标准邮箱格式
- **字符限制**: 最大100个字符
- **必填**: 是
- **验证规则**: 
  - 必须包含@符号
  - 域名部分必须有效
  - 不允许连续的点号
- **示例**: `<EMAIL>`

### 1.8 确认邮箱 (Confirm Email)
- **字段ID**: `confirmEmail`
- **类型**: 邮箱输入框
- **格式要求**: 必须与邮箱字段完全一致
- **必填**: 是

### 1.9 国家代码 (Country Code)
- **字段ID**: `countryCode`
- **类型**: 下拉选择框
- **格式要求**: +号加数字
- **必填**: 是
- **常用选项值**:
  - `+86` - 中国
  - `+60` - 马来西亚
  - `+65` - 新加坡
  - `+1` - 美国/加拿大
  - `+44` - 英国
  - `+61` - 澳大利亚
  - `+81` - 日本
  - `+82` - 韩国
  - `+66` - 泰国

### 1.10 手机号码 (Mobile Number)
- **字段ID**: `mobileNo`
- **类型**: 文本输入框
- **格式要求**: 纯数字，不含国家代码
- **字符限制**: 通常7-15位数字
- **必填**: 是
- **验证规则**: 
  - 只允许数字
  - 长度根据国家代码验证
  - 中国手机号：11位数字
  - 马来西亚手机号：9-10位数字
- **示例**: `13812345678` (中国), `*********` (马来西亚)

## 2. 旅行信息字段 (Travel Information)

### 2.1 到达日期 (Arrival Date)
- **字段ID**: `arrivalDate`
- **类型**: 日期选择器
- **格式要求**: DD/MM/YYYY
- **必填**: 是
- **验证规则**: 
  - 不能早于当前日期
  - 必须早于离开日期
  - 通常在护照有效期内
- **示例**: `15/01/2024`

### 2.2 离开日期 (Departure Date)
- **字段ID**: `departureDate`
- **类型**: 日期选择器
- **格式要求**: DD/MM/YYYY
- **必填**: 是
- **验证规则**: 
  - 必须晚于到达日期
  - 停留时间不能超过签证允许期限
- **示例**: `25/01/2024`

### 2.3 航班号 (Flight Number)
- **字段ID**: `flightNo`
- **类型**: 文本输入框
- **格式要求**: 航空公司代码 + 数字
- **字符限制**: 最大10个字符
- **必填**: 是（如果选择航空旅行）
- **验证规则**: 
  - 2-3位字母 + 1-4位数字
  - 常见格式：MH123, CZ351, SQ123
- **示例**: `MH123`, `CZ351`, `SQ456`

### 2.4 旅行方式 (Mode of Travel)
- **字段ID**: `modeOfTravel`
- **类型**: 下拉选择框
- **必填**: 是
- **选项值**:
  - `AIR` - 航空
  - `LAND` - 陆路
  - `SEA` - 海路

### 2.5 最后出发港口 (Last Port of Embarkation)
- **字段ID**: `lastPort`
- **类型**: 下拉选择框
- **格式要求**: 3位IATA机场代码
- **必填**: 是
- **常用选项值**:
  - `PEK` - 北京首都国际机场
  - `PVG` - 上海浦东国际机场
  - `CAN` - 广州白云国际机场
  - `SZX` - 深圳宝安国际机场
  - `HKG` - 香港国际机场
  - `SIN` - 新加坡樟宜机场
  - `BKK` - 曼谷素万那普机场
  - `NRT` - 东京成田国际机场
  - `ICN` - 首尔仁川国际机场

### 2.6 住宿类型 (Accommodation Type)
- **字段ID**: `accommodation`
- **类型**: 下拉选择框
- **格式要求**: 2位数字代码
- **必填**: 是
- **选项值**:
  - `01` - 酒店 (Hotel)
  - `02` - 朋友家 (Friend's House)
  - `03` - 民宿/Airbnb (Homestay/Airbnb)
  - `04` - 亲戚家 (Relative's House)
  - `05` - 公司宿舍 (Company Accommodation)
  - `99` - 其他 (Others)

### 2.7 住宿地址行1 (Address Line 1)
- **字段ID**: `address`
- **类型**: 文本输入框
- **格式要求**: 英文地址格式
- **字符限制**: 最大100个字符
- **必填**: 是
- **验证规则**: 
  - 必须为英文
  - 包含街道号码和街道名称
  - 不允许中文字符
- **示例**: `123 Jalan Bukit Bintang`

### 2.8 住宿地址行2 (Address Line 2)
- **字段ID**: `address2`
- **类型**: 文本输入框
- **格式要求**: 英文地址格式
- **字符限制**: 最大100个字符
- **必填**: 否
- **示例**: `Pavilion Tower, Level 5`

### 2.9 州代码 (State Code)
- **字段ID**: `state`
- **类型**: 下拉选择框
- **格式要求**: 2位数字代码
- **必填**: 是
- **选项值**:
  - `01` - 柔佛 (Johor)
  - `02` - 吉打 (Kedah)
  - `03` - 吉兰丹 (Kelantan)
  - `04` - 马六甲 (Melaka)
  - `05` - 森美兰 (Negeri Sembilan)
  - `06` - 彭亨 (Pahang)
  - `07` - 槟城 (Pulau Pinang)
  - `08` - 霹雳 (Perak)
  - `09` - 玻璃市 (Perlis)
  - `10` - 雪兰莪 (Selangor)
  - `11` - 登嘉楼 (Terengganu)
  - `12` - 沙巴 (Sabah)
  - `13` - 砂拉越 (Sarawak)
  - `14` - 吉隆坡联邦直辖区 (Kuala Lumpur)
  - `15` - 纳闽联邦直辖区 (Labuan)
  - `16` - 布城联邦直辖区 (Putrajaya)

### 2.10 邮政编码 (Postcode)
- **字段ID**: `postcode`
- **类型**: 文本输入框
- **格式要求**: 5位数字
- **必填**: 是
- **验证规则**: 
  - 必须为5位数字
  - 必须与州代码和城市匹配
  - 范围通常在01000-99999之间
- **示例**: `50088` (吉隆坡), `10200` (槟城)

### 2.11 城市代码 (City Code)
- **字段ID**: `city`
- **类型**: 下拉选择框
- **格式要求**: 4位数字代码
- **必填**: 是
- **主要选项值**:
  - `1400` - 吉隆坡 (Kuala Lumpur)
  - `1000` - 槟城 (George Town, Penang)
  - `0700` - 马六甲 (Melaka)
  - `0100` - 新山 (Johor Bahru)
  - `1000` - 怡保 (Ipoh)
  - `0600` - 关丹 (Kuantan)
  - `1100` - 瓜拉登嘉楼 (Kuala Terengganu)
  - `0900` - 加央 (Kangar)
  - `1200` - 亚庇 (Kota Kinabalu)
  - `1300` - 古晋 (Kuching)

## 3. 字段依赖关系

### 3.1 地理位置依赖
- 州代码 → 城市代码 → 邮政编码
- 必须确保三者之间的逻辑一致性

### 3.2 日期逻辑依赖
- 出生日期 < 当前日期
- 护照到期日期 > 当前日期
- 到达日期 ≥ 当前日期
- 离开日期 > 到达日期

### 3.3 联系方式依赖
- 国家代码 + 手机号码的组合必须有效
- 邮箱与确认邮箱必须完全一致

## 4. 数据验证规则总结

### 4.1 必填字段列表
所有字段均为必填，除了：
- 住宿地址行2 (address2) - 可选

### 4.2 格式验证优先级
1. **高优先级**: 姓名、护照号码、日期格式
2. **中优先级**: 邮箱格式、手机号码格式
3. **低优先级**: 地址格式、航班号格式

### 4.3 错误处理策略
1. **格式错误**: 立即提示并阻止提交
2. **逻辑错误**: 警告提示但允许用户确认
3. **依赖错误**: 自动修正或提供建议选项

## 5. AI识别优化建议

基于以上分析，AI识别系统应重点优化：
1. 中文姓名到英文的标准化转换
2. 各国护照号码格式的准确识别
3. 多种日期格式到DD/MM/YYYY的转换
4. 地址信息的智能分解和标准化
5. 航班号和机场代码的智能匹配
