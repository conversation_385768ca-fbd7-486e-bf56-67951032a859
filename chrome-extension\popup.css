/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    min-height: 500px;
    font-family: 'Microsoft YaHei', 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    font-size: 14px;
    line-height: 1.4;
}

.container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.ai-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 网站检测区域 */
.site-detection {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.detection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
}

.detection-status.detected {
    color: #28a745;
}

.detection-status.not-detected {
    color: #dc3545;
}

/* 主要内容区域 */
.main-content {
    padding: 16px;
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
}

.action-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-btn .icon {
    font-size: 16px;
}

.action-btn .text {
    font-weight: 500;
}

/* 模式选择器 */
.mode-selector {
    margin-bottom: 20px;
}

.mode-selector h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #333;
}

.mode-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.mode-option {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mode-option:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.mode-option.active {
    background: #e7f3ff;
    border-color: #667eea;
}

.mode-icon {
    font-size: 18px;
    margin-right: 10px;
}

.mode-info {
    flex: 1;
}

.mode-info h4 {
    font-size: 13px;
    margin-bottom: 2px;
    color: #333;
}

.mode-info p {
    font-size: 11px;
    color: #666;
}

.mode-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #dee2e6;
}

.mode-status.active {
    background: #28a745;
}

/* 数据管理 */
.data-management {
    margin-bottom: 20px;
}

.data-management h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #333;
}

.data-actions {
    display: flex;
    gap: 6px;
}

.data-btn {
    flex: 1;
    padding: 8px 4px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    font-size: 10px;
    color: #666;
    transition: all 0.2s ease;
}

.data-btn:hover {
    background: #f8f9fa;
    border-color: #667eea;
}

.data-btn .icon {
    font-size: 14px;
}

/* AI助手 */
.ai-assistant {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.ai-title {
    font-size: 13px;
    font-weight: 600;
}

.ai-indicator {
    width: 6px;
    height: 6px;
    background: #4ade80;
    border-radius: 50%;
    margin-left: auto;
}

.ai-suggestions {
    font-size: 12px;
    opacity: 0.9;
    line-height: 1.4;
}

/* 操作日志 */
.operation-log {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.log-title {
    font-size: 13px;
    font-weight: 600;
    color: #333;
}

.clear-log-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 11px;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
}

.clear-log-btn:hover {
    background: #e9ecef;
}

.log-content {
    max-height: 80px;
    overflow-y: auto;
}

.log-item {
    display: flex;
    gap: 8px;
    margin-bottom: 4px;
    font-size: 11px;
}

.log-time {
    color: #666;
    min-width: 30px;
}

.log-message {
    color: #333;
}

/* 底部按钮 */
.footer {
    display: flex;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

.footer-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: none;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    color: #666;
    transition: all 0.2s ease;
}

.footer-btn:hover {
    background: #e9ecef;
    color: #333;
}

.footer-btn .icon {
    font-size: 14px;
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.btn.primary {
    background: #667eea;
    color: white;
}

.btn.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 4px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
