/* MDAC AI智能分析工具 - 简洁2x2网格布局样式 */

/* 基础样式重置和现代化设计 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调系统 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* 背景色系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* 文字色系统 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

body.sidepanel-body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-secondary);
    overflow-x: hidden;
}

.sidepanel-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    position: relative;
}

/* 连接状态指示器 - 简化版 */
.connection-status {
    position: sticky;
    top: 0;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    text-align: center;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #dcfce7;
    color: var(--success-color);
    border-bottom-color: #bbf7d0;
}

/* 头部区域 - 简化版 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.ai-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 网站检测区域 - 简化版 */
.site-detection {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.detection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

/* ===== 核心2x2网格布局样式 ===== */

.grid-layout {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

/* 顶部区域：智能解析输入区 */
.top-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
}

/* 文本输入区域 */
.text-input-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.input-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.input-icon {
    font-size: 16px;
}

.input-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.smart-textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.smart-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.parse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.parse-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 图片上传区域 */
.image-input-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.upload-zone {
    flex: 1;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
}

.upload-zone:hover {
    border-color: var(--primary-color);
    background: rgb(37 99 235 / 0.05);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.upload-icon {
    font-size: 24px;
    color: var(--text-muted);
}

.upload-text {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
}

.upload-hint {
    font-size: 11px;
    color: var(--text-muted);
}

/* 中部区域：字段映射显示区 */
.middle-section {
    flex: 1;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.fields-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 100%;
}

/* 列样式 */
.personal-info-column,
.travel-info-column {
    padding: var(--spacing-md);
    overflow-y: auto;
}

.personal-info-column {
    border-right: 1px solid var(--border-color);
}

.column-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.column-icon {
    font-size: 16px;
}

.column-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 预设区域样式 */
.preset-section {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.preset-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.preset-header .preset-icon {
    font-size: 14px;
}

.preset-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.preset-edit-btn {
    background: none;
    border: none;
    font-size: 12px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 2px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.preset-edit-btn:hover {
    background: var(--bg-accent);
    color: var(--primary-color);
}

.preset-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.preset-field {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.preset-field label {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-secondary);
}

.preset-input {
    padding: 6px var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    background: var(--bg-primary);
    transition: border-color 0.2s ease;
}

.preset-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgb(37 99 235 / 0.1);
}

/* 字段组样式 */
.field-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.field-item {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.field-item label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.field-input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-family: inherit;
    background: var(--bg-primary);
    transition: all 0.2s ease;
}

.field-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.field-input:invalid {
    border-color: var(--error-color);
}

.field-input.filled {
    background: #dcfce7;
    border-color: var(--success-color);
}

.address-input {
    min-height: 60px;
    resize: vertical;
}

/* 字段状态指示器 */
.field-status {
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.field-status.success {
    background: var(--success-color);
    color: white;
    opacity: 1;
}

.field-status.error {
    background: var(--error-color);
    color: white;
    opacity: 1;
}

.field-status.warning {
    background: var(--warning-color);
    color: white;
    opacity: 1;
}

.field-status::after {
    content: '✓';
}

.field-status.error::after {
    content: '✗';
}

.field-status.warning::after {
    content: '!';
}

/* 底部区域：操作按钮区 */
.bottom-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    padding: var(--spacing-md);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* 主要按钮 */
.primary-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.primary-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.primary-action-btn:active {
    transform: translateY(0);
}

/* 辅助按钮组 */
.secondary-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.secondary-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 11px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 500;
}

/* 状态指示器 */
.status-indicators {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: var(--text-secondary);
}

.status-icon {
    font-size: 12px;
}

.status-text {
    font-weight: 500;
}

/* Footer样式 */
.footer {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: var(--text-muted);
}

.version {
    font-weight: 600;
}

.status {
    color: var(--success-color);
}

/* 隐藏的解析状态和结果区域 */
.parsing-status {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1000;
    min-width: 300px;
    text-align: center;
}

.status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.parse-results {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1000;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .input-grid {
        grid-template-columns: 1fr;
    }

    .fields-grid {
        grid-template-columns: 1fr;
    }

    .personal-info-column {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .secondary-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-indicators {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .grid-layout {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .input-grid,
    .fields-grid {
        padding: var(--spacing-sm);
    }

    .secondary-actions {
        grid-template-columns: 1fr;
    }
}

/* 连接状态的其他状态 */
.connection-status.disconnected {
    background: #fef3c7;
    color: var(--warning-color);
    border-bottom-color: #fde68a;
}

.connection-status.error {
    background: #fee2e2;
    color: var(--error-color);
    border-bottom-color: #fecaca;
}

/* ===== 工具类样式 ===== */

/* 显示/隐藏工具类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 动画工具类 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 成功/错误状态样式 */
.success {
    color: var(--success-color) !important;
}

.error {
    color: var(--error-color) !important;
}

.warning {
    color: var(--warning-color) !important;
}

/* 文本工具类 */
.text-center {
    text-align: center;
}

.text-muted {
    color: var(--text-muted);
}

.font-weight-bold {
    font-weight: 600;
}

/* 间距工具类 */
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }

/* ===== 文件结束 ===== */






































.btn.primary {
    background: #4285f4;
    color: white;
}

.btn.primary:hover {
    background: #3367d6;
}

.btn.secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn.secondary:hover {
    background: #f1f3f4;
}

.btn.info {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.btn.info:hover {
    background: #bbdefb;
}

/* 按钮组布局 */
.input-actions,
.supplement-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.input-actions .btn,
.supplement-actions .btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

/* 快速操作区域 */
.quick-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
}

.action-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    min-height: 60px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #4285f4, #1976d2);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.action-btn.secondary:hover {
    background: #f1f3f4;
}

/* 解析状态和结果 */
.parsing-status {
    background: #f0f8ff;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    border: 1px solid #e1e5e9;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4285f4, #1976d2);
    width: 0%;
    transition: width 0.3s ease;
}

/* 解析结果 */
.parse-results {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    margin-bottom: 16px;
    overflow: hidden;
}

.results-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.completeness-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.completeness-bar {
    width: 60px;
    height: 6px;
    background: #e1e5e9;
    border-radius: 3px;
    overflow: hidden;
}

.completeness-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #4caf50);
    width: 0%;
    transition: width 0.3s ease;
}

.extracted-data {
    padding: 16px;
}

.results-actions {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.results-actions .btn {
    flex: 1;
    min-width: 100px;
    justify-content: center;
}

/* AI功能列表 */
.ai-features {
    margin-bottom: 20px;
}

.ai-features h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
}

.feature-icon {
    font-size: 16px;
    width: 24px;
    text-align: center;
}

.feature-info {
    flex: 1;
}

.feature-info h4 {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 2px 0;
    color: #333;
}

.feature-info p {
    font-size: 11px;
    color: #666;
    margin: 0;
}

.feature-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ccc;
}

.feature-status.active {
    background: #4caf50;
}

/* AI助手区域 */
.ai-assistant {
    background: #f0f8ff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid #e1e5e9;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.ai-icon {
    font-size: 16px;
}

.ai-title {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.ai-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf50;
    margin-left: auto;
}

.ai-suggestions {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 底部区域 */
.footer {
    background: #f8f9fa;
    padding: 12px 16px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.footer-btn {
    flex: 1;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid #dadce0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #5f6368;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.footer-btn:hover {
    background: #f1f3f4;
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: #f8f9fa;
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    background: #f8f9fa;
    padding: 12px 16px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 400px) {
    .sidepanel-container {
        padding: 0;
    }
    
    .main-content {
        padding: 12px;
    }
    
    .input-area,
    .supplement-area {
        padding: 12px;
    }
    
    .input-actions,
    .supplement-actions {
        flex-direction: column;
    }
    
    .input-actions .btn,
    .supplement-actions .btn {
        width: 100%;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .results-actions {
        flex-direction: column;
    }
    
    .results-actions .btn {
        width: 100%;
    }
}

/* 紧凑模式 */
.sidepanel-container.compact-mode .input-area textarea,
.sidepanel-container.compact-mode .supplement-area textarea {
    min-height: 60px;
}

.sidepanel-container.compact-mode .action-btn {
    min-height: 50px;
    font-size: 12px;
}

.sidepanel-container.compact-mode .feature-item {
    padding: 8px;
}

/* 补充信息区域特定样式 */
.supplement-input-section {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
}

.supplement-header {
    background: #e3f2fd;
    border-bottom-color: #bbdefb;
}

.supplement-status .status-text {
    color: #1976d2;
}

/* 填充进度显示样式 */
.fill-progress {
    margin: 16px;
    padding: 16px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: progressSlideIn 0.3s ease-out;
}

@keyframes progressSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-title {
    font-size: 14px;
    font-weight: 600;
    color: #2e7d32;
}

.progress-percentage {
    font-size: 16px;
    font-weight: 700;
    color: #1b5e20;
}

.fill-progress .progress-bar {
    height: 6px;
    background: #c8e6c9;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 12px;
}

.fill-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #2e7d32);
    border-radius: 3px;
    transition: width 0.5s ease;
    animation: progressPulse 2s ease-in-out infinite;
}

@keyframes progressPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

.progress-details {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}

.progress-stat {
    font-size: 12px;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    color: #2e7d32;
    font-weight: 500;
    border: 1px solid rgba(46, 125, 50, 0.2);
}

.progress-stat:first-child {
    background: rgba(76, 175, 80, 0.1);
    border-color: rgba(76, 175, 80, 0.3);
}

.progress-stat:nth-child(2) {
    background: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.3);
    color: #c62828;
}

.progress-stat:nth-child(3) {
    background: rgba(255, 152, 0, 0.1);
    border-color: rgba(255, 152, 0, 0.3);
    color: #ef6c00;
}

.progress-stat:nth-child(4) {
    background: rgba(33, 150, 243, 0.1);
    border-color: rgba(33, 150, 243, 0.3);
    color: #1976d2;
}

/* 置信度评估样式 */
.confidence-section {
    margin: 16px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.confidence-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.confidence-header h3 {
    margin: 0;
    font-size: 16px;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.confidence-icon {
    font-size: 18px;
}

.overall-confidence {
    display: flex;
    align-items: center;
    gap: 8px;
}

.confidence-score {
    font-size: 18px;
    font-weight: 700;
}

.confidence-level {
    font-size: 12px;
    color: #6c757d;
}

.confidence-progress {
    margin: 12px 0;
}

.progress-bar-container {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 0.5s ease;
}

.confidence-summary {
    margin: 16px 0;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
}

.stat-item {
    text-align: center;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-item.high {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.stat-item.medium {
    border-color: #ffc107;
    background: rgba(255, 193, 7, 0.1);
}

.stat-item.low {
    border-color: #fd7e14;
    background: rgba(253, 126, 20, 0.1);
}

.stat-item.critical {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.stat-count {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.stat-label {
    display: block;
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

.confidence-issues,
.confidence-recommendations {
    margin: 12px 0;
    padding: 12px;
    border-radius: 6px;
}

.confidence-issues {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.confidence-recommendations {
    background: rgba(0, 123, 255, 0.1);
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.confidence-issues h4,
.confidence-recommendations h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #495057;
}

.issues-list,
.recommendations-list {
    margin: 0;
    padding-left: 16px;
}

.issues-list li,
.recommendations-list li {
    font-size: 13px;
    color: #495057;
    margin-bottom: 4px;
}

.confidence-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 16px;
}

.confidence-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.confidence-btn.primary {
    background: #007bff;
    color: white;
}

.confidence-btn.primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.confidence-btn.secondary {
    background: #6c757d;
    color: white;
}

.confidence-btn.secondary:hover {
    background: #545b62;
}

/* 数据字段样式 */
.data-section {
    margin: 16px;
    padding: 16px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e9ecef;
}

.data-header h3 {
    margin: 0;
    font-size: 16px;
    color: #495057;
}

.data-actions {
    display: flex;
    gap: 8px;
}

.data-btn {
    padding: 6px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.data-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.data-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
}

.data-field {
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.data-field:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.data-field.confidence-high {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.data-field.confidence-medium {
    border-color: #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.data-field.confidence-low {
    border-color: #fd7e14;
    background: rgba(253, 126, 20, 0.05);
}

.data-field.confidence-critical {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.field-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
}

.field-confidence {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
}

.field-value {
    font-size: 14px;
    color: #212529;
    margin-bottom: 6px;
}

.empty-value {
    color: #adb5bd;
    font-style: italic;
}

.field-issues {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.issue-tag {
    font-size: 10px;
    padding: 2px 6px;
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border-radius: 10px;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* 详细置信度分析模态框 */
.confidence-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10008;
    font-family: inherit;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 800px;
    max-height: 80%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 24px;
    height: 24px;
}

.modal-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.detailed-fields {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.detailed-field {
    padding: 16px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.detailed-field .field-header {
    margin-bottom: 8px;
}

.field-name {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.field-confidence {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    background: white;
}

.detailed-field .field-value {
    font-size: 13px;
    color: #212529;
    margin-bottom: 12px;
    padding: 8px;
    background: white;
    border-radius: 4px;
}

.field-details {
    margin-top: 12px;
}

.detail-scores {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 12px;
}

.score-item {
    text-align: center;
    padding: 6px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.score-label {
    display: block;
    font-size: 10px;
    color: #6c757d;
    margin-bottom: 2px;
}

.score-value {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #495057;
}

.field-issues h5,
.field-recommendations h5 {
    margin: 0 0 6px 0;
    font-size: 12px;
    color: #495057;
}

.field-issues ul,
.field-recommendations ul {
    margin: 0;
    padding-left: 16px;
}

.field-issues li,
.field-recommendations li {
    font-size: 11px;
    color: #495057;
    margin-bottom: 2px;
}

/* 响应式设计 */
@media (max-width: 400px) {
    .progress-details {
        justify-content: center;
    }

    .progress-stat {
        font-size: 11px;
        padding: 3px 6px;
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .data-fields {
        grid-template-columns: 1fr;
    }

    .detail-scores {
        grid-template-columns: repeat(3, 1fr);
    }
}
