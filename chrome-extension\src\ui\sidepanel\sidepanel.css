/* MDAC AI智能分析工具 - 简洁2x2网格布局样式 */

/* 基础样式重置和现代化设计 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调系统 */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* 背景色系统 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-accent: #e2e8f0;

    /* 文字色系统 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
}

body.sidepanel-body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-secondary);
    overflow-x: hidden;
}

.sidepanel-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
    position: relative;
}

/* 连接状态指示器 - 简化版 */
.connection-status {
    position: sticky;
    top: 0;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
    font-weight: 500;
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    text-align: center;
    transition: all 0.3s ease;
}

.connection-status.connected {
    background: #dcfce7;
    color: var(--success-color);
    border-bottom-color: #bbf7d0;
}

/* 头部区域 - 简化版 */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.ai-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 12px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 网站检测区域 - 简化版 */
.site-detection {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.detection-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 12px;
    color: var(--text-secondary);
}

/* ===== 核心2x2网格布局样式 ===== */

.grid-layout {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    min-height: 0; /* 允许flex子项收缩 */
    overflow-y: auto;
}

/* 顶部区域：智能解析输入区 */
.top-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    flex-shrink: 0; /* 防止顶部区域被压缩 */
}

.input-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
}

/* 文本输入区域 */
.text-input-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.input-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.input-icon {
    font-size: 16px;
}

.input-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.smart-textarea {
    width: 100%;
    min-height: 120px;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.smart-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.parse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.parse-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 图片上传区域 */
.image-input-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.upload-zone {
    flex: 1;
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--bg-secondary);
}

.upload-zone:hover {
    border-color: var(--primary-color);
    background: rgb(37 99 235 / 0.05);
}

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.upload-icon {
    font-size: 24px;
    color: var(--text-muted);
}

.upload-text {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
}

.upload-hint {
    font-size: 11px;
    color: var(--text-muted);
}

/* 中部区域：字段映射显示区 */
.middle-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 300px; /* 确保有足够的高度显示字段 */
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.fields-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    flex: 1;
    min-height: 0; /* 允许内容滚动 */
}

/* 列样式 */
.personal-info-column,
.travel-info-column {
    padding: var(--spacing-md);
    overflow-y: auto;
}

.personal-info-column {
    border-right: 1px solid var(--border-color);
}

.column-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.column-icon {
    font-size: 16px;
}

.column-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

/* 预设区域样式 */
.preset-section {
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.preset-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.preset-header .preset-icon {
    font-size: 14px;
}

.preset-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-primary);
}

.preset-edit-btn {
    background: none;
    border: none;
    font-size: 12px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 2px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.preset-edit-btn:hover {
    background: var(--bg-accent);
    color: var(--primary-color);
}

.preset-fields {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.preset-field {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.preset-field label {
    font-size: 11px;
    font-weight: 500;
    color: var(--text-secondary);
}

.preset-input {
    padding: 6px var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    background: var(--bg-primary);
    transition: border-color 0.2s ease;
}

.preset-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgb(37 99 235 / 0.1);
}

/* 字段组样式 */
.field-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.field-item {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.field-item label {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 2px;
}

.field-input {
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-family: inherit;
    background: var(--bg-primary);
    transition: all 0.2s ease;
}

.field-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.field-input:invalid {
    border-color: var(--error-color);
}

.field-input.filled {
    background: #dcfce7;
    border-color: var(--success-color);
}

.address-input {
    min-height: 60px;
    resize: vertical;
}

/* 字段状态指示器 */
.field-status {
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.field-status.success {
    background: var(--success-color);
    color: white;
    opacity: 1;
}

.field-status.error {
    background: var(--error-color);
    color: white;
    opacity: 1;
}

.field-status.warning {
    background: var(--warning-color);
    color: white;
    opacity: 1;
}

.field-status::after {
    content: '✓';
}

.field-status.error::after {
    content: '✗';
}

.field-status.warning::after {
    content: '!';
}

/* 底部区域：操作按钮区 */
.bottom-section {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    padding: var(--spacing-md);
    flex-shrink: 0; /* 防止底部区域被压缩 */
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

/* 主要按钮 */
.primary-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.primary-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.primary-action-btn:active {
    transform: translateY(0);
}

/* 辅助按钮组 */
.secondary-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
}

.secondary-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 11px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.secondary-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-weight: 500;
}

/* 状态指示器 */
.status-indicators {
    display: flex;
    justify-content: space-between;
    padding: var(--spacing-sm);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: var(--text-secondary);
}

.status-icon {
    font-size: 12px;
}

.status-text {
    font-weight: 500;
}

/* Footer样式 */
.footer {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

.footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: var(--text-muted);
}

.version {
    font-weight: 600;
}

.status {
    color: var(--success-color);
}

/* 隐藏的解析状态和结果区域 */
.parsing-status {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1000;
    min-width: 300px;
    text-align: center;
}

.status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.parse-results {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-primary);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    z-index: 1000;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .input-grid {
        grid-template-columns: 1fr;
    }

    .fields-grid {
        grid-template-columns: 1fr;
    }

    .personal-info-column {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .secondary-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .status-indicators {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .grid-layout {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .input-grid,
    .fields-grid {
        padding: var(--spacing-sm);
    }

    .secondary-actions {
        grid-template-columns: 1fr;
    }
}

/* 连接状态的其他状态 */
.connection-status.disconnected {
    background: #fef3c7;
    color: var(--warning-color);
    border-bottom-color: #fde68a;
}

.connection-status.error {
    background: #fee2e2;
    color: var(--error-color);
    border-bottom-color: #fecaca;
}

/* ===== 工具类样式 ===== */

/* 显示/隐藏工具类 */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* 动画工具类 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 成功/错误状态样式 */
.success {
    color: var(--success-color) !important;
}

.error {
    color: var(--error-color) !important;
}

.warning {
    color: var(--warning-color) !important;
}

/* 文本工具类 */
.text-center {
    text-align: center;
}

.text-muted {
    color: var(--text-muted);
}

.font-weight-bold {
    font-weight: 600;
}

/* 间距工具类 */
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }

/* ===== 缺失的基础样式 ===== */

/* 基础按钮样式 */
.btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all 0.2s ease;
    text-decoration: none;
    min-height: 36px;
    justify-content: center;
}

.btn.primary {
    background: var(--primary-color);
    color: white;
}

.btn.primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn.secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn.secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex !important;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--text-muted);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-accent);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-md);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* 消息提示系统 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    color: white;
    font-size: 13px;
    font-weight: 500;
    z-index: 10001;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: var(--shadow-lg);
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message.success {
    background: var(--success-color);
}

.message.error {
    background: var(--error-color);
}

.message.warning {
    background: var(--warning-color);
}

.message.info {
    background: var(--primary-color);
}

/* 响应式紧凑模式 */
.compact-mode .input-grid {
    grid-template-columns: 1fr;
}

.compact-mode .fields-grid {
    grid-template-columns: 1fr;
}

.compact-mode .personal-info-column {
    border-right: none;
    border-bottom: 1px solid var(--border-color);
}

.compact-mode .secondary-actions {
    grid-template-columns: repeat(2, 1fr);
}

.compact-mode .status-indicators {
    flex-direction: column;
    gap: var(--spacing-xs);
}



/* 检测状态样式 */
.detection-status.detected .icon {
    color: var(--success-color);
}

.detection-status.not-detected .icon {
    color: var(--warning-color);
}

.detection-status.error .icon {
    color: var(--error-color);
}

/* ===== 布局优化和调试样式 ===== */

/* 确保所有字段都可见 */
.field-group {
    margin-bottom: var(--spacing-md);
}

.field-item {
    margin-bottom: var(--spacing-sm);
}

/* 确保预设区域可见 */
.preset-section {
    margin-bottom: var(--spacing-md);
}

/* 优化列的最小高度 */
.personal-info-column,
.travel-info-column {
    min-height: 200px;
}

/* 确保输入区域有合适的高度 */
.smart-textarea {
    min-height: 80px;
    max-height: 120px;
}

.upload-zone {
    min-height: 100px;
}

/* 调试：为主要区域添加边框（可选，用于调试布局） */
/*
.top-section {
    border: 2px solid #ff0000 !important;
}

.middle-section {
    border: 2px solid #00ff00 !important;
}

.bottom-section {
    border: 2px solid #0000ff !important;
}

.personal-info-column {
    border: 1px solid #ff9900 !important;
}

.travel-info-column {
    border: 1px solid #9900ff !important;
}
*/

/* 确保网格布局在小屏幕下正常工作 */
@media (max-width: 600px) {
    .input-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .fields-grid {
        grid-template-columns: 1fr;
    }

    .personal-info-column {
        border-right: none;
        border-bottom: 1px solid var(--border-color);
        padding-bottom: var(--spacing-md);
    }

    .travel-info-column {
        padding-top: var(--spacing-md);
    }
}