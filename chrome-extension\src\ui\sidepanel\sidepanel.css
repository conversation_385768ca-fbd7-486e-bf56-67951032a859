/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 侧边栏特定样式 */
.sidepanel-body {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    font-family: 'Microsoft YaHei', 'Segoe UI', <PERSON>l, sans-serif;
    background: #f8f9fa;
    color: #333;
    font-size: 14px;
    line-height: 1.4;
    overflow-x: hidden;
}

.sidepanel-container {
    width: 100%;
    max-width: none;
    min-height: 100vh;
    padding: 0;
    background: white;
    display: flex;
    flex-direction: column;
}

/* 连接状态指示器 */
.connection-status {
    position: sticky;
    top: 0;
    background: #e3f2fd;
    color: #1976d2;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 500;
    border-bottom: 1px solid #bbdefb;
    z-index: 1000;
    text-align: center;
}

.connection-status.connected {
    background: #e8f5e8;
    color: #2e7d32;
    border-bottom-color: #c8e6c9;
}

.connection-status.disconnected {
    background: #fff3e0;
    color: #f57c00;
    border-bottom-color: #ffcc02;
}

.connection-status.error {
    background: #ffebee;
    color: #d32f2f;
    border-bottom-color: #ffcdd2;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

/* AI状态指示器 */
.ai-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4caf50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 网站检测区域 */
.site-detection {
    background: #f0f8ff;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

.detection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #666;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

/* 输入区域样式 */
.content-input-section,
.supplement-input-section {
    margin-bottom: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    overflow: hidden;
    background: white;
}

.input-header,
.supplement-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.input-header h3,
.supplement-header h3 {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.input-status,
.supplement-status {
    font-size: 11px;
    color: #666;
}

.input-area,
.supplement-area {
    padding: 16px;
}

.input-area label,
.supplement-area label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
    font-size: 13px;
}

.input-area textarea,
.supplement-area textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.input-area textarea:focus,
.supplement-area textarea:focus {
    outline: none;
    border-color: #4285f4;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
}

/* 图片上传区域 */
.image-upload-area {
    margin: 12px 0;
    padding: 16px;
    border: 2px dashed #ddd;
    border-radius: 6px;
    text-align: center;
    background: #fafafa;
}

.upload-btn {
    background: #4285f4;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
}

.upload-btn:hover {
    background: #3367d6;
}

.upload-hint {
    margin-top: 8px;
    font-size: 11px;
    color: #666;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    text-decoration: none;
    min-height: 36px;
}

.btn.primary {
    background: #4285f4;
    color: white;
}

.btn.primary:hover {
    background: #3367d6;
}

.btn.secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.btn.secondary:hover {
    background: #f1f3f4;
}

.btn.info {
    background: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.btn.info:hover {
    background: #bbdefb;
}

/* 按钮组布局 */
.input-actions,
.supplement-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.input-actions .btn,
.supplement-actions .btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
}

/* 快速操作区域 */
.quick-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
}

.action-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    min-height: 60px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #4285f4, #1976d2);
    color: white;
}

.action-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.action-btn.secondary:hover {
    background: #f1f3f4;
}

/* 解析状态和结果 */
.parsing-status {
    background: #f0f8ff;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    border: 1px solid #e1e5e9;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e1e5e9;
    border-top: 2px solid #4285f4;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4285f4, #1976d2);
    width: 0%;
    transition: width 0.3s ease;
}

/* 解析结果 */
.parse-results {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    margin-bottom: 16px;
    overflow: hidden;
}

.results-header {
    background: #f8f9fa;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0;
}

.completeness-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.completeness-bar {
    width: 60px;
    height: 6px;
    background: #e1e5e9;
    border-radius: 3px;
    overflow: hidden;
}

.completeness-fill {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #4caf50);
    width: 0%;
    transition: width 0.3s ease;
}

.extracted-data {
    padding: 16px;
}

.results-actions {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.results-actions .btn {
    flex: 1;
    min-width: 100px;
    justify-content: center;
}

/* AI功能列表 */
.ai-features {
    margin-bottom: 20px;
}

.ai-features h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e5e9;
}

.feature-icon {
    font-size: 16px;
    width: 24px;
    text-align: center;
}

.feature-info {
    flex: 1;
}

.feature-info h4 {
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 2px 0;
    color: #333;
}

.feature-info p {
    font-size: 11px;
    color: #666;
    margin: 0;
}

.feature-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ccc;
}

.feature-status.active {
    background: #4caf50;
}

/* AI助手区域 */
.ai-assistant {
    background: #f0f8ff;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid #e1e5e9;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.ai-icon {
    font-size: 16px;
}

.ai-title {
    font-weight: 600;
    font-size: 13px;
    color: #333;
}

.ai-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #4caf50;
    margin-left: auto;
}

.ai-suggestions {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 底部区域 */
.footer {
    background: #f8f9fa;
    padding: 12px 16px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.footer-btn {
    flex: 1;
    padding: 8px 12px;
    background: transparent;
    border: 1px solid #dadce0;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: #5f6368;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.footer-btn:hover {
    background: #f1f3f4;
}

/* 模态对话框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: #f8f9fa;
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 16px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    background: #f8f9fa;
    padding: 12px 16px;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 响应式设计 */
@media (max-width: 400px) {
    .sidepanel-container {
        padding: 0;
    }
    
    .main-content {
        padding: 12px;
    }
    
    .input-area,
    .supplement-area {
        padding: 12px;
    }
    
    .input-actions,
    .supplement-actions {
        flex-direction: column;
    }
    
    .input-actions .btn,
    .supplement-actions .btn {
        width: 100%;
    }
    
    .quick-actions {
        flex-direction: column;
    }
    
    .results-actions {
        flex-direction: column;
    }
    
    .results-actions .btn {
        width: 100%;
    }
}

/* 紧凑模式 */
.sidepanel-container.compact-mode .input-area textarea,
.sidepanel-container.compact-mode .supplement-area textarea {
    min-height: 60px;
}

.sidepanel-container.compact-mode .action-btn {
    min-height: 50px;
    font-size: 12px;
}

.sidepanel-container.compact-mode .feature-item {
    padding: 8px;
}

/* 补充信息区域特定样式 */
.supplement-input-section {
    background: #f0f8ff;
    border: 1px solid #b3d9ff;
}

.supplement-header {
    background: #e3f2fd;
    border-bottom-color: #bbdefb;
}

.supplement-status .status-text {
    color: #1976d2;
}
