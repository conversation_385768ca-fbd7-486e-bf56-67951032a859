<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>马来西亚数字入境卡(MDAC)自动填充工具</title>
    <style>
        /* 基础样式设置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .form-container {
            padding: 30px;
        }

        .section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
        }

        .section-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .required {
            color: #ff4757;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-container {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .note strong {
            display: block;
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .container {
                margin: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇲🇾 马来西亚数字入境卡自动填充工具</h1>
            <p>快速、准确地填充MDAC表单，节省您的时间</p>
        </div>

        <div class="form-container">
            <div class="note">
                <strong>使用说明：</strong>
                1. 填写下方表单中的所有必填信息<br>
                2. 点击"自动填充MDAC表单"按钮<br>
                3. 系统将自动打开官方网站并填充您的信息<br>
                4. 请在新窗口中检查信息并提交
            </div>

            <form id="mdacForm">
                <!-- 个人信息部分 -->
                <div class="section">
                    <h3 class="section-title">📋 个人信息</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">姓名 <span class="required">*</span></label>
                            <input type="text" id="name" name="name" required placeholder="请输入护照上的英文姓名">
                        </div>
                        <div class="form-group">
                            <label for="passportNo">护照号码 <span class="required">*</span></label>
                            <input type="text" id="passportNo" name="passportNo" required placeholder="例：A12345678">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="dateOfBirth">出生日期 <span class="required">*</span></label>
                            <input type="date" id="dateOfBirth" name="dateOfBirth" required>
                        </div>
                        <div class="form-group">
                            <label for="nationality">国籍 <span class="required">*</span></label>
                            <select id="nationality" name="nationality" required>
                                <option value="">请选择国籍</option>
                                <option value="CHINA">中国</option>
                                <option value="SINGAPORE">新加坡</option>
                                <option value="THAILAND">泰国</option>
                                <option value="INDONESIA">印度尼西亚</option>
                                <option value="PHILIPPINES">菲律宾</option>
                                <option value="VIETNAM">越南</option>
                                <option value="INDIA">印度</option>
                                <option value="JAPAN">日本</option>
                                <option value="SOUTH KOREA">韩国</option>
                                <option value="UNITED STATES">美国</option>
                                <option value="UNITED KINGDOM">英国</option>
                                <option value="AUSTRALIA">澳大利亚</option>
                                <option value="CANADA">加拿大</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="sex">性别 <span class="required">*</span></label>
                            <select id="sex" name="sex" required>
                                <option value="">请选择性别</option>
                                <option value="MALE">男性</option>
                                <option value="FEMALE">女性</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="passportExpiry">护照到期日期 <span class="required">*</span></label>
                            <input type="date" id="passportExpiry" name="passportExpiry" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">电子邮箱 <span class="required">*</span></label>
                            <input type="email" id="email" name="email" value="<EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label for="confirmEmail">确认电子邮箱 <span class="required">*</span></label>
                            <input type="email" id="confirmEmail" name="confirmEmail" value="<EMAIL>" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="countryCode">国家/地区代码 <span class="required">*</span></label>
                            <input type="text" id="countryCode" name="countryCode" value="+60" required>
                        </div>
                        <div class="form-group">
                            <label for="mobileNo">手机号码 <span class="required">*</span></label>
                            <input type="tel" id="mobileNo" name="mobileNo" value="167372551" required placeholder="不包含国家代码">
                        </div>
                    </div>
                </div>

                <!-- 旅行信息部分 -->
                <div class="section">
                    <h3 class="section-title">✈️ 旅行信息</h3>
                    <div class="note">
                        <strong>重要提醒：</strong>您的行程必须在3天内（包括提交日期）
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="arrivalDate">到达日期 <span class="required">*</span></label>
                            <input type="date" id="arrivalDate" name="arrivalDate" required>
                        </div>
                        <div class="form-group">
                            <label for="departureDate">离开日期 <span class="required">*</span></label>
                            <input type="date" id="departureDate" name="departureDate" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="flightNo">航班/船只/交通工具号码 <span class="required">*</span></label>
                            <input type="text" id="flightNo" name="flightNo" required placeholder="例：MH123">
                        </div>
                        <div class="form-group">
                            <label for="modeOfTravel">旅行方式 <span class="required">*</span></label>
                            <select id="modeOfTravel" name="modeOfTravel" required>
                                <option value="">请选择旅行方式</option>
                                <option value="AIR">飞机</option>
                                <option value="SEA">船只</option>
                                <option value="LAND">陆路</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="lastPort">到达马来西亚前的最后一个港口 <span class="required">*</span></label>
                        <input type="text" id="lastPort" name="lastPort" required placeholder="例：Singapore Changi Airport">
                    </div>

                    <div class="form-group">
                        <label for="accommodation">住宿安排 <span class="required">*</span></label>
                        <select id="accommodation" name="accommodation" required>
                            <option value="">请选择住宿类型</option>
                            <option value="HOTEL">酒店</option>
                            <option value="PRIVATE">私人住宅</option>
                            <option value="HOSTEL">青年旅社</option>
                            <option value="APARTMENT">公寓</option>
                            <option value="OTHERS">其他</option>
                        </select>
                    </div>
                </div>

                <!-- 马来西亚地址信息部分 -->
                <div class="section">
                    <h3 class="section-title">🏠 马来西亚地址信息</h3>
                    <div class="note">
                        <strong>注意：</strong>请只输入字母数字字符
                    </div>

                    <div class="form-group">
                        <label for="address">地址 <span class="required">*</span></label>
                        <textarea id="address" name="address" rows="3" required placeholder="请输入在马来西亚的详细地址"></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="state">州 <span class="required">*</span></label>
                            <select id="state" name="state" required>
                                <option value="">请选择州</option>
                                <option value="KUALA LUMPUR">吉隆坡</option>
                                <option value="SELANGOR">雪兰莪</option>
                                <option value="JOHOR">柔佛</option>
                                <option value="PENANG">槟城</option>
                                <option value="PERAK">霹雳</option>
                                <option value="NEGERI SEMBILAN">森美兰</option>
                                <option value="MALACCA">马六甲</option>
                                <option value="PAHANG">彭亨</option>
                                <option value="TERENGGANU">登嘉楼</option>
                                <option value="KELANTAN">吉兰丹</option>
                                <option value="KEDAH">吉打</option>
                                <option value="PERLIS">玻璃市</option>
                                <option value="SABAH">沙巴</option>
                                <option value="SARAWAK">砂拉越</option>
                                <option value="PUTRAJAYA">布城</option>
                                <option value="LABUAN">纳闽</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="postcode">邮政编码 <span class="required">*</span></label>
                            <input type="text" id="postcode" name="postcode" required placeholder="例：50000">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="city">城市 <span class="required">*</span></label>
                        <input type="text" id="city" name="city" required placeholder="例：Kuala Lumpur">
                    </div>
                </div>

                <div class="btn-container">
                    <button type="button" class="btn" onclick="validateAndFillForm()">🚀 自动填充MDAC表单</button>
                    <button type="button" class="btn" onclick="clearForm()">🗑️ 清空表单</button>
                </div>

                <div id="status" class="status"></div>
            </form>
        </div>
    </div>

    <script>
        // 全局变量定义
        let mdacWindow = null; // 存储打开的MDAC窗口引用
        
        /**
         * 显示状态信息
         * @param {string} message - 要显示的消息
         * @param {string} type - 消息类型：success, error, info
         */
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            // 3秒后自动隐藏状态信息
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        /**
         * 验证表单数据
         * @returns {boolean} 验证是否通过
         */
        function validateForm() {
            const form = document.getElementById('mdacForm');
            const formData = new FormData(form);
            
            // 检查必填字段
            const requiredFields = [
                'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex', 
                'passportExpiry', 'email', 'confirmEmail', 'countryCode', 'mobileNo',
                'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel', 
                'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
            ];
            
            for (let field of requiredFields) {
                if (!formData.get(field) || formData.get(field).trim() === '') {
                    showStatus(`请填写必填字段：${getFieldLabel(field)}`, 'error');
                    document.getElementById(field).focus();
                    return false;
                }
            }
            
            // 验证邮箱是否一致
            if (formData.get('email') !== formData.get('confirmEmail')) {
                showStatus('电子邮箱和确认邮箱不一致', 'error');
                return false;
            }
            
            // 验证日期逻辑
            const arrivalDate = new Date(formData.get('arrivalDate'));
            const departureDate = new Date(formData.get('departureDate'));
            const today = new Date();
            const threeDaysLater = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000);
            
            if (arrivalDate > threeDaysLater) {
                showStatus('到达日期必须在3天内（包括今天）', 'error');
                return false;
            }
            
            if (departureDate <= arrivalDate) {
                showStatus('离开日期必须晚于到达日期', 'error');
                return false;
            }
            
            return true;
        }

        /**
         * 获取字段的中文标签
         * @param {string} fieldName - 字段名称
         * @returns {string} 中文标签
         */
        function getFieldLabel(fieldName) {
            const labels = {
                'name': '姓名',
                'passportNo': '护照号码',
                'dateOfBirth': '出生日期',
                'nationality': '国籍',
                'sex': '性别',
                'passportExpiry': '护照到期日期',
                'email': '电子邮箱',
                'confirmEmail': '确认电子邮箱',
                'countryCode': '国家/地区代码',
                'mobileNo': '手机号码',
                'arrivalDate': '到达日期',
                'departureDate': '离开日期',
                'flightNo': '航班号',
                'modeOfTravel': '旅行方式',
                'lastPort': '最后港口',
                'accommodation': '住宿安排',
                'address': '地址',
                'state': '州',
                'postcode': '邮政编码',
                'city': '城市'
            };
            return labels[fieldName] || fieldName;
        }

        /**
         * 收集表单数据
         * @returns {Object} 表单数据对象
         */
        function collectFormData() {
            const form = document.getElementById('mdacForm');
            const formData = new FormData(form);
            const data = {};

            // 将FormData转换为普通对象
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            return data;
        }

        /**
         * 格式化日期为MDAC表单所需格式
         * @param {string} dateString - 日期字符串
         * @returns {string} 格式化后的日期
         */
        function formatDateForMDAC(dateString) {
            const date = new Date(dateString);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        /**
         * 主要的验证和填充函数
         */
        function validateAndFillForm() {
            showStatus('正在验证表单数据...', 'info');

            // 验证表单
            if (!validateForm()) {
                return;
            }

            // 收集数据
            const formData = collectFormData();

            // 格式化数据以匹配MDAC表单
            const mdacData = {
                // 个人信息
                name: formData.name.toUpperCase(),
                passportNo: formData.passportNo.toUpperCase(),
                dateOfBirth: formatDateForMDAC(formData.dateOfBirth),
                nationality: formData.nationality,
                sex: formData.sex,
                passportExpiry: formatDateForMDAC(formData.passportExpiry),
                email: formData.email.toLowerCase(),
                confirmEmail: formData.confirmEmail.toLowerCase(),
                countryCode: formData.countryCode,
                mobileNo: formData.mobileNo,

                // 旅行信息
                arrivalDate: formatDateForMDAC(formData.arrivalDate),
                departureDate: formatDateForMDAC(formData.departureDate),
                flightNo: formData.flightNo.toUpperCase(),
                modeOfTravel: formData.modeOfTravel,
                lastPort: formData.lastPort,
                accommodation: formData.accommodation,

                // 地址信息
                address: formData.address.toUpperCase(),
                state: formData.state,
                postcode: formData.postcode,
                city: formData.city.toUpperCase()
            };

            // 将数据存储到localStorage
            localStorage.setItem('mdacFormData', JSON.stringify(mdacData));

            showStatus('数据验证成功，正在打开MDAC网站...', 'success');

            // 打开MDAC网站
            openMDACWebsite();
        }

        /**
         * 打开MDAC网站并准备填充
         */
        function openMDACWebsite() {
            const mdacUrl = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';

            // 关闭之前打开的窗口（如果存在）
            if (mdacWindow && !mdacWindow.closed) {
                mdacWindow.close();
            }

            // 打开新窗口
            mdacWindow = window.open(mdacUrl, 'mdacWindow', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            if (!mdacWindow) {
                showStatus('无法打开新窗口，请检查浏览器弹窗设置', 'error');
                return;
            }

            showStatus('MDAC网站已打开，等待页面加载完成...', 'info');

            // 监听窗口加载完成
            let checkCount = 0;
            const maxChecks = 30; // 最多检查30次（30秒）

            const checkInterval = setInterval(() => {
                checkCount++;

                if (mdacWindow.closed) {
                    clearInterval(checkInterval);
                    showStatus('MDAC窗口已关闭', 'info');
                    return;
                }

                if (checkCount >= maxChecks) {
                    clearInterval(checkInterval);
                    showStatus('页面加载超时，请手动刷新MDAC页面', 'error');
                    return;
                }

                try {
                    // 检查页面是否加载完成
                    if (mdacWindow.document && mdacWindow.document.readyState === 'complete') {
                        clearInterval(checkInterval);

                        // 延迟2秒后注入脚本，确保页面完全渲染
                        setTimeout(() => {
                            injectFillScript();
                        }, 2000);
                    }
                } catch (e) {
                    // 跨域错误，继续等待
                    console.log('等待页面加载...', e.message);
                }
            }, 1000);
        }

        /**
         * 向MDAC页面注入填充脚本
         */
        function injectFillScript() {
            if (!mdacWindow || mdacWindow.closed) {
                showStatus('MDAC窗口已关闭', 'error');
                return;
            }

            try {
                // 创建填充脚本
                const fillScript = `
                    (function() {
                        console.log('MDAC自动填充脚本已注入');

                        // 从localStorage获取数据
                        const mdacDataStr = localStorage.getItem('mdacFormData');
                        if (!mdacDataStr) {
                            alert('未找到填充数据，请重新操作');
                            return;
                        }

                        const mdacData = JSON.parse(mdacDataStr);
                        console.log('获取到填充数据:', mdacData);

                        // 填充函数
                        function fillField(selector, value) {
                            const element = document.querySelector(selector);
                            if (element) {
                                if (element.type === 'select-one') {
                                    // 处理下拉选择框
                                    for (let option of element.options) {
                                        if (option.value === value || option.text.includes(value)) {
                                            element.value = option.value;
                                            break;
                                        }
                                    }
                                } else {
                                    // 处理输入框
                                    element.value = value;
                                }

                                // 触发change事件
                                element.dispatchEvent(new Event('change', { bubbles: true }));
                                element.dispatchEvent(new Event('input', { bubbles: true }));

                                console.log('已填充字段:', selector, '值:', value);
                                return true;
                            } else {
                                console.warn('未找到字段:', selector);
                                return false;
                            }
                        }

                        // 等待页面元素完全加载
                        setTimeout(() => {
                            let filledCount = 0;

                            // 填充个人信息（这里需要根据实际的MDAC表单字段名称进行调整）
                            if (fillField('input[name="name"]', mdacData.name)) filledCount++;
                            if (fillField('input[name="passportNo"]', mdacData.passportNo)) filledCount++;
                            if (fillField('input[name="dateOfBirth"]', mdacData.dateOfBirth)) filledCount++;
                            if (fillField('select[name="nationality"]', mdacData.nationality)) filledCount++;
                            if (fillField('select[name="sex"]', mdacData.sex)) filledCount++;
                            if (fillField('input[name="passportExpiry"]', mdacData.passportExpiry)) filledCount++;
                            if (fillField('input[name="email"]', mdacData.email)) filledCount++;
                            if (fillField('input[name="confirmEmail"]', mdacData.confirmEmail)) filledCount++;
                            if (fillField('input[name="countryCode"]', mdacData.countryCode)) filledCount++;
                            if (fillField('input[name="mobileNo"]', mdacData.mobileNo)) filledCount++;

                            // 填充旅行信息
                            if (fillField('input[name="arrivalDate"]', mdacData.arrivalDate)) filledCount++;
                            if (fillField('input[name="departureDate"]', mdacData.departureDate)) filledCount++;
                            if (fillField('input[name="flightNo"]', mdacData.flightNo)) filledCount++;
                            if (fillField('select[name="modeOfTravel"]', mdacData.modeOfTravel)) filledCount++;
                            if (fillField('input[name="lastPort"]', mdacData.lastPort)) filledCount++;
                            if (fillField('select[name="accommodation"]', mdacData.accommodation)) filledCount++;

                            // 填充地址信息
                            if (fillField('textarea[name="address"]', mdacData.address)) filledCount++;
                            if (fillField('select[name="state"]', mdacData.state)) filledCount++;
                            if (fillField('input[name="postcode"]', mdacData.postcode)) filledCount++;
                            if (fillField('input[name="city"]', mdacData.city)) filledCount++;

                            alert('自动填充完成！已填充 ' + filledCount + ' 个字段。\\n\\n请检查所有信息是否正确，然后点击提交按钮。');

                            // 清理localStorage中的数据
                            localStorage.removeItem('mdacFormData');

                        }, 1000);

                    })();
                `;

                // 注入脚本到目标页面
                const script = mdacWindow.document.createElement('script');
                script.textContent = fillScript;
                mdacWindow.document.head.appendChild(script);

                showStatus('自动填充脚本已注入，请查看MDAC窗口', 'success');

            } catch (error) {
                console.error('注入脚本失败:', error);
                showStatus('由于跨域限制，无法自动填充。请手动复制数据到MDAC表单。', 'error');

                // 显示数据供用户手动复制
                displayDataForManualCopy();
            }
        }

        /**
         * 显示数据供用户手动复制
         */
        function displayDataForManualCopy() {
            const data = JSON.parse(localStorage.getItem('mdacFormData'));
            if (!data) return;

            let copyText = '=== MDAC表单填充数据 ===\\n\\n';
            copyText += '个人信息:\\n';
            copyText += `姓名: ${data.name}\\n`;
            copyText += `护照号码: ${data.passportNo}\\n`;
            copyText += `出生日期: ${data.dateOfBirth}\\n`;
            copyText += `国籍: ${data.nationality}\\n`;
            copyText += `性别: ${data.sex}\\n`;
            copyText += `护照到期日期: ${data.passportExpiry}\\n`;
            copyText += `电子邮箱: ${data.email}\\n`;
            copyText += `确认邮箱: ${data.confirmEmail}\\n`;
            copyText += `国家代码: ${data.countryCode}\\n`;
            copyText += `手机号码: ${data.mobileNo}\\n\\n`;

            copyText += '旅行信息:\\n';
            copyText += `到达日期: ${data.arrivalDate}\\n`;
            copyText += `离开日期: ${data.departureDate}\\n`;
            copyText += `航班号: ${data.flightNo}\\n`;
            copyText += `旅行方式: ${data.modeOfTravel}\\n`;
            copyText += `最后港口: ${data.lastPort}\\n`;
            copyText += `住宿安排: ${data.accommodation}\\n\\n`;

            copyText += '地址信息:\\n';
            copyText += `地址: ${data.address}\\n`;
            copyText += `州: ${data.state}\\n`;
            copyText += `邮政编码: ${data.postcode}\\n`;
            copyText += `城市: ${data.city}\\n`;

            // 创建一个临时的文本区域来复制数据
            const textArea = document.createElement('textarea');
            textArea.value = copyText;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);

            alert('数据已复制到剪贴板，请手动粘贴到MDAC表单中。');
        }

        /**
         * 清空表单
         */
        function clearForm() {
            if (confirm('确定要清空所有表单数据吗？')) {
                document.getElementById('mdacForm').reset();

                // 恢复默认值
                document.getElementById('email').value = '<EMAIL>';
                document.getElementById('confirmEmail').value = '<EMAIL>';
                document.getElementById('countryCode').value = '+60';
                document.getElementById('mobileNo').value = '167372551';

                showStatus('表单已清空', 'info');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认的到达日期为明天
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('arrivalDate').value = tomorrow.toISOString().split('T')[0];

            // 设置默认的离开日期为后天
            const dayAfterTomorrow = new Date();
            dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);
            document.getElementById('departureDate').value = dayAfterTomorrow.toISOString().split('T')[0];

            // 设置护照到期日期的最小值为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('passportExpiry').min = today;

            showStatus('MDAC自动填充工具已就绪', 'success');
        });
    </script>
</body>
</html>
