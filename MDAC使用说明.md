# MDAC AI智能填充工具使用说明

## 🎯 工具概述

这是一个集成了Gemini AI的马来西亚数字入境卡(MDAC)智能填充工具，提供AI驱动的智能建议、实时验证、自动翻译和多种填充方案，让入境卡填写更简单、更准确。

## 🤖 AI增强功能

- ✅ **Gemini AI驱动** - 集成Google Gemini 2.5 Flash模型
- ✅ **智能表单建议** - AI分析输入并提供智能建议
- ✅ **实时数据验证** - AI实时验证数据格式和合理性
- ✅ **智能纠错功能** - 自动检测并提供纠错建议
- ✅ **中英文翻译** - AI智能翻译地址信息
- ✅ **旅行信息推荐** - 基于输入提供个性化建议
- ✅ **表单优化分析** - AI全面分析表单质量

## 📋 基础功能特性

- ✅ **智能表单验证** - 自动检查必填字段和数据格式
- ✅ **预设默认值** - 常用信息已预填充
- ✅ **日期格式转换** - 自动转换为MDAC要求的日期格式
- ✅ **跨域安全处理** - 安全地与官方网站交互
- ✅ **错误处理机制** - 完善的错误提示和状态反馈
- ✅ **响应式设计** - 支持手机和电脑使用

## 🚀 使用步骤

### 第一步：打开工具

1. 双击 `mdac-ai-enhanced.html` 文件
2. 工具将在您的默认浏览器中打开
3. 您将看到AI助手面板显示"准备就绪"状态

### 第二步：选择填写模式

工具提供三种填写模式：

#### 🤖 AI智能表单（推荐）

- **智能建议**: AI会根据您的输入提供实时建议
- **自动验证**: 输入时自动验证数据格式和合理性
- **智能翻译**: 中文地址自动翻译为英文
- **优化分析**: AI全面分析表单质量

#### 📖 书签工具法

- **AI增强书签**: 集成智能验证功能的书签工具
- **一键填充**: 在MDAC官网一键填充所有信息
- **跨域兼容**: 不受浏览器限制影响

#### 📋 手动复制法

- **100%兼容**: 适用于所有情况
- **AI验证状态**: 显示每个字段的AI验证结果
- **格式化数据**: 自动格式化为标准格式

### 第三步：AI智能填写（推荐模式）

#### 🤖 AI助手功能使用

**实时AI建议**：
- 输入时AI会自动分析并提供建议
- 绿色边框表示AI验证通过
- 红色边框表示需要检查或修改
- 鼠标悬停查看详细AI建议

**AI帮助按钮**：
- 点击各部分的"AI建议"按钮获取专业建议
- AI会根据当前填写情况提供个性化建议

**智能翻译功能**：
- 地址栏支持中文输入
- 点击"🌐 翻译"按钮自动翻译为英文
- AI确保翻译结果符合MDAC格式要求

#### 📋 表单填写指南

**个人信息部分**：

- **姓名**: 输入护照上的英文姓名，AI会验证格式
- **护照号码**: AI会检查护照号格式和长度
- **出生日期**: 选择日期，AI会验证合理性
- **国籍**: 选择国籍，AI会提供相关建议
- **性别**: 选择性别
- **护照到期日期**: AI会检查是否在有效期内
- **电子邮箱**: AI会验证邮箱格式
- **确认电子邮箱**: AI会检查是否与上面一致
- **国家/地区代码**: 已预设为+60
- **手机号码**: AI会验证号码格式

**旅行信息部分**：

- **到达日期**: AI会检查是否在3天内
- **离开日期**: AI会验证日期逻辑
- **航班号**: AI会识别航空公司并提供信息
- **旅行方式**: 选择交通方式
- **最后港口**: AI会验证港口名称有效性
- **住宿安排**: AI会根据行程推荐住宿类型

**地址信息部分**：

- **地址**: 支持中文输入，AI自动翻译
- **州**: 选择马来西亚的州
- **邮政编码**: AI会验证邮编格式
- **城市**: AI会验证城市名称

#### ✨ AI优化功能

**表单优化分析**：
- 点击"✨ AI优化建议"获取全面分析
- AI会检查数据一致性和完整性
- 提供改进建议和潜在问题提醒

### 第四步：智能验证和填充

1. 点击 **"🚀 AI智能填充"** 按钮
2. AI会进行全面的表单验证
3. 验证通过后，数据会自动保存
4. 系统会切换到书签工具模式

### 第五步：使用书签工具填充

1. 复制AI增强书签代码到浏览器书签栏
2. 打开MDAC官方网站
3. 在官网页面点击创建的书签
4. AI增强脚本会自动填充所有信息

### 第六步：检查并提交

1. 检查MDAC官网上的所有填充信息
2. 确认信息准确无误
3. 点击官方网站的提交按钮
4. 完成入境卡申请

## ⚠️ 重要注意事项

### 时间限制
- **行程必须在3天内**（包括提交当天）
- 建议在出发前1-2天填写

### 数据格式要求
- **姓名**: 必须与护照完全一致
- **地址**: 只能包含字母和数字字符
- **日期**: 系统会自动转换为DD/MM/YYYY格式

### 浏览器兼容性
- 推荐使用 **Chrome**、**Firefox** 或 **Edge** 浏览器
- 确保浏览器允许弹出窗口
- 建议关闭广告拦截器

### 网络要求
- 需要稳定的互联网连接
- 确保能够访问马来西亚政府网站

## 🔧 故障排除

### 问题1：无法打开新窗口
**解决方案**：
- 检查浏览器弹窗设置，允许本网站打开弹窗
- 临时关闭弹窗拦截器

### 问题2：自动填充失败
**解决方案**：
- 工具会自动将数据复制到剪贴板
- 您可以手动粘贴到MDAC表单中
- 检查网络连接是否稳定

### 问题3：表单验证失败
**解决方案**：
- 检查所有必填字段是否已填写
- 确认邮箱地址一致
- 验证日期逻辑是否正确

### 问题4：页面加载超时
**解决方案**：
- 刷新MDAC官方网站页面
- 检查网络连接
- 重新点击"自动填充"按钮

## 📱 移动设备使用

工具支持手机和平板电脑使用：
- 界面会自动适配屏幕尺寸
- 建议使用横屏模式以获得更好体验
- 确保移动浏览器支持JavaScript

## 🔒 隐私和安全

- **本地处理**: 所有数据处理都在您的设备上进行
- **不存储数据**: 工具不会永久存储您的个人信息
- **安全传输**: 仅与官方MDAC网站交互
- **临时存储**: 数据仅在填充过程中临时存储在浏览器中

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **检查使用说明**: 确保按照步骤正确操作
2. **查看故障排除**: 参考上述常见问题解决方案
3. **浏览器控制台**: 按F12查看是否有错误信息
4. **手动填写**: 如果自动填充失败，可以手动复制数据

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 基础自动填充功能
- ✅ 表单验证和错误处理
- ✅ 响应式界面设计
- ✅ 跨域安全处理
- ✅ 移动设备支持

## 🎉 使用技巧

1. **提前准备**: 在使用前准备好所有必要文件（护照、行程单等）
2. **网络环境**: 选择网络稳定的环境使用
3. **浏览器设置**: 使用无痕模式可以避免缓存问题
4. **备份数据**: 建议截图保存填写的信息作为备份

---

**祝您使用愉快！如有任何问题，请参考故障排除部分或联系技术支持。**
