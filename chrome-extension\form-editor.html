<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC表单编辑器 - AI智能填充工具</title>
    <link rel="stylesheet" href="form-editor.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                <img src="icons/icon48.png" alt="MDAC AI">
                <h1>MDAC表单编辑器</h1>
            </div>
            <div class="header-actions">
                <button class="btn secondary" id="loadTemplateBtn">📂 加载模板</button>
                <button class="btn secondary" id="saveTemplateBtn">💾 保存模板</button>
                <button class="btn primary" id="fillFormBtn">🚀 填充表单</button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 侧边栏 -->
            <div class="sidebar">
                <div class="ai-assistant">
                    <div class="ai-header">
                        <span class="ai-icon">🤖</span>
                        <span class="ai-title">AI助手</span>
                    </div>
                    <div class="ai-content">
                        <div class="ai-suggestions" id="aiSuggestions">
                            准备就绪，等待您的指令...
                        </div>
                        <div class="ai-actions">
                            <button class="ai-btn" id="aiValidateBtn">验证表单</button>
                            <button class="ai-btn" id="aiOptimizeBtn">优化建议</button>
                            <button class="ai-btn" id="aiTranslateBtn">智能翻译</button>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>快速操作</h3>
                    <div class="action-list">
                        <button class="action-item" id="contentParseBtn">
                            <span class="icon">🧠</span>
                            <span class="text">智能解析</span>
                        </button>
                        <button class="action-item" id="autoFillBtn">
                            <span class="icon">⚡</span>
                            <span class="text">自动填充</span>
                        </button>
                        <button class="action-item" id="clearFormBtn">
                            <span class="icon">🗑️</span>
                            <span class="text">清空表单</span>
                        </button>
                        <button class="action-item" id="randomDataBtn">
                            <span class="icon">🎲</span>
                            <span class="text">随机数据</span>
                        </button>
                        <button class="action-item" id="exportDataBtn">
                            <span class="icon">📤</span>
                            <span class="text">导出数据</span>
                        </button>
                    </div>
                </div>

                <!-- 智能内容解析面板 -->
                <div class="content-parser-panel" id="contentParserPanel" style="display: none;">
                    <div class="parser-panel-header">
                        <h3>🧠 智能内容解析</h3>
                        <button class="close-panel-btn" id="closeParsePanelBtn">×</button>
                    </div>

                    <div class="parser-panel-content">
                        <textarea
                            id="contentParseInput"
                            placeholder="粘贴任意格式的内容，AI将自动提取并填充表单...&#10;&#10;支持：护照信息、酒店预订、航班信息、个人资料等"
                            rows="8">
                        </textarea>

                        <div class="parser-panel-actions">
                            <button class="btn secondary" id="clearParseInputBtn">清空</button>
                            <button class="btn primary" id="startParseBtn">
                                <span class="parse-btn-text">开始解析</span>
                                <span class="parse-btn-loading" style="display: none;">解析中...</span>
                            </button>
                        </div>

                        <div class="parse-status" id="parseStatus" style="display: none;">
                            <div class="status-message" id="parseStatusMessage">正在解析内容...</div>
                            <div class="status-progress">
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill" id="parseProgressBar"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="validation-status">
                    <h3>验证状态</h3>
                    <div class="status-list" id="validationStatus">
                        <div class="status-item pending">
                            <span class="icon">⏳</span>
                            <span class="text">等待验证</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单区域 -->
            <div class="form-area">
                <form id="mdacForm" class="mdac-form">
                    <!-- 个人信息部分 -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>👤 个人信息</h2>
                            <div class="section-progress">
                                <span class="progress-text">0/10</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="name">姓名 *</label>
                                <input type="text" id="name" name="name" placeholder="护照上的英文姓名" required>
                                <div class="field-hint">请输入护照上的完整英文姓名</div>
                            </div>

                            <div class="form-group">
                                <label for="passportNo">护照号码 *</label>
                                <input type="text" id="passportNo" name="passportNo" placeholder="护照号码" required>
                                <div class="field-hint">输入有效的护照号码</div>
                            </div>

                            <div class="form-group">
                                <label for="dateOfBirth">出生日期 *</label>
                                <input type="date" id="dateOfBirth" name="dateOfBirth" required>
                                <div class="field-hint">选择您的出生日期</div>
                            </div>

                            <div class="form-group">
                                <label for="nationality">国籍 *</label>
                                <select id="nationality" name="nationality" required>
                                    <option value="">请选择国籍</option>
                                    <option value="CHN">中国 (CHN)</option>
                                    <option value="USA">美国 (USA)</option>
                                    <option value="SGP">新加坡 (SGP)</option>
                                    <option value="THA">泰国 (THA)</option>
                                    <option value="IDN">印度尼西亚 (IDN)</option>
                                    <option value="VNM">越南 (VNM)</option>
                                    <option value="PHL">菲律宾 (PHL)</option>
                                    <option value="IND">印度 (IND)</option>
                                    <option value="JPN">日本 (JPN)</option>
                                    <option value="KOR">韩国 (KOR)</option>
                                </select>
                                <div class="field-hint">选择您的国籍</div>
                            </div>

                            <div class="form-group">
                                <label for="sex">性别 *</label>
                                <select id="sex" name="sex" required>
                                    <option value="">请选择性别</option>
                                    <option value="1">男性</option>
                                    <option value="2">女性</option>
                                </select>
                                <div class="field-hint">选择您的性别</div>
                            </div>

                            <div class="form-group">
                                <label for="passportExpiry">护照到期日期 *</label>
                                <input type="date" id="passportExpiry" name="passportExpiry" required>
                                <div class="field-hint">护照必须在入境时有效期6个月以上</div>
                            </div>

                            <div class="form-group">
                                <label for="email">电子邮箱 *</label>
                                <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
                                <div class="field-hint">输入有效的电子邮箱地址</div>
                            </div>

                            <div class="form-group">
                                <label for="confirmEmail">确认邮箱 *</label>
                                <input type="email" id="confirmEmail" name="confirmEmail" placeholder="确认邮箱地址" required>
                                <div class="field-hint">重新输入邮箱地址确认</div>
                            </div>

                            <div class="form-group">
                                <label for="countryCode">国家代码 *</label>
                                <select id="countryCode" name="countryCode" required>
                                    <option value="">选择国家代码</option>
                                    <option value="+60">马来西亚 (+60)</option>
                                    <option value="+86">中国 (+86)</option>
                                    <option value="+1">美国 (+1)</option>
                                    <option value="+65">新加坡 (+65)</option>
                                    <option value="+66">泰国 (+66)</option>
                                    <option value="+62">印度尼西亚 (+62)</option>
                                    <option value="+84">越南 (+84)</option>
                                    <option value="+63">菲律宾 (+63)</option>
                                    <option value="+91">印度 (+91)</option>
                                    <option value="+81">日本 (+81)</option>
                                    <option value="+82">韩国 (+82)</option>
                                </select>
                                <div class="field-hint">选择手机号的国家代码</div>
                            </div>

                            <div class="form-group">
                                <label for="mobileNo">手机号码 *</label>
                                <input type="tel" id="mobileNo" name="mobileNo" placeholder="手机号码（不含国家代码）" required>
                                <div class="field-hint">输入不含国家代码的手机号码</div>
                            </div>
                        </div>
                    </div>

                    <!-- 旅行信息部分 -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>✈️ 旅行信息</h2>
                            <div class="section-progress">
                                <span class="progress-text">0/6</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="arrivalDate">到达日期 *</label>
                                <input type="date" id="arrivalDate" name="arrivalDate" required>
                                <div class="field-hint">选择您到达马来西亚的日期</div>
                            </div>

                            <div class="form-group">
                                <label for="departureDate">离开日期 *</label>
                                <input type="date" id="departureDate" name="departureDate" required>
                                <div class="field-hint">选择您离开马来西亚的日期</div>
                            </div>

                            <div class="form-group">
                                <label for="flightNo">航班/交通工具号码 *</label>
                                <input type="text" id="flightNo" name="flightNo" placeholder="如: MH123" required>
                                <div class="field-hint">输入航班号、船只号或其他交通工具编号</div>
                            </div>

                            <div class="form-group">
                                <label for="modeOfTravel">旅行方式 *</label>
                                <select id="modeOfTravel" name="modeOfTravel" required>
                                    <option value="">请选择旅行方式</option>
                                    <option value="AIR">航空</option>
                                    <option value="LAND">陆路</option>
                                    <option value="SEA">海路</option>
                                </select>
                                <div class="field-hint">选择您的主要旅行方式</div>
                            </div>

                            <div class="form-group">
                                <label for="lastPort">最后港口 *</label>
                                <select id="lastPort" name="lastPort" required>
                                    <option value="">请选择最后港口</option>
                                    <option value="SGP">新加坡</option>
                                    <option value="THA">泰国</option>
                                    <option value="IDN">印度尼西亚</option>
                                    <option value="CHN">中国</option>
                                    <option value="VNM">越南</option>
                                    <option value="PHL">菲律宾</option>
                                    <option value="IND">印度</option>
                                    <option value="JPN">日本</option>
                                    <option value="KOR">韩国</option>
                                </select>
                                <div class="field-hint">选择到达马来西亚前的最后一个港口/国家</div>
                            </div>

                            <div class="form-group">
                                <label for="accommodation">住宿安排 *</label>
                                <select id="accommodation" name="accommodation" required>
                                    <option value="">请选择住宿类型</option>
                                    <option value="01">酒店/汽车旅馆/休息屋</option>
                                    <option value="02">朋友/亲戚家</option>
                                    <option value="99">其他</option>
                                </select>
                                <div class="field-hint">选择您在马来西亚的住宿安排</div>
                            </div>
                        </div>
                    </div>

                    <!-- 地址信息部分 -->
                    <div class="form-section">
                        <div class="section-header">
                            <h2>🏠 马来西亚地址信息</h2>
                            <div class="section-progress">
                                <span class="progress-text">0/5</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="address">地址行1 *</label>
                                <input type="text" id="address" name="address" placeholder="详细地址" required>
                                <div class="field-hint">输入在马来西亚的详细地址</div>
                            </div>

                            <div class="form-group full-width">
                                <label for="address2">地址行2</label>
                                <input type="text" id="address2" name="address2" placeholder="补充地址信息（可选）">
                                <div class="field-hint">可选的补充地址信息</div>
                            </div>

                            <div class="form-group">
                                <label for="state">州 *</label>
                                <select id="state" name="state" required>
                                    <option value="">请选择州</option>
                                    <option value="14">吉隆坡联邦直辖区</option>
                                    <option value="15">纳闽联邦直辖区</option>
                                    <option value="16">布城联邦直辖区</option>
                                    <option value="01">柔佛</option>
                                    <option value="02">吉打</option>
                                    <option value="03">吉兰丹</option>
                                    <option value="04">马六甲</option>
                                    <option value="05">森美兰</option>
                                    <option value="06">彭亨</option>
                                    <option value="07">槟城</option>
                                    <option value="08">霹雳</option>
                                    <option value="09">玻璃市</option>
                                    <option value="10">雪兰莪</option>
                                    <option value="11">登嘉楼</option>
                                    <option value="12">沙巴</option>
                                    <option value="13">砂拉越</option>
                                </select>
                                <div class="field-hint">选择您在马来西亚的州</div>
                            </div>

                            <div class="form-group">
                                <label for="postcode">邮政编码 *</label>
                                <input type="text" id="postcode" name="postcode" placeholder="邮政编码" required>
                                <div class="field-hint">输入5位数字的邮政编码</div>
                            </div>

                            <div class="form-group">
                                <label for="city">城市 *</label>
                                <select id="city" name="city" required>
                                    <option value="">请先选择州</option>
                                </select>
                                <div class="field-hint">根据所选州显示可用城市</div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn secondary" id="resetBtn">重置表单</button>
                        <button type="button" class="btn secondary" id="previewBtn">预览数据</button>
                        <button type="submit" class="btn primary" id="submitBtn">提交表单</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-info">
                <span class="status-text" id="statusText">表单就绪</span>
                <span class="field-count" id="fieldCount">已填写: 0/21</span>
            </div>
            <div class="status-actions">
                <button class="status-btn" id="autoSaveBtn">
                    <span class="icon">💾</span>
                    <span class="text">自动保存</span>
                </button>
                <button class="status-btn" id="helpBtn">
                    <span class="icon">❓</span>
                    <span class="text">帮助</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 模态对话框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">标题</h3>
                <button class="modal-close" id="modalClose">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 动态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn secondary" id="modalCancel">取消</button>
                <button class="btn primary" id="modalConfirm">确认</button>
            </div>
        </div>
    </div>

    <script src="form-editor.js"></script>
</body>
</html>
