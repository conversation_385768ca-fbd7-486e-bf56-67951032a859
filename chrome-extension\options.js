/**
 * MDAC AI智能填充工具 - 设置页面脚本
 * 处理扩展的设置和配置管理
 */

class MDACOptions {
    constructor() {
        this.settings = {};
        this.init();
    }

    /**
     * 初始化设置页面
     */
    async init() {
        this.setupEventListeners();
        await this.loadSettings();
        this.updateUI();
        this.updateDataStats();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 标签切换
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // 设置保存
        document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
        document.getElementById('resetSettings').addEventListener('click', () => this.resetSettings());

        // AI配置
        document.getElementById('toggleApiKey').addEventListener('click', () => this.toggleApiKeyVisibility());
        document.getElementById('testAI').addEventListener('click', () => this.testAIConnection());

        // 模板管理
        document.getElementById('saveTemplate').addEventListener('click', () => this.saveTemplate());
        document.getElementById('loadTemplate').addEventListener('click', () => this.loadTemplate());
        document.getElementById('clearTemplate').addEventListener('click', () => this.clearTemplate());

        // 数据管理
        document.getElementById('exportData').addEventListener('click', () => this.exportData());
        document.getElementById('importData').addEventListener('click', () => this.importData());
        document.getElementById('clearHistory').addEventListener('click', () => this.clearHistory());
        document.getElementById('clearAllData').addEventListener('click', () => this.clearAllData());

        // 文件输入
        document.getElementById('fileInput').addEventListener('change', (e) => this.handleFileImport(e));

        // 链接按钮
        document.getElementById('helpLink').addEventListener('click', () => this.openHelp());
        document.getElementById('feedbackLink').addEventListener('click', () => this.openFeedback());
        document.getElementById('updateLink').addEventListener('click', () => this.checkUpdate());

        // 实时保存某些设置
        document.getElementById('defaultMode').addEventListener('change', () => this.autoSave());
        document.getElementById('aiEnabled').addEventListener('change', () => this.autoSave());
        document.getElementById('encryptData').addEventListener('change', () => this.autoSave());
    }

    /**
     * 切换标签页
     */
    switchTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }

    /**
     * 加载设置
     */
    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['mdacSettings']);
            this.settings = result.mdacSettings || this.getDefaultSettings();
        } catch (error) {
            console.error('加载设置失败:', error);
            this.settings = this.getDefaultSettings();
        }
    }

    /**
     * 获取默认设置
     */
    getDefaultSettings() {
        return {
            defaultMode: 'smart',
            autoFill: false,
            showNotifications: true,
            fillSpeed: 3,
            language: 'zh-CN',
            darkMode: false,
            geminiApiKey: '',
            aiEnabled: true,
            aiModel: 'gemini-2.5-flash-lite-preview-06-17',
            aiTemperature: 0.7,
            encryptData: true,
            autoCleanHistory: true,
            anonymousUsage: false,
            debugMode: false,
            strictMode: false
        };
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 常规设置
        document.getElementById('defaultMode').value = this.settings.defaultMode;
        document.getElementById('autoFill').checked = this.settings.autoFill;
        document.getElementById('showNotifications').checked = this.settings.showNotifications;
        document.getElementById('fillSpeed').value = this.settings.fillSpeed;
        document.getElementById('language').value = this.settings.language;
        document.getElementById('darkMode').checked = this.settings.darkMode;

        // AI配置
        document.getElementById('geminiApiKey').value = this.settings.geminiApiKey;
        document.getElementById('aiEnabled').checked = this.settings.aiEnabled;
        document.getElementById('aiModel').value = this.settings.aiModel;
        document.getElementById('aiTemperature').value = this.settings.aiTemperature;

        // 安全隐私
        document.getElementById('encryptData').checked = this.settings.encryptData;
        document.getElementById('autoCleanHistory').checked = this.settings.autoCleanHistory;
        document.getElementById('anonymousUsage').checked = this.settings.anonymousUsage;
        document.getElementById('debugMode').checked = this.settings.debugMode;
        document.getElementById('strictMode').checked = this.settings.strictMode;
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        try {
            // 收集所有设置
            this.settings = {
                defaultMode: document.getElementById('defaultMode').value,
                autoFill: document.getElementById('autoFill').checked,
                showNotifications: document.getElementById('showNotifications').checked,
                fillSpeed: parseInt(document.getElementById('fillSpeed').value),
                language: document.getElementById('language').value,
                darkMode: document.getElementById('darkMode').checked,
                geminiApiKey: document.getElementById('geminiApiKey').value,
                aiEnabled: document.getElementById('aiEnabled').checked,
                aiModel: document.getElementById('aiModel').value,
                aiTemperature: parseFloat(document.getElementById('aiTemperature').value),
                encryptData: document.getElementById('encryptData').checked,
                autoCleanHistory: document.getElementById('autoCleanHistory').checked,
                anonymousUsage: document.getElementById('anonymousUsage').checked,
                debugMode: document.getElementById('debugMode').checked,
                strictMode: document.getElementById('strictMode').checked
            };

            await chrome.storage.sync.set({ mdacSettings: this.settings });
            this.showStatus('设置已保存', 'success');
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showStatus('保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 自动保存重要设置
     */
    async autoSave() {
        try {
            const quickSettings = {
                defaultMode: document.getElementById('defaultMode').value,
                aiEnabled: document.getElementById('aiEnabled').checked,
                encryptData: document.getElementById('encryptData').checked
            };

            this.settings = { ...this.settings, ...quickSettings };
            await chrome.storage.sync.set({ mdacSettings: this.settings });
        } catch (error) {
            console.error('自动保存失败:', error);
        }
    }

    /**
     * 重置设置
     */
    async resetSettings() {
        if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
            try {
                this.settings = this.getDefaultSettings();
                await chrome.storage.sync.set({ mdacSettings: this.settings });
                this.updateUI();
                this.showStatus('设置已重置', 'success');
            } catch (error) {
                console.error('重置设置失败:', error);
                this.showStatus('重置失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 切换API密钥可见性
     */
    toggleApiKeyVisibility() {
        const input = document.getElementById('geminiApiKey');
        const button = document.getElementById('toggleApiKey');
        
        if (input.type === 'password') {
            input.type = 'text';
            button.textContent = '🙈';
        } else {
            input.type = 'password';
            button.textContent = '👁️';
        }
    }

    /**
     * 测试AI连接
     */
    async testAIConnection() {
        const button = document.getElementById('testAI');
        const result = document.getElementById('aiTestResult');
        
        button.disabled = true;
        button.textContent = '测试中...';
        result.style.display = 'none';

        try {
            const apiKey = document.getElementById('geminiApiKey').value;
            if (!apiKey) {
                throw new Error('请先输入API密钥');
            }

            const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent?key=${apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: '你好，请回复"连接成功"'
                        }]
                    }]
                })
            });

            if (response.ok) {
                result.className = 'test-result success';
                result.textContent = '✅ AI连接测试成功！';
            } else {
                throw new Error(`API调用失败: ${response.status}`);
            }
        } catch (error) {
            result.className = 'test-result error';
            result.textContent = '❌ 连接失败: ' + error.message;
        } finally {
            button.disabled = false;
            button.textContent = '测试AI连接';
            result.style.display = 'block';
        }
    }

    /**
     * 保存模板
     */
    async saveTemplate() {
        try {
            const template = {
                name: document.getElementById('templateName').value,
                passport: document.getElementById('templatePassport').value,
                dob: document.getElementById('templateDOB').value,
                nationality: document.getElementById('templateNationality').value,
                email: document.getElementById('templateEmail').value,
                phone: document.getElementById('templatePhone').value,
                created: Date.now()
            };

            if (!template.name || !template.passport) {
                throw new Error('请至少填写姓名和护照号码');
            }

            const storage = await chrome.storage.local.get(['mdacTemplates']);
            const templates = storage.mdacTemplates || [];
            templates.push(template);

            await chrome.storage.local.set({ mdacTemplates: templates });
            this.showStatus('模板已保存', 'success');
            this.updateDataStats();
        } catch (error) {
            console.error('保存模板失败:', error);
            this.showStatus('保存失败: ' + error.message, 'error');
        }
    }

    /**
     * 加载模板
     */
    async loadTemplate() {
        try {
            const storage = await chrome.storage.local.get(['mdacTemplates']);
            const templates = storage.mdacTemplates || [];
            
            if (templates.length === 0) {
                this.showStatus('暂无保存的模板', 'error');
                return;
            }

            // 加载最新的模板
            const latest = templates[templates.length - 1];
            document.getElementById('templateName').value = latest.name || '';
            document.getElementById('templatePassport').value = latest.passport || '';
            document.getElementById('templateDOB').value = latest.dob || '';
            document.getElementById('templateNationality').value = latest.nationality || '';
            document.getElementById('templateEmail').value = latest.email || '';
            document.getElementById('templatePhone').value = latest.phone || '';

            this.showStatus('模板已加载', 'success');
        } catch (error) {
            console.error('加载模板失败:', error);
            this.showStatus('加载失败: ' + error.message, 'error');
        }
    }

    /**
     * 清空模板
     */
    clearTemplate() {
        if (confirm('确定要清空当前模板吗？')) {
            document.getElementById('templateName').value = '';
            document.getElementById('templatePassport').value = '';
            document.getElementById('templateDOB').value = '';
            document.getElementById('templateNationality').value = '';
            document.getElementById('templateEmail').value = '';
            document.getElementById('templatePhone').value = '';
            this.showStatus('模板已清空', 'success');
        }
    }

    /**
     * 导出数据
     */
    async exportData() {
        try {
            const data = await chrome.storage.local.get(null);
            const exportData = {
                settings: this.settings,
                templates: data.mdacTemplates || [],
                history: data.mdacHistory || [],
                exportTime: new Date().toISOString(),
                version: '1.0.0'
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `mdac-data-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            this.showStatus('数据已导出', 'success');
        } catch (error) {
            console.error('导出数据失败:', error);
            this.showStatus('导出失败: ' + error.message, 'error');
        }
    }

    /**
     * 导入数据
     */
    importData() {
        document.getElementById('fileInput').click();
    }

    /**
     * 处理文件导入
     */
    async handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);

            if (!data.version || !data.settings) {
                throw new Error('无效的数据文件格式');
            }

            if (confirm('确定要导入数据吗？这将覆盖现有的设置和数据。')) {
                // 导入设置
                this.settings = data.settings;
                await chrome.storage.sync.set({ mdacSettings: this.settings });

                // 导入模板和历史
                if (data.templates) {
                    await chrome.storage.local.set({ mdacTemplates: data.templates });
                }
                if (data.history) {
                    await chrome.storage.local.set({ mdacHistory: data.history });
                }

                this.updateUI();
                this.updateDataStats();
                this.showStatus('数据已导入', 'success');
            }
        } catch (error) {
            console.error('导入数据失败:', error);
            this.showStatus('导入失败: ' + error.message, 'error');
        }

        // 清空文件输入
        event.target.value = '';
    }

    /**
     * 清空历史记录
     */
    async clearHistory() {
        if (confirm('确定要清空所有填充历史吗？此操作不可撤销。')) {
            try {
                await chrome.storage.local.remove(['mdacHistory']);
                this.updateDataStats();
                this.showStatus('历史记录已清空', 'success');
            } catch (error) {
                console.error('清空历史失败:', error);
                this.showStatus('清空失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 清空所有数据
     */
    async clearAllData() {
        if (confirm('确定要清空所有数据吗？包括设置、模板和历史记录。此操作不可撤销。')) {
            try {
                await chrome.storage.local.clear();
                await chrome.storage.sync.clear();
                
                this.settings = this.getDefaultSettings();
                await chrome.storage.sync.set({ mdacSettings: this.settings });
                
                this.updateUI();
                this.updateDataStats();
                this.showStatus('所有数据已清空', 'success');
            } catch (error) {
                console.error('清空数据失败:', error);
                this.showStatus('清空失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 更新数据统计
     */
    async updateDataStats() {
        try {
            const data = await chrome.storage.local.get(null);
            const templates = data.mdacTemplates || [];
            const history = data.mdacHistory || [];
            
            // 计算存储使用量
            const storageSize = new Blob([JSON.stringify(data)]).size;
            const storageSizeKB = Math.round(storageSize / 1024);

            document.getElementById('templateCount').textContent = templates.length;
            document.getElementById('historyCount').textContent = history.length;
            document.getElementById('storageUsed').textContent = storageSizeKB + 'KB';
        } catch (error) {
            console.error('更新数据统计失败:', error);
        }
    }

    /**
     * 显示状态消息
     */
    showStatus(message, type = 'success') {
        const status = document.getElementById('saveStatus');
        status.textContent = message;
        status.className = `status ${type}`;
        
        setTimeout(() => {
            status.textContent = '';
            status.className = 'status';
        }, 3000);
    }

    /**
     * 打开帮助页面
     */
    openHelp() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('help.html')
        });
    }

    /**
     * 打开反馈页面
     */
    openFeedback() {
        chrome.tabs.create({
            url: 'https://github.com/mdac-ai/feedback/issues'
        });
    }

    /**
     * 检查更新
     */
    async checkUpdate() {
        this.showStatus('检查更新中...', 'success');
        
        // 模拟检查更新
        setTimeout(() => {
            this.showStatus('当前已是最新版本', 'success');
        }, 2000);
    }
}

// 初始化设置页面
document.addEventListener('DOMContentLoaded', () => {
    new MDACOptions();
});
