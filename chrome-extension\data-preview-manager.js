/**
 * MDAC AI智能分析工具 - 数据预览和确认机制
 * 提供AI解析结果的预览、编辑和确认功能
 */

class DataPreviewManager {
    constructor() {
        // 字段配置信息
        this.fieldConfig = {
            // 个人信息字段
            name: { label: '姓名', type: 'text', required: true, category: 'personal' },
            passportNo: { label: '护照号码', type: 'text', required: true, category: 'personal' },
            dateOfBirth: { label: '出生日期', type: 'date', required: true, category: 'personal' },
            nationality: { label: '国籍', type: 'select', required: true, category: 'personal' },
            sex: { label: '性别', type: 'select', required: true, category: 'personal' },
            passportExpiry: { label: '护照到期日', type: 'date', required: true, category: 'personal' },

            // 联系信息字段
            email: { label: '电子邮箱', type: 'email', required: true, category: 'contact' },
            confirmEmail: { label: '确认邮箱', type: 'email', required: true, category: 'contact' },
            countryCode: { label: '国家代码', type: 'select', required: true, category: 'contact' },
            mobileNo: { label: '手机号码', type: 'tel', required: true, category: 'contact' },

            // 旅行信息字段
            arrivalDate: { label: '到达日期', type: 'date', required: true, category: 'travel' },
            departureDate: { label: '离开日期', type: 'date', required: true, category: 'travel' },
            flightNo: { label: '航班号', type: 'text', required: true, category: 'travel' },
            modeOfTravel: { label: '旅行方式', type: 'select', required: true, category: 'travel' },
            lastPort: { label: '最后港口', type: 'select', required: true, category: 'travel' },

            // 住宿信息字段
            accommodation: { label: '住宿类型', type: 'select', required: true, category: 'accommodation' },
            address: { label: '地址', type: 'text', required: true, category: 'accommodation' },
            address2: { label: '地址2', type: 'text', required: false, category: 'accommodation' },
            state: { label: '州/省', type: 'select', required: true, category: 'accommodation' },
            postcode: { label: '邮政编码', type: 'text', required: true, category: 'accommodation' },
            city: { label: '城市', type: 'select', required: true, category: 'accommodation' }
        };

        // 字段分类
        this.categories = {
            personal: { label: '个人信息', icon: '👤', color: '#007bff' },
            contact: { label: '联系信息', icon: '📞', color: '#28a745' },
            travel: { label: '旅行信息', icon: '✈️', color: '#ffc107' },
            accommodation: { label: '住宿信息', icon: '🏨', color: '#dc3545' }
        };

        // 预览状态
        this.previewData = {};
        this.originalData = {};
        this.isEditing = false;
        this.hasChanges = false;
        this.validationErrors = {};

        // 回调函数
        this.onConfirm = null;
        this.onCancel = null;
        this.onChange = null;
    }

    /**
     * 显示数据预览界面
     * @param {Object} data 要预览的数据
     * @param {Object} options 预览选项
     */
    showPreview(data, options = {}) {
        this.originalData = JSON.parse(JSON.stringify(data));
        this.previewData = JSON.parse(JSON.stringify(data));
        this.hasChanges = false;
        this.validationErrors = {};

        // 设置回调函数
        this.onConfirm = options.onConfirm || null;
        this.onCancel = options.onCancel || null;
        this.onChange = options.onChange || null;

        // 创建预览界面
        this.createPreviewModal();

        // 初始验证
        this.validateAllFields();

        console.log('📋 数据预览界面已显示');
    }

    /**
     * 创建预览模态框
     */
    createPreviewModal() {
        // 移除现有的预览界面
        this.removeExistingPreview();

        const modal = document.createElement('div');
        modal.id = 'mdac-data-preview-modal';
        modal.className = 'mdac-preview-modal';

        modal.innerHTML = `
            <div class="preview-overlay" onclick="window.dataPreviewManager.handleCancel()"></div>
            <div class="preview-container">
                <div class="preview-header">
                    <div class="preview-title">
                        <span class="preview-icon">🔍</span>
                        <h2>数据预览和确认</h2>
                    </div>
                    <div class="preview-actions">
                        <button class="preview-btn secondary" onclick="window.dataPreviewManager.handleCancel()">
                            取消
                        </button>
                        <button class="preview-btn primary" onclick="window.dataPreviewManager.handleConfirm()" id="confirmBtn">
                            确认填充
                        </button>
                    </div>
                </div>

                <div class="preview-content">
                    <div class="preview-summary">
                        <div class="summary-stats">
                            <div class="stat-item">
                                <span class="stat-label">总字段</span>
                                <span class="stat-value" id="totalFields">${Object.keys(this.previewData).length}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">必填字段</span>
                                <span class="stat-value" id="requiredFields">${this.getRequiredFieldsCount()}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">完整度</span>
                                <span class="stat-value" id="completeness">${this.getCompletenessPercentage()}%</span>
                            </div>
                        </div>
                        <div class="summary-actions">
                            <button class="summary-btn" onclick="window.dataPreviewManager.toggleEditMode()">
                                <span class="btn-icon">✏️</span>
                                <span class="btn-text" id="editModeText">编辑模式</span>
                            </button>
                            <button class="summary-btn" onclick="window.dataPreviewManager.resetToOriginal()">
                                <span class="btn-icon">🔄</span>
                                <span class="btn-text">重置</span>
                            </button>
                        </div>
                    </div>

                    <div class="preview-fields">
                        ${this.renderFieldsByCategory()}
                    </div>
                </div>

                <div class="preview-footer">
                    <div class="validation-summary" id="validationSummary">
                        ${this.renderValidationSummary()}
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addPreviewStyles();

        // 插入到页面
        document.body.appendChild(modal);

        // 暴露到全局
        window.dataPreviewManager = this;

        // 设置焦点
        setTimeout(() => {
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    /**
     * 按分类渲染字段
     */
    renderFieldsByCategory() {
        const categorizedFields = this.categorizeFields();
        let html = '';

        for (const [categoryKey, categoryInfo] of Object.entries(this.categories)) {
            const fields = categorizedFields[categoryKey] || [];
            if (fields.length === 0) continue;

            html += `
                <div class="field-category">
                    <div class="category-header">
                        <span class="category-icon">${categoryInfo.icon}</span>
                        <span class="category-title">${categoryInfo.label}</span>
                        <span class="category-count">(${fields.length})</span>
                    </div>
                    <div class="category-fields">
                        ${fields.map(field => this.renderField(field)).join('')}
                    </div>
                </div>
            `;
        }

        return html;
    }

    /**
     * 渲染单个字段
     */
    renderField(fieldKey) {
        const config = this.fieldConfig[fieldKey];
        const value = this.previewData[fieldKey] || '';
        const hasError = this.validationErrors[fieldKey];
        const isRequired = config.required;

        return `
            <div class="field-item ${hasError ? 'has-error' : ''}" data-field="${fieldKey}">
                <div class="field-header">
                    <label class="field-label">
                        ${config.label}
                        ${isRequired ? '<span class="required-mark">*</span>' : ''}
                    </label>
                    ${hasError ? `<span class="field-error">${hasError}</span>` : ''}
                </div>
                <div class="field-input">
                    ${this.renderFieldInput(fieldKey, config, value)}
                </div>
            </div>
        `;
    }

    /**
     * 渲染字段输入控件
     */
    renderFieldInput(fieldKey, config, value) {
        const isEditing = this.isEditing;
        const inputId = `preview-field-${fieldKey}`;

        if (!isEditing) {
            // 只读模式
            return `
                <div class="field-display">
                    <span class="field-value">${value || '<span class="empty-value">未填写</span>'}</span>
                </div>
            `;
        }

        // 编辑模式
        switch (config.type) {
            case 'select':
                return `
                    <select id="${inputId}" class="field-control" onchange="window.dataPreviewManager.handleFieldChange('${fieldKey}', this.value)">
                        <option value="">请选择...</option>
                        ${this.getSelectOptions(fieldKey).map(option =>
                            `<option value="${option.value}" ${option.value === value ? 'selected' : ''}>${option.label}</option>`
                        ).join('')}
                    </select>
                `;
            case 'textarea':
                return `
                    <textarea id="${inputId}" class="field-control" rows="3"
                        onchange="window.dataPreviewManager.handleFieldChange('${fieldKey}', this.value)"
                        placeholder="请输入${config.label}">${value}</textarea>
                `;
            default:
                return `
                    <input type="${config.type}" id="${inputId}" class="field-control"
                        value="${value}"
                        onchange="window.dataPreviewManager.handleFieldChange('${fieldKey}', this.value)"
                        placeholder="请输入${config.label}">
                `;
        }
    }

    /**
     * 获取选择框选项
     */
    getSelectOptions(fieldKey) {
        const options = {
            nationality: [
                { value: 'CHN', label: '中国 (CHN)' },
                { value: 'USA', label: '美国 (USA)' },
                { value: 'SGP', label: '新加坡 (SGP)' },
                { value: 'MYS', label: '马来西亚 (MYS)' },
                { value: 'THA', label: '泰国 (THA)' },
                { value: 'IDN', label: '印度尼西亚 (IDN)' }
            ],
            sex: [
                { value: '1', label: '男性' },
                { value: '2', label: '女性' }
            ],
            countryCode: [
                { value: '+86', label: '中国 (+86)' },
                { value: '+60', label: '马来西亚 (+60)' },
                { value: '+65', label: '新加坡 (+65)' },
                { value: '+1', label: '美国 (+1)' },
                { value: '+66', label: '泰国 (+66)' }
            ],
            modeOfTravel: [
                { value: 'AIR', label: '航空' },
                { value: 'SEA', label: '海运' },
                { value: 'LAND', label: '陆路' }
            ],
            lastPort: [
                { value: 'SGP', label: '新加坡' },
                { value: 'BKK', label: '曼谷' },
                { value: 'CGK', label: '雅加达' },
                { value: 'HKG', label: '香港' }
            ],
            accommodation: [
                { value: '01', label: '酒店' },
                { value: '02', label: '民宿' },
                { value: '03', label: '朋友家' },
                { value: '04', label: '其他' }
            ],
            state: [
                { value: '14', label: '吉隆坡' },
                { value: '10', label: '雪兰莪' },
                { value: '07', label: '槟城' },
                { value: '01', label: '柔佛' }
            ],
            city: [
                { value: '1400', label: '吉隆坡市' },
                { value: '1000', label: '莎阿南' },
                { value: '0700', label: '乔治市' }
            ]
        };

        return options[fieldKey] || [];
    }

    /**
     * 将字段按分类分组
     */
    categorizeFields() {
        const categorized = {};

        for (const [fieldKey, value] of Object.entries(this.previewData)) {
            const config = this.fieldConfig[fieldKey];
            if (!config) continue;

            const category = config.category;
            if (!categorized[category]) {
                categorized[category] = [];
            }
            categorized[category].push(fieldKey);
        }

        return categorized;
    }

    /**
     * 获取必填字段数量
     */
    getRequiredFieldsCount() {
        return Object.keys(this.previewData).filter(key =>
            this.fieldConfig[key]?.required
        ).length;
    }

    /**
     * 获取完整度百分比
     */
    getCompletenessPercentage() {
        const totalFields = Object.keys(this.previewData).length;
        const filledFields = Object.values(this.previewData).filter(value =>
            value && value.toString().trim() !== ''
        ).length;

        return totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 0;
    }

    /**
     * 处理字段值变化
     */
    handleFieldChange(fieldKey, value) {
        this.previewData[fieldKey] = value;
        this.hasChanges = true;

        // 验证字段
        this.validateField(fieldKey, value);

        // 更新统计信息
        this.updateSummaryStats();

        // 触发变化回调
        if (this.onChange) {
            this.onChange(fieldKey, value, this.previewData);
        }

        console.log(`📝 字段 ${fieldKey} 已更新: ${value}`);
    }

    /**
     * 验证单个字段
     */
    validateField(fieldKey, value) {
        const config = this.fieldConfig[fieldKey];
        if (!config) return;

        const errors = [];

        // 必填验证
        if (config.required && (!value || value.toString().trim() === '')) {
            errors.push('此字段为必填项');
        }

        // 类型验证
        if (value && value.toString().trim() !== '') {
            switch (config.type) {
                case 'email':
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                        errors.push('请输入有效的邮箱地址');
                    }
                    break;
                case 'tel':
                    if (!/^\d{8,15}$/.test(value.replace(/\D/g, ''))) {
                        errors.push('请输入有效的电话号码');
                    }
                    break;
                case 'date':
                    if (!/^\d{2}\/\d{2}\/\d{4}$/.test(value)) {
                        errors.push('请使用 DD/MM/YYYY 格式');
                    }
                    break;
            }
        }

        // 更新验证错误
        if (errors.length > 0) {
            this.validationErrors[fieldKey] = errors[0];
        } else {
            delete this.validationErrors[fieldKey];
        }

        // 更新字段显示
        this.updateFieldDisplay(fieldKey);

        // 更新验证摘要
        this.updateValidationSummary();
    }

    /**
     * 验证所有字段
     */
    validateAllFields() {
        for (const [fieldKey, value] of Object.entries(this.previewData)) {
            this.validateField(fieldKey, value);
        }
    }

    /**
     * 更新字段显示
     */
    updateFieldDisplay(fieldKey) {
        const fieldElement = document.querySelector(`[data-field="${fieldKey}"]`);
        if (!fieldElement) return;

        const hasError = this.validationErrors[fieldKey];

        if (hasError) {
            fieldElement.classList.add('has-error');
            const errorElement = fieldElement.querySelector('.field-error');
            if (errorElement) {
                errorElement.textContent = hasError;
            }
        } else {
            fieldElement.classList.remove('has-error');
        }
    }

    /**
     * 更新统计信息
     */
    updateSummaryStats() {
        const totalElement = document.getElementById('totalFields');
        const requiredElement = document.getElementById('requiredFields');
        const completenessElement = document.getElementById('completeness');

        if (totalElement) totalElement.textContent = Object.keys(this.previewData).length;
        if (requiredElement) requiredElement.textContent = this.getRequiredFieldsCount();
        if (completenessElement) completenessElement.textContent = this.getCompletenessPercentage() + '%';
    }

    /**
     * 渲染验证摘要
     */
    renderValidationSummary() {
        const errorCount = Object.keys(this.validationErrors).length;

        if (errorCount === 0) {
            return `
                <div class="validation-success">
                    <span class="validation-icon">✅</span>
                    <span class="validation-text">所有字段验证通过</span>
                </div>
            `;
        }

        return `
            <div class="validation-errors">
                <span class="validation-icon">⚠️</span>
                <span class="validation-text">发现 ${errorCount} 个验证错误</span>
                <button class="validation-details-btn" onclick="window.dataPreviewManager.showValidationDetails()">
                    查看详情
                </button>
            </div>
        `;
    }

    /**
     * 更新验证摘要
     */
    updateValidationSummary() {
        const summaryElement = document.getElementById('validationSummary');
        if (summaryElement) {
            summaryElement.innerHTML = this.renderValidationSummary();
        }

        // 更新确认按钮状态
        const confirmBtn = document.getElementById('confirmBtn');
        if (confirmBtn) {
            const hasErrors = Object.keys(this.validationErrors).length > 0;
            confirmBtn.disabled = hasErrors;
            confirmBtn.className = `preview-btn ${hasErrors ? 'disabled' : 'primary'}`;
        }
    }

    /**
     * 切换编辑模式
     */
    toggleEditMode() {
        this.isEditing = !this.isEditing;

        // 重新渲染字段
        const fieldsContainer = document.querySelector('.preview-fields');
        if (fieldsContainer) {
            fieldsContainer.innerHTML = this.renderFieldsByCategory();
        }

        // 更新按钮文本
        const editModeText = document.getElementById('editModeText');
        if (editModeText) {
            editModeText.textContent = this.isEditing ? '预览模式' : '编辑模式';
        }

        console.log(`📝 ${this.isEditing ? '进入' : '退出'}编辑模式`);
    }

    /**
     * 重置到原始数据
     */
    resetToOriginal() {
        if (this.hasChanges && !confirm('确定要重置所有更改吗？')) {
            return;
        }

        this.previewData = JSON.parse(JSON.stringify(this.originalData));
        this.hasChanges = false;
        this.validationErrors = {};

        // 重新验证
        this.validateAllFields();

        // 重新渲染
        const fieldsContainer = document.querySelector('.preview-fields');
        if (fieldsContainer) {
            fieldsContainer.innerHTML = this.renderFieldsByCategory();
        }

        // 更新统计
        this.updateSummaryStats();

        console.log('🔄 数据已重置到原始状态');
    }

    /**
     * 显示验证详情
     */
    showValidationDetails() {
        const errors = Object.entries(this.validationErrors);
        if (errors.length === 0) return;

        const errorList = errors.map(([field, error]) => {
            const config = this.fieldConfig[field];
            return `<li><strong>${config?.label || field}:</strong> ${error}</li>`;
        }).join('');

        const detailsModal = document.createElement('div');
        detailsModal.className = 'validation-details-modal';
        detailsModal.innerHTML = `
            <div class="details-overlay" onclick="this.parentElement.remove()"></div>
            <div class="details-content">
                <div class="details-header">
                    <h3>验证错误详情</h3>
                    <button class="details-close" onclick="this.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="details-body">
                    <ul class="error-list">${errorList}</ul>
                </div>
            </div>
        `;

        document.body.appendChild(detailsModal);
    }

    /**
     * 处理确认操作
     */
    handleConfirm() {
        // 最终验证
        this.validateAllFields();

        if (Object.keys(this.validationErrors).length > 0) {
            alert('请先修正所有验证错误');
            return;
        }

        // 触发确认回调
        if (this.onConfirm) {
            this.onConfirm(this.previewData);
        }

        // 关闭预览
        this.closePreview();

        console.log('✅ 数据确认完成');
    }

    /**
     * 处理取消操作
     */
    handleCancel() {
        if (this.hasChanges && !confirm('确定要取消吗？未保存的更改将丢失。')) {
            return;
        }

        // 触发取消回调
        if (this.onCancel) {
            this.onCancel();
        }

        // 关闭预览
        this.closePreview();

        console.log('❌ 数据预览已取消');
    }

    /**
     * 关闭预览界面
     */
    closePreview() {
        this.removeExistingPreview();

        // 清理全局引用
        if (window.dataPreviewManager === this) {
            delete window.dataPreviewManager;
        }
    }

    /**
     * 移除现有的预览界面
     */
    removeExistingPreview() {
        const existingModal = document.getElementById('mdac-data-preview-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const existingDetails = document.querySelector('.validation-details-modal');
        if (existingDetails) {
            existingDetails.remove();
        }
    }

    /**
     * 添加预览样式
     */
    addPreviewStyles() {
        if (document.getElementById('mdac-preview-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'mdac-preview-styles';
        styles.textContent = `
            .mdac-preview-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .preview-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(2px);
            }

            .preview-container {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 900px;
                max-height: 90%;
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }

            .preview-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20px 24px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }

            .preview-title {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .preview-icon {
                font-size: 24px;
            }

            .preview-title h2 {
                margin: 0;
                font-size: 20px;
                font-weight: 600;
            }

            .preview-actions {
                display: flex;
                gap: 12px;
            }

            .preview-btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .preview-btn.primary {
                background: #28a745;
                color: white;
            }

            .preview-btn.primary:hover {
                background: #218838;
                transform: translateY(-1px);
            }

            .preview-btn.secondary {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }

            .preview-btn.secondary:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .preview-btn.disabled {
                background: #6c757d;
                color: #adb5bd;
                cursor: not-allowed;
            }

            .preview-content {
                flex: 1;
                overflow-y: auto;
                padding: 24px;
            }

            .preview-summary {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 24px;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }

            .summary-stats {
                display: flex;
                gap: 24px;
            }

            .stat-item {
                text-align: center;
            }

            .stat-label {
                display: block;
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 4px;
            }

            .stat-value {
                display: block;
                font-size: 18px;
                font-weight: 600;
                color: #495057;
            }

            .summary-actions {
                display: flex;
                gap: 12px;
            }

            .summary-btn {
                display: flex;
                align-items: center;
                gap: 6px;
                padding: 8px 12px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .summary-btn:hover {
                background: #e9ecef;
                border-color: #adb5bd;
            }

            .btn-icon {
                font-size: 14px;
            }

            .field-category {
                margin-bottom: 24px;
            }

            .category-header {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 16px;
                padding: 12px 16px;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }

            .category-icon {
                font-size: 18px;
            }

            .category-title {
                font-size: 16px;
                font-weight: 600;
                color: #495057;
            }

            .category-count {
                font-size: 14px;
                color: #6c757d;
            }

            .category-fields {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 16px;
            }

            .field-item {
                padding: 16px;
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                transition: all 0.2s ease;
            }

            .field-item:hover {
                border-color: #007bff;
                box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
            }

            .field-item.has-error {
                border-color: #dc3545;
                background: #fff5f5;
            }

            .field-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .field-label {
                font-size: 14px;
                font-weight: 500;
                color: #495057;
            }

            .required-mark {
                color: #dc3545;
                margin-left: 2px;
            }

            .field-error {
                font-size: 12px;
                color: #dc3545;
                font-weight: 500;
            }

            .field-display {
                padding: 8px 0;
            }

            .field-value {
                font-size: 14px;
                color: #495057;
            }

            .empty-value {
                color: #adb5bd;
                font-style: italic;
            }

            .field-control {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.2s ease;
            }

            .field-control:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
            }

            .field-item.has-error .field-control {
                border-color: #dc3545;
            }

            .preview-footer {
                padding: 16px 24px;
                background: #f8f9fa;
                border-top: 1px solid #e9ecef;
            }

            .validation-summary {
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .validation-success {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #28a745;
                font-weight: 500;
            }

            .validation-errors {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #dc3545;
                font-weight: 500;
            }

            .validation-icon {
                font-size: 16px;
            }

            .validation-details-btn {
                padding: 4px 8px;
                background: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: background 0.2s ease;
            }

            .validation-details-btn:hover {
                background: #c82333;
            }

            .validation-details-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10001;
                font-family: inherit;
            }

            .details-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
            }

            .details-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 90%;
                max-width: 500px;
                background: white;
                border-radius: 8px;
                overflow: hidden;
            }

            .details-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 16px 20px;
                background: #dc3545;
                color: white;
            }

            .details-header h3 {
                margin: 0;
                font-size: 16px;
            }

            .details-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
            }

            .details-body {
                padding: 20px;
            }

            .error-list {
                margin: 0;
                padding-left: 20px;
            }

            .error-list li {
                margin-bottom: 8px;
                color: #495057;
            }
        `;

        document.head.appendChild(styles);
    }
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataPreviewManager;
} else {
    window.DataPreviewManager = DataPreviewManager;
}