# Malaysia Digital Arrival Card (MDAC) - Complete Website Analysis

## Website Overview

- **URL**: <https://imigresen-online.imi.gov.my/mdac/main?registerMain>
- **Title**: Malaysia Digital Arrival Card - MDAC
- **Organization**: Jabatan Imigresen Malaysia (Immigration Department of Malaysia)
- **Date Accessed**: July 9, 2025
- **Current Time**: 01:42:58 AM (Wednesday)

## Navigation Structure

### Main Navigation Menu

1. **Home** (`/mdac/main`)
2. **Registration** (`/mdac/main?registerMain`) - Current active page
3. **Check Registration** (`/mdac/register?viewRegistration`)
4. **Check Visit Pass** (`/mdac/register?viewVisitPass`)
5. **Check Egate Eligibility Status** (`/mdac/egate`)

## Page Content Structure

### Header Section

- **Government Logo/Header Image**: Located at `/mdac/img/header.png`
- **Real-time Clock Display**: Shows current date and time
- **Language Setting**: Currently set to Malaysian (ms)

### Main Registration Form

#### Form Action

- **Method**: POST
- **Action**: `/mdac/register`
- **Form ID**: `permohonan`

#### Section 1: Personal Information

**Required Fields (marked with *):**

1. **Name**
   - Field ID: `name`
   - Type: Text input
   - Max length: 60 characters
   - Validation: Characters only, no paste allowed
   - Case: Uppercase

2. **Passport Number**
   - Field ID: `passNo`
   - Type: Text input
   - Max length: 12 characters
   - Validation: Alphanumeric only, no paste allowed
   - Case: Uppercase

3. **Date of Birth**
   - Field ID: `dob`
   - Type: Date picker
   - Format: DD/MM/YYYY
   - Restriction: Must be at least 1 day in the past
   - Read-only input field

4. **Nationality/Citizenship**
   - Field ID: `nationality`
   - Type: Dropdown select
   - Options: 200+ countries and territories (from AFG - Afghanistan to ZIM - Zimbabwe)
   - Triggers accommodation display logic
   - Triggers country phone code retrieval

5. **Sex**
   - Field ID: `sex`
   - Type: Dropdown select
   - Options: Male (1), Female (2)

6. **Date of Passport Expiry**
   - Field ID: `passExpDte`
   - Type: Date picker
   - Format: DD/MM/YYYY
   - Restriction: Must be in the future
   - Read-only input field

7. **Email Address**
   - Field ID: `email`
   - Type: Email input
   - Max length: 100 characters
   - Validation: Email format check, no paste allowed

8. **Confirm Email Address**
   - Field ID: `confirmEmail`
   - Type: Email input
   - Max length: 100 characters
   - Validation: Must match email, no copy/paste allowed

9. **Country/Region Code**
   - Field ID: `region`
   - Type: Dropdown select
   - Options: 200+ country codes for phone numbers
   - Format: (Code) Country Name

10. **Mobile Number**
    - Field ID: `mobile`
    - Type: Text input
    - Max length: 12 characters
    - Validation: Numbers and special characters only, no paste allowed

#### Section 2: Traveling Information

**Important Notice**: Trip must be within 3 days (including submission date)

**Required Fields:**

1. **Date of Arrival**
   - Field ID: `arrDt`
   - Type: Date picker
   - Format: DD/MM/YYYY
   - Restriction: Today to +2 days only
   - Triggers departure date range update

2. **Date of Departure**
   - Field ID: `depDt`
   - Type: Date picker
   - Format: DD/MM/YYYY
   - Restriction: Must be after arrival date
   - Dynamic start date based on arrival

3. **Flight/Vessel/Transportation Number**
   - Field ID: `vesselNm`
   - Type: Text input
   - Max length: 30 characters
   - Validation: Alphanumeric only, no paste allowed

4. **Mode of Travel**
   - Field ID: `trvlMode`
   - Type: Dropdown select
   - Options:
     - AIR (1)
     - LAND (2)
     - SEA (3)

5. **Last Port of Embarkation before Malaysia**
   - Field ID: `embark`
   - Type: Dropdown select
   - Options: Same 200+ countries as nationality field

#### Section 3: Accommodation Information

**Dynamic Section**: Appears based on nationality selection

**Required Fields:**

1. **Accommodation of Stay**
   - Field ID: `accommodationStay`
   - Type: Dropdown select
   - Options:
     - HOTEL/MOTEL/REST HOUSE (01)
     - RESIDENCE OF FRIENDS/RELATIVES (02)
     - OTHERS (99)

2. **Address (In Malaysia)**
   - Field ID: `accommodationAddress1`
   - Type: Text input
   - Max length: 100 characters
   - Validation: Alphanumeric only, no paste allowed
   - Note: Alphanumeric characters only

3. **Address Line 2**
   - Field ID: `accommodationAddress2`
   - Type: Text input
   - Max length: 100 characters
   - Same validation as address line 1

4. **State**
   - Field ID: `accommodationState`
   - Type: Dropdown select
   - Options (16 Malaysian states):
     - JOHOR (01)
     - KEDAH (02)
     - KELANTAN (03)
     - MELAKA (04)
     - NEGERI SEMBILAN (05)
     - PAHANG (06)
     - PULAU PINANG (07)
     - PERAK (08)
     - PERLIS (09)
     - SELANGOR (10)
     - TERENGGANU (11)
     - SABAH (12)
     - SARAWAK (13)
     - WP KUALA LUMPUR (14)
     - WP LABUAN (15)
     - WP PUTRAJAYA (16)
   - Triggers city dropdown population

5. **City**
   - Field ID: `accommodationCity`
   - Type: Dropdown select
   - Dynamic options based on selected state
   - Triggers postcode auto-fill

6. **Postcode**
   - Field ID: `accommodationPostcode`
   - Type: Text input
   - Max length: 5 characters
   - Validation: Numbers only
   - Auto-populated based on city selection

### Form Controls

**Action Buttons:**

1. **Submit** - Validates form and submits registration
2. **Reset** - Clears all form fields

### Technical Implementation Details

#### JavaScript Features

- **Date Pickers**: Bootstrap datepicker with custom validation
- **Dynamic Form Logic**: Fields appear/disappear based on selections
- **Real-time Validation**: Field-level validation on blur/change
- **Auto-population**: Postcode and city interdependence
- **Character Restrictions**: Input filtering and validation
- **Copy/Paste Prevention**: Security measures on sensitive fields

#### Form Validation Rules

- **Required Field Validation**: All marked fields must be completed
- **Date Range Validation**: Arrival within 3 days, departure after arrival
- **Email Matching**: Confirmation email must match original
- **Character Limits**: Enforced on all text inputs
- **Format Validation**: Date formats, email formats, numeric fields

#### Security Features

- **Hidden Security Fields**:
  - `_sourcePage`: Form state token
  - `__fp`: Fingerprint/security hash
- **Input Sanitization**: Server-side validation hints
- **Copy/Paste Prevention**: On sensitive fields
- **Form State Management**: Session and CSRF protection

### Footer Information

- **Copyright**: Malaysia Digital Arrival Card (MDAC) | © Jabatan Imigresen Malaysia
- **Back to Top**: Navigation helper

### Additional Features

- **Responsive Design**: Mobile-friendly form layout
- **Loading Indicators**: Ajax spinner for form processing
- **Modal Dialogs**: Confirmation dialogs for certain actions
- **Browser Compatibility**: Cross-browser form validation

## Form Field Dependencies

### Conditional Logic Flow

1. **Nationality Selection** → Shows/hides accommodation section
2. **Nationality Selection** → Populates country phone codes
3. **State Selection** → Populates city dropdown
4. **City Selection** → Auto-fills postcode
5. **Arrival Date** → Sets minimum departure date range

### Data Validation Chain

1. **Email Validation** → Triggers on blur event
2. **Email Confirmation** → Validates match on blur
3. **Address Validation** → Checks alphanumeric format
4. **Form Submission** → Comprehensive validation check

## Browser Context Information

- **Active Tab**: Malaysia Digital Arrival Card registration page
- **Window Count**: 1 browser window
- **Total Tabs**: 8 open tabs
- **Other Tabs**: Gmail, Ctrip booking, GoMyHire user pages, Chrome extensions

## Complete Country/Territory List

The form includes comprehensive dropdown options for:

### Nationality/Citizenship Options (200+ entries)

- Afghanistan (AFG) to Zimbabwe (ZIM)
- Special categories: Stateless, Refugee, UN organizations
- British territories and dependencies
- All sovereign nations and territories

### Phone Country Codes (200+ entries)

- Format: (Code) Country Name
- Ranges from (+1) for North America to (+998) for Uzbekistan
- Includes all international dialing codes

### Malaysian States and Territories (16 entries)

- 13 Traditional states
- 3 Federal territories (Kuala Lumpur, Labuan, Putrajaya)

## Key User Experience Features

### Form Usability

- **Progressive Disclosure**: Only show relevant fields
- **Real-time Feedback**: Immediate validation messages
- **Auto-completion**: Smart field population
- **Error Prevention**: Input restrictions and format validation
- **Mobile Optimization**: Touch-friendly interface

### Accessibility Features

- **Clear Labels**: All form fields properly labeled
- **Required Field Indicators**: Asterisk (*) marking
- **Date Format Hints**: DD/MM/YYYY placeholders
- **Logical Tab Order**: Sequential form navigation
- **Error Messages**: Clear validation feedback

## Summary

This comprehensive analysis covers the complete structure, functionality, and user interaction flow of the Malaysia Digital Arrival Card (MDAC) online registration system. The system is a sophisticated government digital service that streamlines immigration pre-registration for visitors to Malaysia, featuring robust validation, security measures, and user-friendly design principles.
