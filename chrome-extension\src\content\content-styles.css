/**
 * MDAC AI智能填充工具 - 内容脚本样式
 * 在MDAC官网页面中注入的UI组件样式
 */

/* AI助手浮动面板 */
#mdac-ai-assistant {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* AI助手头部 */
.ai-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-icon {
    font-size: 18px;
    margin-right: 8px;
}

.ai-title {
    font-size: 14px;
    font-weight: 600;
    flex: 1;
}

.ai-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
}

.ai-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* AI助手内容 */
.ai-content {
    padding: 16px;
    display: block;
}

.ai-status {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    text-align: center;
}

.ai-suggestions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    font-size: 12px;
    line-height: 1.4;
    min-height: 60px;
    max-height: 120px;
    overflow-y: auto;
}

.ai-suggestions:empty::before {
    content: "等待AI分析...";
    opacity: 0.6;
    font-style: italic;
}

/* AI助手按钮 */
.ai-actions {
    display: flex;
    gap: 8px;
}

.ai-btn {
    flex: 1;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.ai-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.ai-btn:active {
    transform: translateY(0);
}

/* 通知样式 */
.mdac-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 8px;
    color: white;
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    z-index: 10001;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    animation: slideInRight 0.3s ease-out;
    max-width: 300px;
    word-wrap: break-word;
}

.mdac-notification.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.mdac-notification.error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.mdac-notification.warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #333;
}

.mdac-notification.info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 表单字段高亮 */
.mdac-field-highlight {
    box-shadow: 0 0 0 2px #667eea !important;
    border-color: #667eea !important;
    transition: all 0.2s ease;
}

.mdac-field-success {
    box-shadow: 0 0 0 2px #28a745 !important;
    border-color: #28a745 !important;
}

.mdac-field-error {
    box-shadow: 0 0 0 2px #dc3545 !important;
    border-color: #dc3545 !important;
}

/* 填充进度指示器 */
.mdac-progress-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 25px 35px;
    border-radius: 16px;
    z-index: 10002;
    text-align: center;
    font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.progress-icon {
    font-size: 32px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.progress-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.mdac-progress-bar {
    width: 240px;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.mdac-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.5s ease;
    position: relative;
}

.mdac-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.progress-percentage {
    font-size: 12px;
    font-weight: 600;
    color: #667eea;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 40px;
}

/* 字段验证提示 */
.mdac-field-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10003;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.mdac-field-tooltip::before {
    content: '';
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid #333;
}

.mdac-field-tooltip.success {
    background: #28a745;
}

.mdac-field-tooltip.error {
    background: #dc3545;
}

.mdac-field-tooltip.warning {
    background: #ffc107;
    color: #333;
}

/* 智能建议气泡 */
.mdac-suggestion-bubble {
    position: absolute;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    z-index: 10003;
    max-width: 200px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    animation: fadeInUp 0.3s ease-out;
}

.mdac-suggestion-bubble::before {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 20px;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid #667eea;
}

@keyframes fadeInUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    #mdac-ai-assistant {
        width: 280px;
        right: 10px;
        top: 10px;
    }
    
    .mdac-notification {
        right: 10px;
        top: 70px;
        max-width: 280px;
    }
}

/* 滚动条样式 */
.ai-suggestions::-webkit-scrollbar {
    width: 4px;
}

.ai-suggestions::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.ai-suggestions::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.ai-suggestions::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 加载动画 */
.mdac-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 成功动画 */
.mdac-success-checkmark {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #28a745;
    position: relative;
}

.mdac-success-checkmark::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 3px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 错误动画 */
.mdac-error-cross {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #dc3545;
    position: relative;
}

.mdac-error-cross::before,
.mdac-error-cross::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 3px;
    width: 2px;
    height: 10px;
    background: white;
}

.mdac-error-cross::before {
    transform: rotate(45deg);
}

.mdac-error-cross::after {
    transform: rotate(-45deg);
}

/* 填充进度状态样式 */
.progress-status {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    gap: 4px;
}

.status-item {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 500;
    min-width: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.status-item.success {
    background: rgba(40, 167, 69, 0.8);
}

.status-item.failed {
    background: rgba(220, 53, 69, 0.8);
}

.status-item.skipped {
    background: rgba(255, 193, 7, 0.8);
}

.status-item.progress {
    background: rgba(0, 123, 255, 0.8);
    animation: statusPulse 1.5s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* 进度条样式更新 */
.mdac-progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    transition: width 0.5s ease;
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
}

/* 进度可视化器样式 */
.progress-visualizer {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-visualizer.show {
    opacity: 1;
}

.progress-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: progressSlideIn 0.3s ease-out;
}

@keyframes progressSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.progress-title {
    display: flex;
    align-items: center;
    gap: 6px;
}

.progress-title h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.progress-controls {
    display: flex;
    gap: 6px;
}

.control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 3px;
    color: white;
    cursor: pointer;
    padding: 3px 6px;
    font-size: 12px;
    transition: background 0.2s ease;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.progress-content {
    padding: 16px;
}

.progress-summary {
    margin-bottom: 12px;
}

.progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    margin-bottom: 12px;
}

.stat-item {
    text-align: center;
    padding: 6px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.stat-value {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.stat-label {
    display: block;
    font-size: 10px;
    color: #6c757d;
    margin-top: 2px;
}

.progress-bar-container {
    margin: 8px 0;
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #0056b3);
    border-radius: 3px;
    transition: width 0.5s ease;
    animation: progressPulse 2s ease-in-out infinite;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 10px;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.progress-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #6c757d;
}

.field-progress {
    margin-top: 12px;
    border-top: 1px solid #e9ecef;
    padding-top: 12px;
}

.field-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.field-header h4 {
    margin: 0;
    font-size: 12px;
    color: #495057;
}

.toggle-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 12px;
    color: #6c757d;
    padding: 0;
}

.field-list {
    max-height: 200px;
    overflow-y: auto;
}

.field-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    margin-bottom: 4px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.field-item.field-updating {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: scale(1.02);
}

.field-item.field-success {
    background: #e8f5e8;
    border-color: #4caf50;
}

.field-item.field-failed {
    background: #ffebee;
    border-color: #f44336;
}

.field-status {
    width: 20px;
    text-align: center;
}

.status-icon {
    font-size: 12px;
}

.field-info {
    flex: 1;
    min-width: 0;
}

.field-name {
    font-size: 11px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 2px;
}

.field-value {
    font-size: 10px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.field-progress {
    width: 40px;
}

.mini-progress-bar {
    width: 100%;
    height: 3px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
}

.mini-progress-fill {
    height: 100%;
    background: #007bff;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.progress-actions {
    display: flex;
    gap: 6px;
    justify-content: flex-end;
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.action-btn.secondary {
    background: #6c757d;
    color: white;
}

.action-btn.secondary:hover {
    background: #545b62;
}

.action-btn.danger {
    background: #dc3545;
    color: white;
}

.action-btn.danger:hover {
    background: #c82333;
}

.btn-icon {
    font-size: 10px;
}

.btn-text {
    font-size: 10px;
}

.completion-banner {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 1px solid #c8e6c9;
    border-radius: 6px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.completion-banner.show {
    opacity: 1;
    transform: translateY(0);
}

.completion-icon {
    font-size: 24px;
}

.completion-text h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #2e7d32;
}

.completion-text p {
    margin: 0;
    font-size: 12px;
    color: #4caf50;
}

.progress-visualizer.minimized .progress-content,
.progress-visualizer.minimized .progress-actions {
    display: none;
}

.progress-visualizer.closing {
    opacity: 0;
    transform: scale(0.9);
}

/* 侧边栏进度样式 */
.sidepanel-progress {
    margin: 16px;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
}

.sidepanel-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.sidepanel-progress-header h4 {
    margin: 0;
    font-size: 14px;
    color: #495057;
}

.progress-percentage {
    font-size: 16px;
    font-weight: 600;
    color: #007bff;
}

.sidepanel-progress-bar {
    width: 100%;
    height: 4px;
    background: #e9ecef;
    border-radius: 2px;
    overflow: hidden;
    margin: 8px 0;
}

.sidepanel-progress-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #6c757d;
}

.field-counter {
    font-weight: 500;
}

.sidepanel-field-list {
    margin-top: 8px;
    max-height: 120px;
    overflow-y: auto;
}

.compact-field-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 3px 6px;
    margin-bottom: 2px;
    background: white;
    border-radius: 3px;
    font-size: 10px;
}

.field-status-icon {
    font-size: 10px;
    width: 12px;
    text-align: center;
}

.compact-field-item .field-name {
    color: #495057;
    font-size: 10px;
}

/* 隐藏原始表单验证消息 */
.mdac-hide-validation {
    display: none !important;
}
