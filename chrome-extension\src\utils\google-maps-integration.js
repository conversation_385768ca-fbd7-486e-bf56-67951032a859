/**
 * Google Maps API集成工具
 * 利用现有Gemini API密钥实现地址标准化和验证功能
 */

class GoogleMapsIntegration {
    constructor(apiKey) {
        this.apiKey = apiKey;
        this.baseUrl = 'https://maps.googleapis.com/maps/api';
        this.cache = new Map(); // 缓存查询结果
        this.rateLimitDelay = 100; // API调用间隔
        this.lastCallTime = 0;
    }

    /**
     * 地址标准化 - 将中文地址转换为标准英文地址
     */
    async standardizeAddress(address, options = {}) {
        try {
            console.log('🗺️ 开始地址标准化:', address);

            // 检查缓存
            const cacheKey = `standardize_${address}`;
            if (this.cache.has(cacheKey)) {
                console.log('📋 使用缓存结果');
                return this.cache.get(cacheKey);
            }

            // API调用限流
            await this.rateLimitCheck();

            // 使用Geocoding API进行地址标准化
            const geocodeResult = await this.geocodeAddress(address, options);
            
            if (!geocodeResult.success) {
                throw new Error(geocodeResult.error);
            }

            // 解析结果
            const standardizedResult = this.parseGeocodeResult(geocodeResult.data);
            
            // 缓存结果
            this.cache.set(cacheKey, standardizedResult);
            
            console.log('✅ 地址标准化完成:', standardizedResult);
            return standardizedResult;

        } catch (error) {
            console.error('❌ 地址标准化失败:', error);
            return {
                success: false,
                error: error.message,
                originalAddress: address
            };
        }
    }

    /**
     * 地址验证 - 验证地址是否在马来西亚境内
     */
    async validateMalaysianAddress(address) {
        try {
            console.log('🇲🇾 验证马来西亚地址:', address);

            const result = await this.standardizeAddress(address, {
                region: 'MY',
                components: 'country:MY'
            });

            if (!result.success) {
                return {
                    isValid: false,
                    error: result.error,
                    address: address
                };
            }

            // 检查是否在马来西亚
            const isMalaysian = result.country === 'Malaysia' || 
                              result.countryCode === 'MY' ||
                              result.formattedAddress.includes('Malaysia');

            return {
                isValid: isMalaysian,
                standardizedAddress: result.formattedAddress,
                components: result.components,
                confidence: result.confidence,
                address: address
            };

        } catch (error) {
            console.error('❌ 地址验证失败:', error);
            return {
                isValid: false,
                error: error.message,
                address: address
            };
        }
    }

    /**
     * 邮政编码自动补全
     */
    async autocompletePostcode(address, partialPostcode = '') {
        try {
            console.log('📮 邮政编码自动补全:', address, partialPostcode);

            // 先获取地址的详细信息
            const addressResult = await this.standardizeAddress(address);
            
            if (!addressResult.success) {
                throw new Error('无法解析地址');
            }

            // 从地址组件中提取邮政编码
            const extractedPostcode = addressResult.components.postal_code;
            
            if (extractedPostcode) {
                // 验证邮政编码格式（马来西亚为5位数字）
                if (/^\d{5}$/.test(extractedPostcode)) {
                    return {
                        success: true,
                        postcode: extractedPostcode,
                        confidence: 0.9,
                        source: 'google_maps'
                    };
                }
            }

            // 如果有部分邮政编码，尝试匹配
            if (partialPostcode && partialPostcode.length >= 2) {
                const suggestions = await this.suggestPostcodes(partialPostcode, addressResult);
                return {
                    success: true,
                    suggestions: suggestions,
                    confidence: 0.7,
                    source: 'suggestion'
                };
            }

            return {
                success: false,
                error: '无法确定邮政编码',
                extractedPostcode: extractedPostcode
            };

        } catch (error) {
            console.error('❌ 邮政编码补全失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取地址的州和城市信息
     */
    async getLocationInfo(address) {
        try {
            console.log('🏙️ 获取位置信息:', address);

            const result = await this.standardizeAddress(address);
            
            if (!result.success) {
                throw new Error(result.error);
            }

            // 映射到MDAC代码
            const locationInfo = this.mapToMDACCodes(result.components);

            return {
                success: true,
                state: locationInfo.state,
                city: locationInfo.city,
                postcode: locationInfo.postcode,
                formattedAddress: result.formattedAddress,
                confidence: result.confidence
            };

        } catch (error) {
            console.error('❌ 获取位置信息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 调用Google Geocoding API
     */
    async geocodeAddress(address, options = {}) {
        try {
            const params = new URLSearchParams({
                address: address,
                key: this.apiKey,
                language: 'en', // 返回英文结果
                region: options.region || 'MY' // 默认马来西亚
            });

            // 添加组件过滤
            if (options.components) {
                params.append('components', options.components);
            }

            const url = `${this.baseUrl}/geocode/json?${params}`;
            
            const response = await fetch(url);
            const data = await response.json();

            if (data.status === 'OK' && data.results.length > 0) {
                return {
                    success: true,
                    data: data.results[0]
                };
            } else {
                return {
                    success: false,
                    error: `Geocoding失败: ${data.status}`
                };
            }

        } catch (error) {
            return {
                success: false,
                error: `API调用失败: ${error.message}`
            };
        }
    }

    /**
     * 解析Geocoding结果
     */
    parseGeocodeResult(result) {
        const components = {};
        
        // 解析地址组件
        result.address_components.forEach(component => {
            const types = component.types;
            
            if (types.includes('street_number')) {
                components.street_number = component.long_name;
            }
            if (types.includes('route')) {
                components.route = component.long_name;
            }
            if (types.includes('locality')) {
                components.locality = component.long_name;
            }
            if (types.includes('administrative_area_level_1')) {
                components.state = component.long_name;
                components.state_short = component.short_name;
            }
            if (types.includes('country')) {
                components.country = component.long_name;
                components.country_code = component.short_name;
            }
            if (types.includes('postal_code')) {
                components.postal_code = component.long_name;
            }
        });

        return {
            success: true,
            formattedAddress: result.formatted_address,
            components: components,
            location: result.geometry.location,
            confidence: this.calculateConfidence(result),
            placeId: result.place_id
        };
    }

    /**
     * 映射到MDAC代码
     */
    mapToMDACCodes(components) {
        const stateMapping = {
            'Johor': '01', 'Kedah': '02', 'Kelantan': '03', 'Melaka': '04',
            'Negeri Sembilan': '05', 'Pahang': '06', 'Penang': '07', 'Perak': '08',
            'Perlis': '09', 'Selangor': '10', 'Terengganu': '11', 'Sabah': '12',
            'Sarawak': '13', 'Kuala Lumpur': '14', 'Labuan': '15', 'Putrajaya': '16'
        };

        const cityMapping = {
            'Kuala Lumpur': '1400', 'George Town': '1000', 'Melaka': '0700',
            'Johor Bahru': '0100', 'Ipoh': '0800', 'Kuantan': '0600',
            'Kuala Terengganu': '1100', 'Kangar': '0900', 'Kota Kinabalu': '1200',
            'Kuching': '1300'
        };

        const stateName = components.state;
        const cityName = components.locality;

        return {
            state: stateMapping[stateName] || null,
            city: cityMapping[cityName] || null,
            postcode: components.postal_code || null,
            stateName: stateName,
            cityName: cityName
        };
    }

    /**
     * 计算置信度
     */
    calculateConfidence(result) {
        let confidence = 0.5; // 基础置信度

        // 根据地址类型调整置信度
        if (result.types.includes('street_address')) {
            confidence += 0.3;
        } else if (result.types.includes('route')) {
            confidence += 0.2;
        } else if (result.types.includes('locality')) {
            confidence += 0.1;
        }

        // 根据几何精度调整
        if (result.geometry.location_type === 'ROOFTOP') {
            confidence += 0.2;
        } else if (result.geometry.location_type === 'RANGE_INTERPOLATED') {
            confidence += 0.1;
        }

        return Math.min(confidence, 1.0);
    }

    /**
     * 建议邮政编码
     */
    async suggestPostcodes(partialPostcode, addressResult) {
        // 基于地理位置和部分邮编生成建议
        const suggestions = [];
        
        // 这里可以实现更复杂的邮编建议逻辑
        // 目前返回基础建议
        const baseCode = partialPostcode.padEnd(5, '0');
        for (let i = 0; i < 5; i++) {
            const suggestion = (parseInt(baseCode) + i).toString().padStart(5, '0');
            suggestions.push({
                postcode: suggestion,
                confidence: 0.6 - (i * 0.1)
            });
        }

        return suggestions;
    }

    /**
     * API调用限流检查
     */
    async rateLimitCheck() {
        const now = Date.now();
        const timeSinceLastCall = now - this.lastCallTime;
        
        if (timeSinceLastCall < this.rateLimitDelay) {
            const waitTime = this.rateLimitDelay - timeSinceLastCall;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        this.lastCallTime = Date.now();
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
        console.log('🗑️ Google Maps缓存已清除');
    }

    /**
     * 获取缓存统计
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// 导出Google Maps集成工具
if (typeof window !== 'undefined') {
    window.GoogleMapsIntegration = GoogleMapsIntegration;
} else if (typeof module !== 'undefined' && module.exports) {
    module.exports = GoogleMapsIntegration;
}
