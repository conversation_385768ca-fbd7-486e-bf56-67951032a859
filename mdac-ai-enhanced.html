<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能填充工具 - Gemini驱动</title>
    <style>
        /* 基础样式设置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .ai-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .content {
            padding: 30px;
        }

        .ai-assistant {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            position: relative;
        }

        .ai-assistant h3 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ai-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #4ade80;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .ai-suggestions {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            min-height: 60px;
            backdrop-filter: blur(10px);
        }

        .method-selector {
            margin-bottom: 30px;
            text-align: center;
        }

        .method-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
        }

        .method-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .method-btn.active {
            background: #48bb78;
        }

        .method-btn.ai-enhanced::after {
            content: "🤖";
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 12px;
        }

        .method-content {
            display: none;
            margin-top: 30px;
        }

        .method-content.active {
            display: block;
        }

        .form-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }

        .section-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ai-helper-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .ai-helper-btn:hover {
            transform: scale(1.05);
        }

        .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .required {
            color: #ff4757;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .ai-validated {
            border-color: #28a745 !important;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
        }

        .ai-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
        }

        .ai-suggestion-tooltip {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            margin-top: 5px;
        }

        .ai-suggestion-tooltip.show {
            display: block;
        }

        .translate-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: #17a2b8;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 11px;
        }

        .btn-container {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .note strong {
            display: block;
            margin-bottom: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .container {
                margin: 10px;
            }

            .header {
                padding: 20px;
            }

            .content {
                padding: 20px;
            }

            .ai-badge {
                position: static;
                display: inline-block;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="ai-badge">🤖 Gemini AI 驱动</div>
            <h1>🇲🇾 MDAC AI智能填充工具</h1>
            <p>集成Gemini AI的智能表单填充助手，让入境卡填写更简单、更准确</p>
        </div>

        <div class="content">
            <!-- AI助手面板 -->
            <div class="ai-assistant">
                <h3>
                    <span>🤖 AI智能助手</span>
                    <span class="ai-status"></span>
                </h3>
                <p>我是您的专属AI助手，可以帮您智能填写表单、验证信息、翻译地址，并提供旅行建议。</p>
                <div class="ai-suggestions" id="aiSuggestions">
                    <div id="aiSuggestionsContent">准备就绪，开始填写表单时我会为您提供智能建议...</div>
                </div>
            </div>

            <div class="note">
                <strong>🚀 AI增强功能：</strong>
                • 智能表单建议和自动补全<br>
                • 实时数据验证和纠错<br>
                • 旅行信息智能推荐<br>
                • 中英文地址智能翻译<br>
                • 多种自动填充方案
            </div>

            <div class="method-selector">
                <button class="method-btn ai-enhanced active" onclick="showMethod('smart-form')">🤖 AI智能表单</button>
                <button class="method-btn ai-enhanced" onclick="showMethod('bookmarklet')">📖 书签工具</button>
                <button class="method-btn" onclick="showMethod('manual')">📋 手动复制</button>
            </div>

            <!-- AI智能表单 -->
            <div id="smart-form" class="method-content active">
                <h2>🤖 AI智能表单填写</h2>

                <div class="note">
                    <strong>智能填写模式：</strong>AI助手会根据您的输入提供智能建议、自动验证数据准确性，并帮助翻译地址信息。
                </div>

                <form id="aiMdacForm">
                    <!-- 个人信息部分 -->
                    <div class="form-section">
                        <div class="section-title">
                            <span>📋 个人信息</span>
                            <button type="button" class="ai-helper-btn" onclick="aiHelp('personal')">AI建议</button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiName">姓名 <span class="required">*</span></label>
                                <input type="text" id="aiName" name="name" required placeholder="请输入护照上的英文姓名"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiName-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiPassportNo">护照号码 <span class="required">*</span></label>
                                <input type="text" id="aiPassportNo" name="passportNo" required placeholder="例：A12345678"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiPassportNo-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiDateOfBirth">出生日期 <span class="required">*</span></label>
                                <input type="date" id="aiDateOfBirth" name="dateOfBirth" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiDateOfBirth-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiNationality">国籍 <span class="required">*</span></label>
                                <select id="aiNationality" name="nationality" required onchange="aiValidateField(this)">
                                    <option value="">请选择国籍</option>
                                    <option value="CHINA">中国</option>
                                    <option value="SINGAPORE">新加坡</option>
                                    <option value="THAILAND">泰国</option>
                                    <option value="INDONESIA">印度尼西亚</option>
                                    <option value="PHILIPPINES">菲律宾</option>
                                    <option value="VIETNAM">越南</option>
                                    <option value="INDIA">印度</option>
                                    <option value="JAPAN">日本</option>
                                    <option value="SOUTH KOREA">韩国</option>
                                    <option value="UNITED STATES">美国</option>
                                    <option value="UNITED KINGDOM">英国</option>
                                    <option value="AUSTRALIA">澳大利亚</option>
                                    <option value="CANADA">加拿大</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiNationality-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiSex">性别 <span class="required">*</span></label>
                                <select id="aiSex" name="sex" required onchange="aiValidateField(this)">
                                    <option value="">请选择性别</option>
                                    <option value="MALE">男性</option>
                                    <option value="FEMALE">女性</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiSex-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiPassportExpiry">护照到期日期 <span class="required">*</span></label>
                                <input type="date" id="aiPassportExpiry" name="passportExpiry" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiPassportExpiry-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiEmail">电子邮箱 <span class="required">*</span></label>
                                <input type="email" id="aiEmail" name="email" value="<EMAIL>" required
                                       oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiEmail-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiConfirmEmail">确认电子邮箱 <span class="required">*</span></label>
                                <input type="email" id="aiConfirmEmail" name="confirmEmail" value="<EMAIL>" required
                                       oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiConfirmEmail-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiCountryCode">国家/地区代码 <span class="required">*</span></label>
                                <input type="text" id="aiCountryCode" name="countryCode" value="+60" required
                                       oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiCountryCode-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiMobileNo">手机号码 <span class="required">*</span></label>
                                <input type="tel" id="aiMobileNo" name="mobileNo" value="*********" required
                                       placeholder="不包含国家代码" oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiMobileNo-tooltip"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 旅行信息部分 -->
                    <div class="form-section">
                        <div class="section-title">
                            <span>✈️ 旅行信息</span>
                            <button type="button" class="ai-helper-btn" onclick="aiHelp('travel')">AI推荐</button>
                        </div>
                        <div class="note">
                            <strong>重要提醒：</strong>您的行程必须在3天内（包括提交日期）
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiArrivalDate">到达日期 <span class="required">*</span></label>
                                <input type="date" id="aiArrivalDate" name="arrivalDate" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiArrivalDate-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiDepartureDate">离开日期 <span class="required">*</span></label>
                                <input type="date" id="aiDepartureDate" name="departureDate" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiDepartureDate-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiFlightNo">航班/船只/交通工具号码 <span class="required">*</span></label>
                                <input type="text" id="aiFlightNo" name="flightNo" required placeholder="例：MH123"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiFlightNo-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiModeOfTravel">旅行方式 <span class="required">*</span></label>
                                <select id="aiModeOfTravel" name="modeOfTravel" required onchange="aiValidateField(this)">
                                    <option value="">请选择旅行方式</option>
                                    <option value="AIR">飞机</option>
                                    <option value="SEA">船只</option>
                                    <option value="LAND">陆路</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiModeOfTravel-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="aiLastPort">到达马来西亚前的最后一个港口 <span class="required">*</span></label>
                            <input type="text" id="aiLastPort" name="lastPort" required
                                   placeholder="例：Singapore Changi Airport" oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                            <div class="ai-suggestion-tooltip" id="aiLastPort-tooltip"></div>
                        </div>

                        <div class="form-group">
                            <label for="aiAccommodation">住宿安排 <span class="required">*</span></label>
                            <select id="aiAccommodation" name="accommodation" required onchange="aiValidateField(this)">
                                <option value="">请选择住宿类型</option>
                                <option value="HOTEL">酒店</option>
                                <option value="PRIVATE">私人住宅</option>
                                <option value="HOSTEL">青年旅社</option>
                                <option value="APARTMENT">公寓</option>
                                <option value="OTHERS">其他</option>
                            </select>
                            <div class="ai-suggestion-tooltip" id="aiAccommodation-tooltip"></div>
                        </div>
                    </div>

                    <!-- 马来西亚地址信息部分 -->
                    <div class="form-section">
                        <div class="section-title">
                            <span>🏠 马来西亚地址信息</span>
                            <button type="button" class="ai-helper-btn" onclick="aiHelp('address')">AI翻译</button>
                        </div>
                        <div class="note">
                            <strong>注意：</strong>请只输入字母数字字符。AI助手可以帮您将中文地址翻译为英文。
                        </div>

                        <div class="form-group">
                            <label for="aiAddress">地址 <span class="required">*</span></label>
                            <div style="position: relative;">
                                <textarea id="aiAddress" name="address" rows="3" required
                                          placeholder="请输入在马来西亚的详细地址（可输入中文，AI会帮您翻译）"
                                          oninput="aiValidateField(this)" onblur="aiSuggestField(this)"></textarea>
                                <button type="button" class="translate-btn" onclick="aiTranslateAddress()">🌐 翻译</button>
                            </div>
                            <div class="ai-suggestion-tooltip" id="aiAddress-tooltip"></div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiState">州 <span class="required">*</span></label>
                                <select id="aiState" name="state" required onchange="aiValidateField(this)">
                                    <option value="">请选择州</option>
                                    <option value="KUALA LUMPUR">吉隆坡</option>
                                    <option value="SELANGOR">雪兰莪</option>
                                    <option value="JOHOR">柔佛</option>
                                    <option value="PENANG">槟城</option>
                                    <option value="PERAK">霹雳</option>
                                    <option value="NEGERI SEMBILAN">森美兰</option>
                                    <option value="MALACCA">马六甲</option>
                                    <option value="PAHANG">彭亨</option>
                                    <option value="TERENGGANU">登嘉楼</option>
                                    <option value="KELANTAN">吉兰丹</option>
                                    <option value="KEDAH">吉打</option>
                                    <option value="PERLIS">玻璃市</option>
                                    <option value="SABAH">沙巴</option>
                                    <option value="SARAWAK">砂拉越</option>
                                    <option value="PUTRAJAYA">布城</option>
                                    <option value="LABUAN">纳闽</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiState-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiPostcode">邮政编码 <span class="required">*</span></label>
                                <input type="text" id="aiPostcode" name="postcode" required placeholder="例：50000"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiPostcode-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="aiCity">城市 <span class="required">*</span></label>
                            <input type="text" id="aiCity" name="city" required placeholder="例：Kuala Lumpur"
                                   oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                            <div class="ai-suggestion-tooltip" id="aiCity-tooltip"></div>
                        </div>
                    </div>

                    <div class="btn-container">
                        <button type="button" class="btn" onclick="aiValidateAndFillForm()">🚀 AI智能填充</button>
                        <button type="button" class="btn" onclick="aiClearForm()">🗑️ 清空表单</button>
                        <button type="button" class="btn" onclick="aiOptimizeForm()">✨ AI优化建议</button>
                    </div>
                </form>
            </div>

            <!-- 书签工具法 -->
            <div id="bookmarklet" class="method-content">
                <h2>📖 书签工具法（AI增强）</h2>

                <div class="note">
                    <strong>AI增强书签工具：</strong>集成了智能验证和错误检测功能的书签工具
                </div>

                <!-- 快速输入窗口 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>⚡ 快速输入窗口</span>
                        <button type="button" class="ai-helper-btn" onclick="aiQuickFill()">AI智能填充</button>
                    </div>
                    <div class="note">
                        <strong>快速模式：</strong>在此快速填写基本信息，AI会自动验证并生成书签数据
                    </div>

                    <div id="quickInputForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="qName">姓名（英文大写）<span class="required">*</span></label>
                                <input type="text" id="qName" placeholder="ZHANG SAN" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qName-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qPassport">护照号码<span class="required">*</span></label>
                                <input type="text" id="qPassport" placeholder="A12345678" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qPassport-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="qBirth">出生日期<span class="required">*</span></label>
                                <input type="date" id="qBirth" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qBirth-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qNationality">国籍<span class="required">*</span></label>
                                <select id="qNationality" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                    <option value="">选择国籍</option>
                                    <option value="CHINA">中国</option>
                                    <option value="SINGAPORE">新加坡</option>
                                    <option value="THAILAND">泰国</option>
                                    <option value="INDONESIA">印度尼西亚</option>
                                    <option value="PHILIPPINES">菲律宾</option>
                                    <option value="VIETNAM">越南</option>
                                    <option value="INDIA">印度</option>
                                    <option value="JAPAN">日本</option>
                                    <option value="SOUTH KOREA">韩国</option>
                                    <option value="UNITED STATES">美国</option>
                                    <option value="UNITED KINGDOM">英国</option>
                                    <option value="AUSTRALIA">澳大利亚</option>
                                    <option value="CANADA">加拿大</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="qNationality-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="qSex">性别<span class="required">*</span></label>
                                <select id="qSex" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                    <option value="">选择性别</option>
                                    <option value="MALE">男性</option>
                                    <option value="FEMALE">女性</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="qSex-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qPassportExpiry">护照到期日期<span class="required">*</span></label>
                                <input type="date" id="qPassportExpiry" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qPassportExpiry-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="qEmail">电子邮箱<span class="required">*</span></label>
                                <input type="email" id="qEmail" value="<EMAIL>" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qEmail-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qMobile">手机号码<span class="required">*</span></label>
                                <input type="text" id="qMobile" value="*********" placeholder="不包含国家代码" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qMobile-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="qArrival">到达日期<span class="required">*</span></label>
                                <input type="date" id="qArrival" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qArrival-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qDeparture">离开日期<span class="required">*</span></label>
                                <input type="date" id="qDeparture" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qDeparture-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="qFlight">航班号<span class="required">*</span></label>
                                <input type="text" id="qFlight" placeholder="MH123" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qFlight-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qLastPort">最后港口<span class="required">*</span></label>
                                <input type="text" id="qLastPort" placeholder="Singapore Changi Airport" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qLastPort-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="qAddress">马来西亚地址<span class="required">*</span></label>
                            <div style="position: relative;">
                                <textarea id="qAddress" placeholder="请输入在马来西亚的详细地址（可输入中文，AI会帮您翻译）"
                                          style="width:100%;margin:5px 0;padding:10px;height:60px;"
                                          oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)"></textarea>
                                <button type="button" class="translate-btn" onclick="aiTranslateQuickAddress()">🌐 翻译</button>
                            </div>
                            <div class="ai-suggestion-tooltip" id="qAddress-tooltip"></div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="qState">州<span class="required">*</span></label>
                                <select id="qState" style="width:100%;margin:5px 0;padding:10px;" onchange="aiValidateQuickField(this)">
                                    <option value="">请选择州</option>
                                    <option value="KUALA LUMPUR">吉隆坡</option>
                                    <option value="SELANGOR">雪兰莪</option>
                                    <option value="JOHOR">柔佛</option>
                                    <option value="PENANG">槟城</option>
                                    <option value="PERAK">霹雳</option>
                                    <option value="NEGERI SEMBILAN">森美兰</option>
                                    <option value="MALACCA">马六甲</option>
                                    <option value="PAHANG">彭亨</option>
                                    <option value="TERENGGANU">登嘉楼</option>
                                    <option value="KELANTAN">吉兰丹</option>
                                    <option value="KEDAH">吉打</option>
                                    <option value="PERLIS">玻璃市</option>
                                    <option value="SABAH">沙巴</option>
                                    <option value="SARAWAK">砂拉越</option>
                                    <option value="PUTRAJAYA">布城</option>
                                    <option value="LABUAN">纳闽</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="qState-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="qPostcode">邮政编码<span class="required">*</span></label>
                                <input type="text" id="qPostcode" placeholder="50000" style="width:100%;margin:5px 0;padding:10px;"
                                       oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)">
                                <div class="ai-suggestion-tooltip" id="qPostcode-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="qCity">城市<span class="required">*</span></label>
                            <input type="text" id="qCity" placeholder="Kuala Lumpur" style="width:100%;margin:5px 0;padding:10px;"
                                   oninput="aiValidateQuickField(this)" onblur="aiSuggestQuickField(this)">
                            <div class="ai-suggestion-tooltip" id="qCity-tooltip"></div>
                        </div>

                        <div class="btn-container">
                            <button type="button" class="btn" onclick="saveQuickData()">💾 保存数据</button>
                            <button type="button" class="btn" onclick="clearQuickForm()">🗑️ 清空表单</button>
                            <button type="button" class="btn" onclick="aiOptimizeQuickForm()">✨ AI优化</button>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <span>创建AI增强书签</span>
                    </div>
                    <p>将下面的代码拖拽到浏览器书签栏，或者复制代码手动创建书签：</p>
                    <div style="background: #333; color: #fff; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 15px 0; font-size: 12px;" id="aiBookmarkletCode">
                        javascript:(function(){var data=JSON.parse(localStorage.getItem('mdacFormData')||'{}');if(!data.name){alert('请先填写数据！');return;}function fillField(selector,value){var el=document.querySelector(selector);if(el){if(el.tagName==='SELECT'){for(var i=0;i<el.options.length;i++){if(el.options[i].value===value||el.options[i].text.includes(value)){el.value=el.options[i].value;break;}}}else{el.value=value;}el.dispatchEvent(new Event('change',{bubbles:true}));el.dispatchEvent(new Event('input',{bubbles:true}));return true;}return false;}setTimeout(function(){var count=0;count+=fillField('input[name*="name"]',data.name)?1:0;count+=fillField('input[name*="passport"]',data.passportNo)?1:0;count+=fillField('input[name*="birth"]',data.dateOfBirth)?1:0;count+=fillField('select[name*="nationality"]',data.nationality)?1:0;count+=fillField('select[name*="sex"]',data.sex)?1:0;count+=fillField('input[name*="expiry"]',data.passportExpiry)?1:0;count+=fillField('input[name*="email"]',data.email)?1:0;count+=fillField('input[name*="mobile"]',data.mobileNo)?1:0;count+=fillField('input[name*="arrival"]',data.arrivalDate)?1:0;count+=fillField('input[name*="departure"]',data.departureDate)?1:0;count+=fillField('input[name*="flight"]',data.flightNo)?1:0;count+=fillField('textarea[name*="address"]',data.address)?1:0;count+=fillField('input[name*="postcode"]',data.postcode)?1:0;count+=fillField('input[name*="city"]',data.city)?1:0;alert('AI增强填充完成！已填充'+count+'个字段。');},1000);})();
                    </div>
                    <button class="btn" onclick="copyBookmarklet()">📋 复制书签代码</button>
                    <button class="btn" onclick="openMDACWebsite()">🌐 打开MDAC网站</button>
                </div>
            </div>

            <!-- 手动复制法 -->
            <div id="manual" class="method-content">
                <h2>📋 手动复制法</h2>

                <div class="note">
                    <strong>适用于所有情况：</strong>100%可靠的手动填写方案
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <span>生成填充数据</span>
                    </div>
                    <button class="btn" onclick="generateManualData()">📊 生成复制数据</button>
                    <div id="manualData" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; display: none;">
                        <!-- 数据将在这里显示 -->
                    </div>
                    <button class="btn" onclick="copyManualData()" style="display:none;" id="copyManualBtn">📋 复制所有数据</button>
                </div>
            </div>

            <div id="status" class="status"></div>
        </div>
    </div>

    <script>
        // Gemini AI配置 - 硬编码API密钥（个人使用）
        const GEMINI_API_KEY = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
        const GEMINI_MODEL = 'gemini-2.5-flash-lite-preview-06-17';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

        // 全局变量
        let currentData = {};
        let aiCache = new Map(); // AI响应缓存
        let validationResults = new Map(); // 验证结果缓存

        /**
         * 调用Gemini AI API
         * @param {string} prompt - 提示词
         * @param {string} context - 上下文信息
         * @returns {Promise<string>} AI响应
         */
        async function callGeminiAI(prompt, context = '') {
            const cacheKey = prompt + context;

            // 检查缓存
            if (aiCache.has(cacheKey)) {
                return aiCache.get(cacheKey);
            }

            try {
                const fullPrompt = context ? `${context}\n\n${prompt}` : prompt;

                const response = await fetch(GEMINI_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: fullPrompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 1024,
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }

                const data = await response.json();
                const result = data.candidates[0].content.parts[0].text;

                // 缓存结果
                aiCache.set(cacheKey, result);

                return result;
            } catch (error) {
                console.error('Gemini AI调用失败:', error);
                return null;
            }
        }

        /**
         * 显示状态信息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         */
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        /**
         * 更新AI建议面板
         * @param {string} message - 建议内容
         */
        function updateAISuggestions(message) {
            const suggestionsDiv = document.getElementById('aiSuggestionsContent');
            suggestionsDiv.innerHTML = message;
        }

        /**
         * AI字段验证
         * @param {HTMLElement} field - 表单字段
         */
        async function aiValidateField(field) {
            const fieldName = field.name;
            const fieldValue = field.value;
            const fieldType = field.type || field.tagName.toLowerCase();

            if (!fieldValue) return;

            // 显示加载状态
            field.style.borderColor = '#ffc107';

            try {
                let prompt = '';
                let context = '你是一个专业的表单验证助手，专门帮助用户填写马来西亚数字入境卡(MDAC)。请简洁地回答，只给出验证结果和建议。';

                switch (fieldName) {
                    case 'name':
                        prompt = `验证这个姓名是否符合护照格式要求："${fieldValue}"。请检查是否为英文大写、格式是否正确。`;
                        break;
                    case 'passportNo':
                        prompt = `验证这个护照号码格式是否正确："${fieldValue}"。请检查格式和长度是否符合国际标准。`;
                        break;
                    case 'email':
                        prompt = `验证这个邮箱地址格式是否正确："${fieldValue}"。`;
                        break;
                    case 'mobileNo':
                        prompt = `验证这个手机号码格式是否正确："${fieldValue}"。这是马来西亚的手机号码，不包含国家代码。`;
                        break;
                    case 'flightNo':
                        prompt = `验证这个航班号格式是否正确："${fieldValue}"。请检查是否符合国际航班号标准。`;
                        break;
                    case 'postcode':
                        prompt = `验证这个马来西亚邮政编码是否正确："${fieldValue}"。马来西亚邮政编码通常是5位数字。`;
                        break;
                    default:
                        return; // 不需要AI验证的字段
                }

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    // 简单的结果解析
                    const isValid = result.toLowerCase().includes('正确') ||
                                   result.toLowerCase().includes('有效') ||
                                   result.toLowerCase().includes('符合');

                    if (isValid) {
                        field.classList.add('ai-validated');
                        field.classList.remove('ai-error');
                        validationResults.set(fieldName, { valid: true, message: result });
                    } else {
                        field.classList.add('ai-error');
                        field.classList.remove('ai-validated');
                        validationResults.set(fieldName, { valid: false, message: result });

                        // 显示错误提示
                        showTooltip(field, result);
                    }
                } else {
                    // AI调用失败，使用基础验证
                    basicValidateField(field);
                }
            } catch (error) {
                console.error('AI验证失败:', error);
                basicValidateField(field);
            }
        }

        /**
         * 基础字段验证（AI失败时的回退方案）
         * @param {HTMLElement} field - 表单字段
         */
        function basicValidateField(field) {
            const fieldName = field.name;
            const fieldValue = field.value;
            let isValid = true;
            let message = '';

            switch (fieldName) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    isValid = emailRegex.test(fieldValue);
                    message = isValid ? '邮箱格式正确' : '邮箱格式不正确';
                    break;
                case 'passportNo':
                    isValid = fieldValue.length >= 6 && fieldValue.length <= 12;
                    message = isValid ? '护照号码长度合适' : '护照号码长度应在6-12位之间';
                    break;
                case 'mobileNo':
                    const mobileRegex = /^[0-9]{8,12}$/;
                    isValid = mobileRegex.test(fieldValue);
                    message = isValid ? '手机号码格式正确' : '手机号码应为8-12位数字';
                    break;
                case 'postcode':
                    const postcodeRegex = /^[0-9]{5}$/;
                    isValid = postcodeRegex.test(fieldValue);
                    message = isValid ? '邮政编码格式正确' : '马来西亚邮政编码应为5位数字';
                    break;
            }

            if (isValid) {
                field.classList.add('ai-validated');
                field.classList.remove('ai-error');
            } else {
                field.classList.add('ai-error');
                field.classList.remove('ai-validated');
                showTooltip(field, message);
            }

            validationResults.set(fieldName, { valid: isValid, message: message });
        }

        /**
         * AI字段建议
         * @param {HTMLElement} field - 表单字段
         */
        async function aiSuggestField(field) {
            const fieldName = field.name;
            const fieldValue = field.value;

            if (!fieldValue) return;

            try {
                let prompt = '';
                let context = '你是一个专业的旅行助手，专门帮助用户填写马来西亚数字入境卡。请提供简洁实用的建议。';

                switch (fieldName) {
                    case 'flightNo':
                        prompt = `根据航班号"${fieldValue}"，请告诉我这是哪个航空公司的航班，通常从哪里飞往马来西亚。`;
                        break;
                    case 'lastPort':
                        prompt = `用户填写的最后港口是"${fieldValue}"，请确认这是否是一个有效的机场或港口名称，如果不准确请提供建议。`;
                        break;
                    case 'address':
                        if (containsChinese(fieldValue)) {
                            prompt = `请将这个中文地址翻译为英文："${fieldValue}"。要求：只使用字母数字字符，符合马来西亚地址格式。`;
                        }
                        break;
                    case 'city':
                        prompt = `确认"${fieldValue}"是否是马来西亚的有效城市名称，如果不是请提供建议。`;
                        break;
                }

                if (prompt) {
                    const result = await callGeminiAI(prompt, context);
                    if (result) {
                        showTooltip(field, result);
                        updateAISuggestions(`💡 AI建议：${result}`);
                    }
                }
            } catch (error) {
                console.error('AI建议失败:', error);
            }
        }

        /**
         * 检查文本是否包含中文
         * @param {string} text - 要检查的文本
         * @returns {boolean} 是否包含中文
         */
        function containsChinese(text) {
            return /[\u4e00-\u9fff]/.test(text);
        }

        /**
         * 显示字段提示
         * @param {HTMLElement} field - 表单字段
         * @param {string} message - 提示消息
         */
        function showTooltip(field, message) {
            const tooltipId = field.id + '-tooltip';
            const tooltip = document.getElementById(tooltipId);

            if (tooltip) {
                tooltip.textContent = message;
                tooltip.classList.add('show');

                setTimeout(() => {
                    tooltip.classList.remove('show');
                }, 5000);
            }
        }

        /**
         * AI地址翻译功能
         */
        async function aiTranslateAddress() {
            const addressField = document.getElementById('aiAddress');
            const addressValue = addressField.value;

            if (!addressValue) {
                showStatus('请先输入地址', 'error');
                return;
            }

            if (!containsChinese(addressValue)) {
                showStatus('地址已经是英文，无需翻译', 'info');
                return;
            }

            // 显示加载状态
            const translateBtn = document.querySelector('.translate-btn');
            const originalText = translateBtn.textContent;
            translateBtn.innerHTML = '<div class="loading"></div>';
            translateBtn.disabled = true;

            try {
                const prompt = `请将以下中文地址翻译为英文，要求：
1. 只使用字母、数字和基本标点符号
2. 符合马来西亚地址格式
3. 保持地址的准确性和可识别性
4. 如果是酒店名称，请保留英文酒店名

地址："${addressValue}"

请只返回翻译后的英文地址，不要包含其他说明。`;

                const context = '你是一个专业的地址翻译助手，专门处理马来西亚地址的中英文翻译。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    // 清理翻译结果，确保只包含字母数字字符
                    const cleanResult = result.replace(/[^\w\s,.-]/g, '').trim();
                    addressField.value = cleanResult;
                    showStatus('地址翻译完成！', 'success');
                    updateAISuggestions(`🌐 地址翻译完成：${cleanResult}`);

                    // 触发验证
                    aiValidateField(addressField);
                } else {
                    showStatus('翻译失败，请手动输入英文地址', 'error');
                }
            } catch (error) {
                console.error('地址翻译失败:', error);
                showStatus('翻译服务暂时不可用，请手动输入英文地址', 'error');
            } finally {
                // 恢复按钮状态
                translateBtn.textContent = originalText;
                translateBtn.disabled = false;
            }
        }

        /**
         * AI帮助功能
         * @param {string} section - 帮助的部分
         */
        async function aiHelp(section) {
            updateAISuggestions('<div class="loading"></div> AI正在分析，请稍候...');

            try {
                let prompt = '';
                let context = '你是一个专业的旅行助手，专门帮助用户填写马来西亚数字入境卡(MDAC)。请提供实用的建议和提醒。';

                switch (section) {
                    case 'personal':
                        prompt = `请为填写马来西亚数字入境卡的个人信息部分提供建议和注意事项。包括：
1. 姓名填写要求
2. 护照号码格式
3. 常见错误提醒
4. 填写技巧`;
                        break;
                    case 'travel':
                        prompt = `请为填写马来西亚数字入境卡的旅行信息部分提供建议，包括：
1. 日期选择建议
2. 航班信息填写
3. 住宿类型推荐
4. 常见入境港口`;
                        break;
                    case 'address':
                        prompt = `请为填写马来西亚地址信息提供建议，包括：
1. 地址格式要求
2. 常见的马来西亚城市和州
3. 邮政编码规则
4. 地址翻译注意事项`;
                        break;
                }

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`🤖 AI助手建议：<br><br>${result.replace(/\n/g, '<br>')}`);
                } else {
                    updateAISuggestions('AI助手暂时不可用，请参考表单提示信息。');
                }
            } catch (error) {
                console.error('AI帮助失败:', error);
                updateAISuggestions('AI助手暂时不可用，请参考表单提示信息。');
            }
        }

        /**
         * AI表单优化建议
         */
        async function aiOptimizeForm() {
            const formData = collectAIFormData();

            if (Object.keys(formData).length === 0) {
                showStatus('请先填写一些表单信息', 'error');
                return;
            }

            updateAISuggestions('<div class="loading"></div> AI正在分析您的表单，生成优化建议...');

            try {
                const prompt = `请分析以下MDAC表单数据，提供优化建议和潜在问题提醒：

表单数据：
${JSON.stringify(formData, null, 2)}

请检查：
1. 数据一致性
2. 格式正确性
3. 逻辑合理性
4. 完整性
5. 提供改进建议`;

                const context = '你是一个专业的表单审核专家，专门检查马来西亚数字入境卡的填写质量。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`✨ AI优化建议：<br><br>${result.replace(/\n/g, '<br>')}`);
                    showStatus('AI分析完成，请查看建议', 'success');
                } else {
                    updateAISuggestions('AI分析暂时不可用，请手动检查表单信息。');
                    showStatus('AI服务暂时不可用', 'error');
                }
            } catch (error) {
                console.error('AI优化失败:', error);
                updateAISuggestions('AI分析暂时不可用，请手动检查表单信息。');
                showStatus('AI服务暂时不可用', 'error');
            }
        }

        /**
         * 收集AI表单数据
         * @returns {Object} 表单数据
         */
        function collectAIFormData() {
            const form = document.getElementById('aiMdacForm');
            const formData = new FormData(form);
            const data = {};

            for (let [key, value] of formData.entries()) {
                if (value && value.trim() !== '') {
                    data[key] = value;
                }
            }

            return data;
        }

        /**
         * AI验证并填充表单
         */
        async function aiValidateAndFillForm() {
            showStatus('AI正在验证表单数据...', 'info');

            // 收集表单数据
            const formData = collectAIFormData();

            // 基础验证
            const requiredFields = [
                'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
                'passportExpiry', 'email', 'confirmEmail', 'countryCode', 'mobileNo',
                'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
                'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
            ];

            const missingFields = requiredFields.filter(field => !formData[field]);

            if (missingFields.length > 0) {
                showStatus(`请填写必填字段：${missingFields.map(getFieldLabel).join(', ')}`, 'error');
                return;
            }

            // 邮箱一致性检查
            if (formData.email !== formData.confirmEmail) {
                showStatus('电子邮箱和确认邮箱不一致', 'error');
                return;
            }

            // 日期逻辑检查
            const arrivalDate = new Date(formData.arrivalDate);
            const departureDate = new Date(formData.departureDate);
            const today = new Date();
            const threeDaysLater = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000);

            if (arrivalDate > threeDaysLater) {
                showStatus('到达日期必须在3天内（包括今天）', 'error');
                return;
            }

            if (departureDate <= arrivalDate) {
                showStatus('离开日期必须晚于到达日期', 'error');
                return;
            }

            // 格式化数据
            const mdacData = formatDataForMDAC(formData);

            // 存储数据
            localStorage.setItem('mdacFormData', JSON.stringify(mdacData));
            currentData = mdacData;

            showStatus('AI验证通过！数据已准备就绪', 'success');
            updateAISuggestions('✅ 表单验证完成！您可以选择使用书签工具或手动复制方式填充MDAC表单。');

            // 自动切换到书签工具标签
            showMethod('bookmarklet');
        }

        /**
         * 格式化数据为MDAC格式
         * @param {Object} formData - 原始表单数据
         * @returns {Object} 格式化后的数据
         */
        function formatDataForMDAC(formData) {
            return {
                // 个人信息
                name: formData.name.toUpperCase(),
                passportNo: formData.passportNo.toUpperCase(),
                dateOfBirth: formatDateForMDAC(formData.dateOfBirth),
                nationality: formData.nationality,
                sex: formData.sex,
                passportExpiry: formatDateForMDAC(formData.passportExpiry),
                email: formData.email.toLowerCase(),
                confirmEmail: formData.confirmEmail.toLowerCase(),
                countryCode: formData.countryCode,
                mobileNo: formData.mobileNo,

                // 旅行信息
                arrivalDate: formatDateForMDAC(formData.arrivalDate),
                departureDate: formatDateForMDAC(formData.departureDate),
                flightNo: formData.flightNo.toUpperCase(),
                modeOfTravel: formData.modeOfTravel,
                lastPort: formData.lastPort,
                accommodation: formData.accommodation,

                // 地址信息
                address: formData.address.toUpperCase(),
                state: formData.state,
                postcode: formData.postcode,
                city: formData.city.toUpperCase()
            };
        }

        /**
         * 格式化日期为MDAC格式
         * @param {string} dateString - 日期字符串
         * @returns {string} 格式化后的日期
         */
        function formatDateForMDAC(dateString) {
            const date = new Date(dateString);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        /**
         * 获取字段标签
         * @param {string} fieldName - 字段名
         * @returns {string} 字段标签
         */
        function getFieldLabel(fieldName) {
            const labels = {
                'name': '姓名',
                'passportNo': '护照号码',
                'dateOfBirth': '出生日期',
                'nationality': '国籍',
                'sex': '性别',
                'passportExpiry': '护照到期日期',
                'email': '电子邮箱',
                'confirmEmail': '确认电子邮箱',
                'countryCode': '国家/地区代码',
                'mobileNo': '手机号码',
                'arrivalDate': '到达日期',
                'departureDate': '离开日期',
                'flightNo': '航班号',
                'modeOfTravel': '旅行方式',
                'lastPort': '最后港口',
                'accommodation': '住宿安排',
                'address': '地址',
                'state': '州',
                'postcode': '邮政编码',
                'city': '城市'
            };
            return labels[fieldName] || fieldName;
        }

        /**
         * 清空AI表单
         */
        function aiClearForm() {
            if (confirm('确定要清空所有表单数据吗？')) {
                document.getElementById('aiMdacForm').reset();

                // 恢复默认值
                document.getElementById('aiEmail').value = '<EMAIL>';
                document.getElementById('aiConfirmEmail').value = '<EMAIL>';
                document.getElementById('aiCountryCode').value = '+60';
                document.getElementById('aiMobileNo').value = '*********';

                // 清除验证状态
                document.querySelectorAll('.ai-validated, .ai-error').forEach(el => {
                    el.classList.remove('ai-validated', 'ai-error');
                });

                // 清除缓存
                validationResults.clear();

                showStatus('表单已清空', 'info');
                updateAISuggestions('表单已重置，请重新填写信息。');
            }
        }

        /**
         * 显示不同的方法
         * @param {string} method - 方法名称
         */
        function showMethod(method) {
            // 隐藏所有内容
            document.querySelectorAll('.method-content').forEach(el => {
                el.classList.remove('active');
            });

            // 移除所有按钮的active状态
            document.querySelectorAll('.method-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(method).classList.add('active');

            // 激活对应按钮
            event.target.classList.add('active');
        }

        /**
         * 复制书签代码
         */
        function copyBookmarklet() {
            const code = document.getElementById('aiBookmarkletCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                showStatus('AI增强书签代码已复制！请在浏览器中创建新书签并粘贴此代码。', 'success');
            }).catch(() => {
                // 回退方案
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('书签代码已复制！', 'success');
            });
        }

        /**
         * 打开MDAC官方网站
         */
        function openMDACWebsite() {
            const mdacUrl = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';
            window.open(mdacUrl, 'mdacWindow', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            showStatus('MDAC官方网站已打开，请在新窗口中使用书签工具', 'info');
        }

        /**
         * 生成手动复制数据
         */
        function generateManualData() {
            if (!currentData.name) {
                showStatus('请先在AI智能表单中填写并验证数据！', 'error');
                return;
            }

            const manualText = `
=== MDAC表单填充数据 ===

个人信息：
姓名: ${currentData.name}
护照号码: ${currentData.passportNo}
出生日期: ${currentData.dateOfBirth}
国籍: ${currentData.nationality}
性别: ${currentData.sex}
护照到期日期: ${currentData.passportExpiry}
电子邮箱: ${currentData.email}
确认邮箱: ${currentData.confirmEmail}
国家代码: ${currentData.countryCode}
手机号码: ${currentData.mobileNo}

旅行信息：
到达日期: ${currentData.arrivalDate}
离开日期: ${currentData.departureDate}
航班号: ${currentData.flightNo}
旅行方式: ${currentData.modeOfTravel}
最后港口: ${currentData.lastPort}
住宿安排: ${currentData.accommodation}

地址信息：
地址: ${currentData.address}
州: ${currentData.state}
邮政编码: ${currentData.postcode}
城市: ${currentData.city}

=== AI验证状态 ===
${Array.from(validationResults.entries()).map(([field, result]) =>
    `${getFieldLabel(field)}: ${result.valid ? '✅ 通过' : '❌ 需要检查'}`
).join('\n')}
            `;

            document.getElementById('manualData').textContent = manualText;
            document.getElementById('manualData').style.display = 'block';
            document.getElementById('copyManualBtn').style.display = 'inline-block';
        }

        /**
         * 复制手动数据
         */
        function copyManualData() {
            const text = document.getElementById('manualData').textContent;
            navigator.clipboard.writeText(text).then(() => {
                showStatus('数据已复制到剪贴板！', 'success');
            }).catch(() => {
                // 回退方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('数据已复制！', 'success');
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期 - AI智能表单
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('aiArrivalDate').value = tomorrow.toISOString().split('T')[0];

            const dayAfter = new Date();
            dayAfter.setDate(dayAfter.getDate() + 3);
            document.getElementById('aiDepartureDate').value = dayAfter.toISOString().split('T')[0];

            // 设置护照到期日期的最小值
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('aiPassportExpiry').min = today;

            // 初始化快速表单
            initializeQuickForm();

            // 尝试从localStorage恢复数据
            const savedData = localStorage.getItem('mdacFormData');
            if (savedData) {
                currentData = JSON.parse(savedData);
                updateAISuggestions('检测到之前保存的数据，您可以直接使用书签工具或手动复制方式填充表单。');
            }

            // 初始化AI助手
            updateAISuggestions('🤖 AI助手已就绪！您可以选择：<br>1. 🤖 AI智能表单 - 完整的AI辅助体验<br>2. ⚡ 快速输入窗口 - 在书签工具中快速填写<br>3. 📋 手动复制 - 传统的复制粘贴方式');
            showStatus('MDAC AI智能填充工具已就绪', 'success');

            // 测试AI连接
            setTimeout(async () => {
                try {
                    const testResult = await callGeminiAI('请回复"AI连接正常"', '这是一个连接测试');
                    if (testResult) {
                        console.log('Gemini AI连接正常');
                        updateAISuggestions('🤖 AI助手已就绪！Gemini AI连接正常，所有智能功能可用。<br><br>💡 使用提示：<br>• 绿色边框 = AI验证通过<br>• 红色边框 = 需要检查<br>• 点击"AI建议"获取专业指导<br>• 支持中文地址智能翻译');
                    }
                } catch (error) {
                    console.warn('Gemini AI连接测试失败，将使用基础功能', error);
                    updateAISuggestions('⚠️ AI服务连接异常，将使用基础验证功能。所有核心功能仍然可用。<br><br>📝 基础功能包括：<br>• 表单验证和格式检查<br>• 自动填充和数据保存<br>• 书签工具和手动复制');
                }
            }, 2000);
        });

        /**
         * 快速输入窗口的AI验证
         * @param {HTMLElement} field - 表单字段
         */
        async function aiValidateQuickField(field) {
            // 复用主表单的验证逻辑
            await aiValidateField(field);
        }

        /**
         * 快速输入窗口的AI建议
         * @param {HTMLElement} field - 表单字段
         */
        async function aiSuggestQuickField(field) {
            // 复用主表单的建议逻辑
            await aiSuggestField(field);
        }

        /**
         * 快速地址翻译
         */
        async function aiTranslateQuickAddress() {
            const addressField = document.getElementById('qAddress');
            const addressValue = addressField.value;

            if (!addressValue) {
                showStatus('请先输入地址', 'error');
                return;
            }

            if (!containsChinese(addressValue)) {
                showStatus('地址已经是英文，无需翻译', 'info');
                return;
            }

            // 显示加载状态
            const translateBtn = document.querySelector('#bookmarklet .translate-btn');
            const originalText = translateBtn.textContent;
            translateBtn.innerHTML = '<div class="loading"></div>';
            translateBtn.disabled = true;

            try {
                const prompt = `请将以下中文地址翻译为英文，要求：
1. 只使用字母、数字和基本标点符号
2. 符合马来西亚地址格式
3. 保持地址的准确性和可识别性
4. 如果是酒店名称，请保留英文酒店名

地址："${addressValue}"

请只返回翻译后的英文地址，不要包含其他说明。`;

                const context = '你是一个专业的地址翻译助手，专门处理马来西亚地址的中英文翻译。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    // 清理翻译结果，确保只包含字母数字字符
                    const cleanResult = result.replace(/[^\w\s,.-]/g, '').trim();
                    addressField.value = cleanResult;
                    showStatus('地址翻译完成！', 'success');
                    updateAISuggestions(`🌐 地址翻译完成：${cleanResult}`);

                    // 触发验证
                    aiValidateQuickField(addressField);
                } else {
                    showStatus('翻译失败，请手动输入英文地址', 'error');
                }
            } catch (error) {
                console.error('地址翻译失败:', error);
                showStatus('翻译服务暂时不可用，请手动输入英文地址', 'error');
            } finally {
                // 恢复按钮状态
                translateBtn.textContent = originalText;
                translateBtn.disabled = false;
            }
        }

        /**
         * AI智能快速填充
         */
        async function aiQuickFill() {
            updateAISuggestions('<div class="loading"></div> AI正在分析您的信息，生成智能建议...');

            try {
                const formData = collectQuickFormData();

                if (Object.keys(formData).length === 0) {
                    showStatus('请先填写一些基本信息', 'error');
                    updateAISuggestions('请先填写基本信息，AI会根据您的输入提供智能建议。');
                    return;
                }

                const prompt = `请分析以下MDAC表单数据，提供智能填写建议：

已填写数据：
${JSON.stringify(formData, null, 2)}

请提供：
1. 数据验证结果
2. 缺失字段提醒
3. 智能填写建议
4. 潜在问题警告
5. 优化建议`;

                const context = '你是一个专业的MDAC表单填写助手，请提供实用的建议。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`🤖 AI智能分析结果：<br><br>${result.replace(/\n/g, '<br>')}`);
                    showStatus('AI分析完成，请查看建议', 'success');
                } else {
                    updateAISuggestions('AI分析暂时不可用，请继续填写表单。');
                    showStatus('AI服务暂时不可用', 'error');
                }
            } catch (error) {
                console.error('AI智能填充失败:', error);
                updateAISuggestions('AI分析暂时不可用，请继续填写表单。');
                showStatus('AI服务暂时不可用', 'error');
            }
        }

        /**
         * 收集快速表单数据
         * @returns {Object} 表单数据
         */
        function collectQuickFormData() {
            const fields = [
                'qName', 'qPassport', 'qBirth', 'qNationality', 'qSex',
                'qPassportExpiry', 'qEmail', 'qMobile', 'qArrival', 'qDeparture',
                'qFlight', 'qLastPort', 'qAddress', 'qState', 'qPostcode', 'qCity'
            ];

            const data = {};

            fields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element && element.value && element.value.trim() !== '') {
                    const fieldName = fieldId.substring(1).toLowerCase(); // 移除'q'前缀
                    data[fieldName] = element.value;
                }
            });

            return data;
        }

        /**
         * 保存快速表单数据
         */
        function saveQuickData() {
            const formData = collectQuickFormData();

            // 验证必填字段
            const requiredFields = ['name', 'passport', 'birth', 'nationality', 'sex', 'passportexpiry', 'email', 'mobile', 'arrival', 'departure', 'flight', 'lastport', 'address', 'state', 'postcode', 'city'];
            const missingFields = requiredFields.filter(field => !formData[field]);

            if (missingFields.length > 0) {
                showStatus(`请填写必填字段：${missingFields.join(', ')}`, 'error');
                return;
            }

            // 格式化数据
            const mdacData = {
                name: formData.name.toUpperCase(),
                passportNo: formData.passport.toUpperCase(),
                dateOfBirth: formatDateForMDAC(formData.birth),
                nationality: formData.nationality,
                sex: formData.sex,
                passportExpiry: formatDateForMDAC(formData.passportexpiry),
                email: formData.email.toLowerCase(),
                confirmEmail: formData.email.toLowerCase(),
                countryCode: '+60',
                mobileNo: formData.mobile,
                arrivalDate: formatDateForMDAC(formData.arrival),
                departureDate: formatDateForMDAC(formData.departure),
                flightNo: formData.flight.toUpperCase(),
                modeOfTravel: 'AIR',
                lastPort: formData.lastport,
                accommodation: 'HOTEL',
                address: formData.address.toUpperCase(),
                state: formData.state,
                postcode: formData.postcode,
                city: formData.city.toUpperCase()
            };

            // 保存到localStorage
            localStorage.setItem('mdacFormData', JSON.stringify(mdacData));
            currentData = mdacData;

            showStatus('数据已保存！现在可以使用书签工具进行自动填充。', 'success');
            updateAISuggestions('✅ 数据保存成功！您可以：<br>1. 使用下方的书签工具一键填充<br>2. 打开MDAC官网并使用书签<br>3. 切换到手动复制模式');
        }

        /**
         * 清空快速表单
         */
        function clearQuickForm() {
            if (confirm('确定要清空快速输入表单吗？')) {
                const fields = [
                    'qName', 'qPassport', 'qBirth', 'qNationality', 'qSex',
                    'qPassportExpiry', 'qEmail', 'qMobile', 'qArrival', 'qDeparture',
                    'qFlight', 'qLastPort', 'qAddress', 'qState', 'qPostcode', 'qCity'
                ];

                fields.forEach(fieldId => {
                    const element = document.getElementById(fieldId);
                    if (element) {
                        element.value = '';
                        element.classList.remove('ai-validated', 'ai-error');
                    }
                });

                // 恢复默认值
                document.getElementById('qEmail').value = '<EMAIL>';
                document.getElementById('qMobile').value = '*********';

                showStatus('快速表单已清空', 'info');
                updateAISuggestions('快速表单已重置，请重新填写信息。');
            }
        }

        /**
         * AI优化快速表单
         */
        async function aiOptimizeQuickForm() {
            const formData = collectQuickFormData();

            if (Object.keys(formData).length === 0) {
                showStatus('请先填写一些表单信息', 'error');
                return;
            }

            updateAISuggestions('<div class="loading"></div> AI正在优化您的表单数据...');

            try {
                const prompt = `请优化以下MDAC快速表单数据：

当前数据：
${JSON.stringify(formData, null, 2)}

请提供：
1. 数据完整性检查
2. 格式优化建议
3. 逻辑一致性验证
4. 填写质量评估
5. 具体改进建议`;

                const context = '你是一个专业的表单优化专家，专门优化MDAC表单数据质量。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`✨ AI优化建议：<br><br>${result.replace(/\n/g, '<br>')}`);
                    showStatus('AI优化分析完成', 'success');
                } else {
                    updateAISuggestions('AI优化暂时不可用，请手动检查表单信息。');
                    showStatus('AI服务暂时不可用', 'error');
                }
            } catch (error) {
                console.error('AI优化失败:', error);
                updateAISuggestions('AI优化暂时不可用，请手动检查表单信息。');
                showStatus('AI服务暂时不可用', 'error');
            }
        }

        // 防止表单意外提交
        document.getElementById('aiMdacForm').addEventListener('submit', function(e) {
            e.preventDefault();
            aiValidateAndFillForm();
        });

        // 初始化快速表单的默认日期
        function initializeQuickForm() {
            // 设置默认日期
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('qArrival').value = tomorrow.toISOString().split('T')[0];

            const dayAfter = new Date();
            dayAfter.setDate(dayAfter.getDate() + 3);
            document.getElementById('qDeparture').value = dayAfter.toISOString().split('T')[0];

            // 设置护照到期日期的最小值
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('qPassportExpiry').min = today;
        }
    </script>
</body>
</html>