<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能填充工具 - Gemini驱动</title>
    <style>
        /* 基础样式设置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .ai-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
        }

        .content {
            padding: 30px;
        }

        .ai-assistant {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            position: relative;
        }

        .ai-assistant h3 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ai-status {
            display: inline-block;
            width: 10px;
            height: 10px;
            background: #4ade80;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .ai-suggestions {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            min-height: 60px;
            backdrop-filter: blur(10px);
        }

        .method-selector {
            margin-bottom: 30px;
            text-align: center;
        }

        .method-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            position: relative;
        }

        .method-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .method-btn.active {
            background: #48bb78;
        }

        .method-btn.ai-enhanced::after {
            content: "🤖";
            position: absolute;
            top: -5px;
            right: -5px;
            font-size: 12px;
        }

        .method-content {
            display: none;
            margin-top: 30px;
        }

        .method-content.active {
            display: block;
        }

        .form-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
        }

        .section-title {
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .ai-helper-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .ai-helper-btn:hover {
            transform: scale(1.05);
        }

        .form-group {
            margin-bottom: 15px;
            position: relative;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .required {
            color: #ff4757;
        }

        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .ai-validated {
            border-color: #28a745 !important;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
        }

        .ai-error {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
        }

        .ai-suggestion-tooltip {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: #333;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            margin-top: 5px;
        }

        .ai-suggestion-tooltip.show {
            display: block;
        }

        .translate-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: #17a2b8;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 11px;
        }

        .btn-container {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 16px;
            border-radius: 25px;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin: 0 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .note strong {
            display: block;
            margin-bottom: 5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .container {
                margin: 10px;
            }

            .header {
                padding: 20px;
            }

            .content {
                padding: 20px;
            }

            .ai-badge {
                position: static;
                display: inline-block;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="ai-badge">🤖 Gemini AI 驱动</div>
            <h1>🇲🇾 MDAC AI智能填充工具</h1>
            <p>集成Gemini AI的智能表单填充助手，让入境卡填写更简单、更准确</p>
        </div>

        <div class="content">
            <!-- AI助手面板 -->
            <div class="ai-assistant">
                <h3>
                    <span>🤖 AI智能助手</span>
                    <span class="ai-status"></span>
                </h3>
                <p>我是您的专属AI助手，可以帮您智能填写表单、验证信息、翻译地址，并提供旅行建议。</p>
                <div class="ai-suggestions" id="aiSuggestions">
                    <div id="aiSuggestionsContent">准备就绪，开始填写表单时我会为您提供智能建议...</div>
                </div>
            </div>

            <div class="note">
                <strong>🚀 AI增强功能：</strong>
                • 智能表单建议和自动补全<br>
                • 实时数据验证和纠错<br>
                • 旅行信息智能推荐<br>
                • 中英文地址智能翻译<br>
                • 多种自动填充方案
            </div>

            <div class="method-selector">
                <button class="method-btn ai-enhanced active" onclick="showMethod('smart-form')">🤖 AI智能表单</button>
                <button class="method-btn ai-enhanced" onclick="showMethod('bookmarklet')">📖 书签工具</button>
                <button class="method-btn ai-enhanced" onclick="showMethod('api-submit')">🚀 API直接提交</button>
                <button class="method-btn" onclick="showMethod('manual')">📋 手动复制</button>
            </div>

            <!-- AI智能表单 -->
            <div id="smart-form" class="method-content active">
                <h2>🤖 AI智能表单填写</h2>

                <div class="note">
                    <strong>智能填写模式：</strong>AI助手会根据您的输入提供智能建议、自动验证数据准确性，并帮助翻译地址信息。
                </div>

                <form id="aiMdacForm">
                    <!-- 个人信息部分 -->
                    <div class="form-section">
                        <div class="section-title">
                            <span>📋 个人信息</span>
                            <button type="button" class="ai-helper-btn" onclick="aiHelp('personal')">AI建议</button>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiName">姓名 <span class="required">*</span></label>
                                <input type="text" id="aiName" name="name" required placeholder="请输入护照上的英文姓名"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiName-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiPassportNo">护照号码 <span class="required">*</span></label>
                                <input type="text" id="aiPassportNo" name="passportNo" required placeholder="例：A12345678"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiPassportNo-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiDateOfBirth">出生日期 <span class="required">*</span></label>
                                <input type="date" id="aiDateOfBirth" name="dateOfBirth" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiDateOfBirth-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiNationality">国籍 <span class="required">*</span></label>
                                <select id="aiNationality" name="nationality" required onchange="aiValidateField(this)">
                                    <option value="">请选择国籍</option>
                                    <option value="CHINA">中国</option>
                                    <option value="SINGAPORE">新加坡</option>
                                    <option value="THAILAND">泰国</option>
                                    <option value="INDONESIA">印度尼西亚</option>
                                    <option value="PHILIPPINES">菲律宾</option>
                                    <option value="VIETNAM">越南</option>
                                    <option value="INDIA">印度</option>
                                    <option value="JAPAN">日本</option>
                                    <option value="SOUTH KOREA">韩国</option>
                                    <option value="UNITED STATES">美国</option>
                                    <option value="UNITED KINGDOM">英国</option>
                                    <option value="AUSTRALIA">澳大利亚</option>
                                    <option value="CANADA">加拿大</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiNationality-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiSex">性别 <span class="required">*</span></label>
                                <select id="aiSex" name="sex" required onchange="aiValidateField(this)">
                                    <option value="">请选择性别</option>
                                    <option value="MALE">男性</option>
                                    <option value="FEMALE">女性</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiSex-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiPassportExpiry">护照到期日期 <span class="required">*</span></label>
                                <input type="date" id="aiPassportExpiry" name="passportExpiry" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiPassportExpiry-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiEmail">电子邮箱 <span class="required">*</span></label>
                                <input type="email" id="aiEmail" name="email" value="<EMAIL>" required
                                       oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiEmail-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiConfirmEmail">确认电子邮箱 <span class="required">*</span></label>
                                <input type="email" id="aiConfirmEmail" name="confirmEmail" value="<EMAIL>" required
                                       oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiConfirmEmail-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiCountryCode">国家/地区代码 <span class="required">*</span></label>
                                <input type="text" id="aiCountryCode" name="countryCode" value="+60" required
                                       oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiCountryCode-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiMobileNo">手机号码 <span class="required">*</span></label>
                                <input type="tel" id="aiMobileNo" name="mobileNo" value="*********" required
                                       placeholder="不包含国家代码" oninput="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiMobileNo-tooltip"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 旅行信息部分 -->
                    <div class="form-section">
                        <div class="section-title">
                            <span>✈️ 旅行信息</span>
                            <button type="button" class="ai-helper-btn" onclick="aiHelp('travel')">AI推荐</button>
                        </div>
                        <div class="note">
                            <strong>重要提醒：</strong>您的行程必须在3天内（包括提交日期）
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiArrivalDate">到达日期 <span class="required">*</span></label>
                                <input type="date" id="aiArrivalDate" name="arrivalDate" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiArrivalDate-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiDepartureDate">离开日期 <span class="required">*</span></label>
                                <input type="date" id="aiDepartureDate" name="departureDate" required
                                       onchange="aiValidateField(this)">
                                <div class="ai-suggestion-tooltip" id="aiDepartureDate-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiFlightNo">航班/船只/交通工具号码 <span class="required">*</span></label>
                                <input type="text" id="aiFlightNo" name="flightNo" required placeholder="例：MH123"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiFlightNo-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiModeOfTravel">旅行方式 <span class="required">*</span></label>
                                <select id="aiModeOfTravel" name="modeOfTravel" required onchange="aiValidateField(this)">
                                    <option value="">请选择旅行方式</option>
                                    <option value="AIR">飞机</option>
                                    <option value="SEA">船只</option>
                                    <option value="LAND">陆路</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiModeOfTravel-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="aiLastPort">到达马来西亚前的最后一个港口 <span class="required">*</span></label>
                            <input type="text" id="aiLastPort" name="lastPort" required
                                   placeholder="例：Singapore Changi Airport" oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                            <div class="ai-suggestion-tooltip" id="aiLastPort-tooltip"></div>
                        </div>

                        <div class="form-group">
                            <label for="aiAccommodation">住宿安排 <span class="required">*</span></label>
                            <select id="aiAccommodation" name="accommodation" required onchange="aiValidateField(this)">
                                <option value="">请选择住宿类型</option>
                                <option value="HOTEL">酒店</option>
                                <option value="PRIVATE">私人住宅</option>
                                <option value="HOSTEL">青年旅社</option>
                                <option value="APARTMENT">公寓</option>
                                <option value="OTHERS">其他</option>
                            </select>
                            <div class="ai-suggestion-tooltip" id="aiAccommodation-tooltip"></div>
                        </div>
                    </div>

                    <!-- 马来西亚地址信息部分 -->
                    <div class="form-section">
                        <div class="section-title">
                            <span>🏠 马来西亚地址信息</span>
                            <button type="button" class="ai-helper-btn" onclick="aiHelp('address')">AI翻译</button>
                        </div>
                        <div class="note">
                            <strong>注意：</strong>请只输入字母数字字符。AI助手可以帮您将中文地址翻译为英文。
                        </div>

                        <div class="form-group">
                            <label for="aiAddress">地址 <span class="required">*</span></label>
                            <div style="position: relative;">
                                <textarea id="aiAddress" name="address" rows="3" required
                                          placeholder="请输入在马来西亚的详细地址（可输入中文，AI会帮您翻译）"
                                          oninput="aiValidateField(this)" onblur="aiSuggestField(this)"></textarea>
                                <button type="button" class="translate-btn" onclick="aiTranslateAddress()">🌐 翻译</button>
                            </div>
                            <div class="ai-suggestion-tooltip" id="aiAddress-tooltip"></div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="aiState">州 <span class="required">*</span></label>
                                <select id="aiState" name="state" required onchange="aiValidateField(this)">
                                    <option value="">请选择州</option>
                                    <option value="KUALA LUMPUR">吉隆坡</option>
                                    <option value="SELANGOR">雪兰莪</option>
                                    <option value="JOHOR">柔佛</option>
                                    <option value="PENANG">槟城</option>
                                    <option value="PERAK">霹雳</option>
                                    <option value="NEGERI SEMBILAN">森美兰</option>
                                    <option value="MALACCA">马六甲</option>
                                    <option value="PAHANG">彭亨</option>
                                    <option value="TERENGGANU">登嘉楼</option>
                                    <option value="KELANTAN">吉兰丹</option>
                                    <option value="KEDAH">吉打</option>
                                    <option value="PERLIS">玻璃市</option>
                                    <option value="SABAH">沙巴</option>
                                    <option value="SARAWAK">砂拉越</option>
                                    <option value="PUTRAJAYA">布城</option>
                                    <option value="LABUAN">纳闽</option>
                                </select>
                                <div class="ai-suggestion-tooltip" id="aiState-tooltip"></div>
                            </div>
                            <div class="form-group">
                                <label for="aiPostcode">邮政编码 <span class="required">*</span></label>
                                <input type="text" id="aiPostcode" name="postcode" required placeholder="例：50000"
                                       oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                                <div class="ai-suggestion-tooltip" id="aiPostcode-tooltip"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="aiCity">城市 <span class="required">*</span></label>
                            <input type="text" id="aiCity" name="city" required placeholder="例：Kuala Lumpur"
                                   oninput="aiValidateField(this)" onblur="aiSuggestField(this)">
                            <div class="ai-suggestion-tooltip" id="aiCity-tooltip"></div>
                        </div>
                    </div>

                    <div class="btn-container">
                        <button type="button" class="btn" onclick="aiValidateAndFillForm()">🚀 AI智能填充</button>
                        <button type="button" class="btn" onclick="aiClearForm()">🗑️ 清空表单</button>
                        <button type="button" class="btn" onclick="aiOptimizeForm()">✨ AI优化建议</button>
                    </div>
                </form>
            </div>

            <!-- 书签工具法 -->
            <div id="bookmarklet" class="method-content">
                <h2>📖 书签工具法（AI增强）</h2>

                <div class="note">
                    <strong>AI增强书签工具：</strong>集成了智能验证和错误检测功能的书签工具
                </div>

                <!-- 智能内容输入框 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>🤖 AI智能内容解析</span>
                        <button type="button" class="ai-helper-btn" onclick="aiParseContent()">🧠 AI解析</button>
                    </div>
                    <div class="note">
                        <strong>智能解析模式：</strong>在下方输入框中粘贴或输入您的所有信息，AI会自动识别并填充到对应字段
                    </div>

                    <div class="form-group">
                        <label for="contentInput">📝 内容输入框</label>
                        <textarea id="contentInput" placeholder="请在此输入或粘贴您的所有信息，例如：

姓名：ZHANG SAN
护照号：A12345678
出生日期：1990-01-01
国籍：中国
性别：男
护照到期：2030-12-31
邮箱：<EMAIL>
手机：*********
到达日期：2025-01-10
离开日期：2025-01-15
航班号：MH123
最后港口：新加坡樟宜机场
地址：吉隆坡市中心酒店
州：吉隆坡
邮编：50000
城市：吉隆坡

AI会自动识别这些信息并填充到表单中..."
                                  style="width:100%; height:300px; padding:15px; border:2px solid #ddd; border-radius:8px; font-size:14px; line-height:1.5; font-family: 'Microsoft YaHei', Arial, sans-serif;"
                                  oninput="aiRealTimeAnalysis()"></textarea>
                        <div class="ai-suggestion-tooltip" id="contentInput-tooltip"></div>
                    </div>

                    <div class="btn-container">
                        <button type="button" class="btn" onclick="aiParseContent()">🧠 AI智能解析</button>
                        <button type="button" class="btn" onclick="clearContentInput()">🗑️ 清空内容</button>
                        <button type="button" class="btn" onclick="aiOptimizeContent()">✨ AI优化建议</button>
                    </div>

                    <!-- 解析结果显示区域 -->
                    <div id="parseResults" style="display:none; margin-top:20px;">
                        <div class="section-title">
                            <span>📊 AI解析结果</span>
                        </div>
                        <div id="parseResultsContent" style="background:#f8f9fa; padding:15px; border-radius:8px; font-family:monospace; white-space:pre-wrap;"></div>
                        <div class="btn-container" style="margin-top:15px;">
                            <button type="button" class="btn" onclick="applyParsedData()">✅ 应用解析结果</button>
                            <button type="button" class="btn" onclick="editParsedData()">✏️ 手动编辑</button>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <span>创建AI增强书签</span>
                    </div>
                    <p>将下面的代码拖拽到浏览器书签栏，或者复制代码手动创建书签：</p>
                    <div style="background: #333; color: #fff; padding: 15px; border-radius: 5px; font-family: monospace; word-break: break-all; margin: 15px 0; font-size: 12px;" id="aiBookmarkletCode">
                        javascript:(function(){var data=JSON.parse(localStorage.getItem('mdacFormData')||'{}');if(!data.name){alert('请先填写数据！');return;}function fillField(selector,value){var el=document.querySelector(selector);if(el){if(el.tagName==='SELECT'){for(var i=0;i<el.options.length;i++){if(el.options[i].value===value||el.options[i].text.includes(value)){el.value=el.options[i].value;break;}}}else{el.value=value;}el.dispatchEvent(new Event('change',{bubbles:true}));el.dispatchEvent(new Event('input',{bubbles:true}));return true;}return false;}setTimeout(function(){var count=0;count+=fillField('input[name*="name"]',data.name)?1:0;count+=fillField('input[name*="passport"]',data.passportNo)?1:0;count+=fillField('input[name*="birth"]',data.dateOfBirth)?1:0;count+=fillField('select[name*="nationality"]',data.nationality)?1:0;count+=fillField('select[name*="sex"]',data.sex)?1:0;count+=fillField('input[name*="expiry"]',data.passportExpiry)?1:0;count+=fillField('input[name*="email"]',data.email)?1:0;count+=fillField('input[name*="mobile"]',data.mobileNo)?1:0;count+=fillField('input[name*="arrival"]',data.arrivalDate)?1:0;count+=fillField('input[name*="departure"]',data.departureDate)?1:0;count+=fillField('input[name*="flight"]',data.flightNo)?1:0;count+=fillField('textarea[name*="address"]',data.address)?1:0;count+=fillField('input[name*="postcode"]',data.postcode)?1:0;count+=fillField('input[name*="city"]',data.city)?1:0;alert('AI增强填充完成！已填充'+count+'个字段。');},1000);})();
                    </div>
                    <button class="btn" onclick="copyBookmarklet()">📋 复制书签代码</button>
                    <button class="btn" onclick="openMDACWebsite()">🌐 打开MDAC网站</button>
                </div>
            </div>

            <!-- API直接提交 -->
            <div id="api-submit" class="method-content">
                <h2>🚀 API直接提交（实验性功能）</h2>

                <div class="note" style="background: #fff3cd; border-color: #ffeaa7;">
                    <strong>⚠️ 重要提醒：</strong>
                    此功能为实验性技术演示，使用前请了解相关风险：<br>
                    • 可能违反网站使用条款<br>
                    • 政府网站通常不允许自动化提交<br>
                    • 仅供技术研究和学习使用<br>
                    • 建议优先使用官方网站提交
                </div>

                <!-- 网络请求分析 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>🔍 网络请求分析</span>
                        <button type="button" class="ai-helper-btn" onclick="startNetworkAnalysis()">开始分析</button>
                    </div>
                    <div class="note">
                        <strong>分析步骤：</strong>
                        1. 点击"开始分析"打开MDAC网站<br>
                        2. 系统会自动捕获网络请求<br>
                        3. 分析API接口结构和参数<br>
                        4. 生成直接提交方案
                    </div>

                    <div id="networkAnalysisStatus" style="margin: 15px 0; padding: 10px; border-radius: 5px; display: none;">
                        <div id="analysisStatusText"></div>
                    </div>

                    <div class="btn-container">
                        <button type="button" class="btn" onclick="startNetworkAnalysis()">🔍 开始网络分析</button>
                        <button type="button" class="btn" onclick="stopNetworkAnalysis()">⏹️ 停止分析</button>
                        <button type="button" class="btn" onclick="showAnalysisResults()">📊 查看结果</button>
                    </div>
                </div>

                <!-- API接口信息 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>📡 API接口信息</span>
                    </div>
                    <div id="apiInfo" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; display: none;">
                        等待网络分析结果...
                    </div>
                </div>

                <!-- 直接提交功能 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>🚀 直接提交</span>
                        <button type="button" class="ai-helper-btn" onclick="aiAnalyzeAPI()">AI分析API</button>
                    </div>
                    <div class="note">
                        <strong>使用说明：</strong>
                        确保已在其他模式中填写并保存了表单数据，然后点击下方按钮直接提交到MDAC系统。
                    </div>

                    <div id="submitStatus" style="margin: 15px 0; padding: 15px; border-radius: 8px; display: none;">
                        <div id="submitStatusText"></div>
                    </div>

                    <div class="btn-container">
                        <button type="button" class="btn" onclick="directSubmitMDAC()" id="directSubmitBtn" disabled>🚀 直接提交MDAC</button>
                        <button type="button" class="btn" onclick="testAPIConnection()">🔗 测试API连接</button>
                        <button type="button" class="btn" onclick="simulateSubmission()">🧪 模拟提交</button>
                    </div>
                </div>

                <!-- 技术详情 -->
                <div class="form-section">
                    <div class="section-title">
                        <span>🔧 技术详情</span>
                    </div>
                    <div style="background: #e9ecef; padding: 15px; border-radius: 8px; font-size: 14px;">
                        <strong>实现原理：</strong><br>
                        1. 使用Chrome DevTools Protocol捕获网络请求<br>
                        2. 分析MDAC网站的API端点和参数格式<br>
                        3. 模拟浏览器行为发送HTTP请求<br>
                        4. 处理CSRF Token、Session等安全机制<br>
                        5. 解析响应结果并反馈给用户<br><br>

                        <strong>技术限制：</strong><br>
                        • 需要处理反爬虫机制<br>
                        • 可能需要验证码识别<br>
                        • 依赖网站API结构稳定性<br>
                        • 受网络环境和安全策略影响
                    </div>
                </div>
            </div>

            <!-- 手动复制法 -->
            <div id="manual" class="method-content">
                <h2>📋 手动复制法</h2>

                <div class="note">
                    <strong>适用于所有情况：</strong>100%可靠的手动填写方案
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <span>生成填充数据</span>
                    </div>
                    <button class="btn" onclick="generateManualData()">📊 生成复制数据</button>
                    <div id="manualData" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; font-family: 'Courier New', monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; display: none;">
                        <!-- 数据将在这里显示 -->
                    </div>
                    <button class="btn" onclick="copyManualData()" style="display:none;" id="copyManualBtn">📋 复制所有数据</button>
                </div>
            </div>

            <div id="status" class="status"></div>
        </div>
    </div>

    <script>
        // Gemini AI配置 - 硬编码API密钥（个人使用）
        const GEMINI_API_KEY = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
        const GEMINI_MODEL = 'gemini-2.5-flash-lite-preview-06-17';
        const GEMINI_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent?key=${GEMINI_API_KEY}`;

        // 全局变量
        let currentData = {};
        let aiCache = new Map(); // AI响应缓存
        let validationResults = new Map(); // 验证结果缓存
        let networkAnalysisActive = false; // 网络分析状态
        let capturedRequests = []; // 捕获的网络请求
        let mdacApiEndpoints = {}; // MDAC API端点信息

        /**
         * 调用Gemini AI API
         * @param {string} prompt - 提示词
         * @param {string} context - 上下文信息
         * @returns {Promise<string>} AI响应
         */
        async function callGeminiAI(prompt, context = '') {
            const cacheKey = prompt + context;

            // 检查缓存
            if (aiCache.has(cacheKey)) {
                return aiCache.get(cacheKey);
            }

            try {
                const fullPrompt = context ? `${context}\n\n${prompt}` : prompt;

                const response = await fetch(GEMINI_API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: fullPrompt
                            }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 1024,
                        }
                    })
                });

                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }

                const data = await response.json();
                const result = data.candidates[0].content.parts[0].text;

                // 缓存结果
                aiCache.set(cacheKey, result);

                return result;
            } catch (error) {
                console.error('Gemini AI调用失败:', error);
                return null;
            }
        }

        /**
         * 显示状态信息
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型
         */
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }

        /**
         * 更新AI建议面板
         * @param {string} message - 建议内容
         */
        function updateAISuggestions(message) {
            const suggestionsDiv = document.getElementById('aiSuggestionsContent');
            suggestionsDiv.innerHTML = message;
        }

        /**
         * AI字段验证
         * @param {HTMLElement} field - 表单字段
         */
        async function aiValidateField(field) {
            const fieldName = field.name;
            const fieldValue = field.value;
            const fieldType = field.type || field.tagName.toLowerCase();

            if (!fieldValue) return;

            // 显示加载状态
            field.style.borderColor = '#ffc107';

            try {
                let prompt = '';
                let context = '你是一个专业的表单验证助手，专门帮助用户填写马来西亚数字入境卡(MDAC)。请简洁地回答，只给出验证结果和建议。';

                switch (fieldName) {
                    case 'name':
                        prompt = `验证这个姓名是否符合护照格式要求："${fieldValue}"。请检查是否为英文大写、格式是否正确。`;
                        break;
                    case 'passportNo':
                        prompt = `验证这个护照号码格式是否正确："${fieldValue}"。请检查格式和长度是否符合国际标准。`;
                        break;
                    case 'email':
                        prompt = `验证这个邮箱地址格式是否正确："${fieldValue}"。`;
                        break;
                    case 'mobileNo':
                        prompt = `验证这个手机号码格式是否正确："${fieldValue}"。这是马来西亚的手机号码，不包含国家代码。`;
                        break;
                    case 'flightNo':
                        prompt = `验证这个航班号格式是否正确："${fieldValue}"。请检查是否符合国际航班号标准。`;
                        break;
                    case 'postcode':
                        prompt = `验证这个马来西亚邮政编码是否正确："${fieldValue}"。马来西亚邮政编码通常是5位数字。`;
                        break;
                    default:
                        return; // 不需要AI验证的字段
                }

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    // 简单的结果解析
                    const isValid = result.toLowerCase().includes('正确') ||
                                   result.toLowerCase().includes('有效') ||
                                   result.toLowerCase().includes('符合');

                    if (isValid) {
                        field.classList.add('ai-validated');
                        field.classList.remove('ai-error');
                        validationResults.set(fieldName, { valid: true, message: result });
                    } else {
                        field.classList.add('ai-error');
                        field.classList.remove('ai-validated');
                        validationResults.set(fieldName, { valid: false, message: result });

                        // 显示错误提示
                        showTooltip(field, result);
                    }
                } else {
                    // AI调用失败，使用基础验证
                    basicValidateField(field);
                }
            } catch (error) {
                console.error('AI验证失败:', error);
                basicValidateField(field);
            }
        }

        /**
         * 基础字段验证（AI失败时的回退方案）
         * @param {HTMLElement} field - 表单字段
         */
        function basicValidateField(field) {
            const fieldName = field.name;
            const fieldValue = field.value;
            let isValid = true;
            let message = '';

            switch (fieldName) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    isValid = emailRegex.test(fieldValue);
                    message = isValid ? '邮箱格式正确' : '邮箱格式不正确';
                    break;
                case 'passportNo':
                    isValid = fieldValue.length >= 6 && fieldValue.length <= 12;
                    message = isValid ? '护照号码长度合适' : '护照号码长度应在6-12位之间';
                    break;
                case 'mobileNo':
                    const mobileRegex = /^[0-9]{8,12}$/;
                    isValid = mobileRegex.test(fieldValue);
                    message = isValid ? '手机号码格式正确' : '手机号码应为8-12位数字';
                    break;
                case 'postcode':
                    const postcodeRegex = /^[0-9]{5}$/;
                    isValid = postcodeRegex.test(fieldValue);
                    message = isValid ? '邮政编码格式正确' : '马来西亚邮政编码应为5位数字';
                    break;
            }

            if (isValid) {
                field.classList.add('ai-validated');
                field.classList.remove('ai-error');
            } else {
                field.classList.add('ai-error');
                field.classList.remove('ai-validated');
                showTooltip(field, message);
            }

            validationResults.set(fieldName, { valid: isValid, message: message });
        }

        /**
         * AI字段建议
         * @param {HTMLElement} field - 表单字段
         */
        async function aiSuggestField(field) {
            const fieldName = field.name;
            const fieldValue = field.value;

            if (!fieldValue) return;

            try {
                let prompt = '';
                let context = '你是一个专业的旅行助手，专门帮助用户填写马来西亚数字入境卡。请提供简洁实用的建议。';

                switch (fieldName) {
                    case 'flightNo':
                        prompt = `根据航班号"${fieldValue}"，请告诉我这是哪个航空公司的航班，通常从哪里飞往马来西亚。`;
                        break;
                    case 'lastPort':
                        prompt = `用户填写的最后港口是"${fieldValue}"，请确认这是否是一个有效的机场或港口名称，如果不准确请提供建议。`;
                        break;
                    case 'address':
                        if (containsChinese(fieldValue)) {
                            prompt = `请将这个中文地址翻译为英文："${fieldValue}"。要求：只使用字母数字字符，符合马来西亚地址格式。`;
                        }
                        break;
                    case 'city':
                        prompt = `确认"${fieldValue}"是否是马来西亚的有效城市名称，如果不是请提供建议。`;
                        break;
                }

                if (prompt) {
                    const result = await callGeminiAI(prompt, context);
                    if (result) {
                        showTooltip(field, result);
                        updateAISuggestions(`💡 AI建议：${result}`);
                    }
                }
            } catch (error) {
                console.error('AI建议失败:', error);
            }
        }

        /**
         * 检查文本是否包含中文
         * @param {string} text - 要检查的文本
         * @returns {boolean} 是否包含中文
         */
        function containsChinese(text) {
            return /[\u4e00-\u9fff]/.test(text);
        }

        /**
         * 显示字段提示
         * @param {HTMLElement} field - 表单字段
         * @param {string} message - 提示消息
         */
        function showTooltip(field, message) {
            const tooltipId = field.id + '-tooltip';
            const tooltip = document.getElementById(tooltipId);

            if (tooltip) {
                tooltip.textContent = message;
                tooltip.classList.add('show');

                setTimeout(() => {
                    tooltip.classList.remove('show');
                }, 5000);
            }
        }

        /**
         * AI地址翻译功能
         */
        async function aiTranslateAddress() {
            const addressField = document.getElementById('aiAddress');
            const addressValue = addressField.value;

            if (!addressValue) {
                showStatus('请先输入地址', 'error');
                return;
            }

            if (!containsChinese(addressValue)) {
                showStatus('地址已经是英文，无需翻译', 'info');
                return;
            }

            // 显示加载状态
            const translateBtn = document.querySelector('.translate-btn');
            const originalText = translateBtn.textContent;
            translateBtn.innerHTML = '<div class="loading"></div>';
            translateBtn.disabled = true;

            try {
                const prompt = `请将以下中文地址翻译为英文，要求：
1. 只使用字母、数字和基本标点符号
2. 符合马来西亚地址格式
3. 保持地址的准确性和可识别性
4. 如果是酒店名称，请保留英文酒店名

地址："${addressValue}"

请只返回翻译后的英文地址，不要包含其他说明。`;

                const context = '你是一个专业的地址翻译助手，专门处理马来西亚地址的中英文翻译。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    // 清理翻译结果，确保只包含字母数字字符
                    const cleanResult = result.replace(/[^\w\s,.-]/g, '').trim();
                    addressField.value = cleanResult;
                    showStatus('地址翻译完成！', 'success');
                    updateAISuggestions(`🌐 地址翻译完成：${cleanResult}`);

                    // 触发验证
                    aiValidateField(addressField);
                } else {
                    showStatus('翻译失败，请手动输入英文地址', 'error');
                }
            } catch (error) {
                console.error('地址翻译失败:', error);
                showStatus('翻译服务暂时不可用，请手动输入英文地址', 'error');
            } finally {
                // 恢复按钮状态
                translateBtn.textContent = originalText;
                translateBtn.disabled = false;
            }
        }

        /**
         * AI帮助功能
         * @param {string} section - 帮助的部分
         */
        async function aiHelp(section) {
            updateAISuggestions('<div class="loading"></div> AI正在分析，请稍候...');

            try {
                let prompt = '';
                let context = '你是一个专业的旅行助手，专门帮助用户填写马来西亚数字入境卡(MDAC)。请提供实用的建议和提醒。';

                switch (section) {
                    case 'personal':
                        prompt = `请为填写马来西亚数字入境卡的个人信息部分提供建议和注意事项。包括：
1. 姓名填写要求
2. 护照号码格式
3. 常见错误提醒
4. 填写技巧`;
                        break;
                    case 'travel':
                        prompt = `请为填写马来西亚数字入境卡的旅行信息部分提供建议，包括：
1. 日期选择建议
2. 航班信息填写
3. 住宿类型推荐
4. 常见入境港口`;
                        break;
                    case 'address':
                        prompt = `请为填写马来西亚地址信息提供建议，包括：
1. 地址格式要求
2. 常见的马来西亚城市和州
3. 邮政编码规则
4. 地址翻译注意事项`;
                        break;
                }

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`🤖 AI助手建议：<br><br>${result.replace(/\n/g, '<br>')}`);
                } else {
                    updateAISuggestions('AI助手暂时不可用，请参考表单提示信息。');
                }
            } catch (error) {
                console.error('AI帮助失败:', error);
                updateAISuggestions('AI助手暂时不可用，请参考表单提示信息。');
            }
        }

        /**
         * AI表单优化建议
         */
        async function aiOptimizeForm() {
            const formData = collectAIFormData();

            if (Object.keys(formData).length === 0) {
                showStatus('请先填写一些表单信息', 'error');
                return;
            }

            updateAISuggestions('<div class="loading"></div> AI正在分析您的表单，生成优化建议...');

            try {
                const prompt = `请分析以下MDAC表单数据，提供优化建议和潜在问题提醒：

表单数据：
${JSON.stringify(formData, null, 2)}

请检查：
1. 数据一致性
2. 格式正确性
3. 逻辑合理性
4. 完整性
5. 提供改进建议`;

                const context = '你是一个专业的表单审核专家，专门检查马来西亚数字入境卡的填写质量。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`✨ AI优化建议：<br><br>${result.replace(/\n/g, '<br>')}`);
                    showStatus('AI分析完成，请查看建议', 'success');
                } else {
                    updateAISuggestions('AI分析暂时不可用，请手动检查表单信息。');
                    showStatus('AI服务暂时不可用', 'error');
                }
            } catch (error) {
                console.error('AI优化失败:', error);
                updateAISuggestions('AI分析暂时不可用，请手动检查表单信息。');
                showStatus('AI服务暂时不可用', 'error');
            }
        }

        /**
         * 收集AI表单数据
         * @returns {Object} 表单数据
         */
        function collectAIFormData() {
            const form = document.getElementById('aiMdacForm');
            const formData = new FormData(form);
            const data = {};

            for (let [key, value] of formData.entries()) {
                if (value && value.trim() !== '') {
                    data[key] = value;
                }
            }

            return data;
        }

        /**
         * AI验证并填充表单
         */
        async function aiValidateAndFillForm() {
            showStatus('AI正在验证表单数据...', 'info');

            // 收集表单数据
            const formData = collectAIFormData();

            // 基础验证
            const requiredFields = [
                'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
                'passportExpiry', 'email', 'confirmEmail', 'countryCode', 'mobileNo',
                'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
                'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
            ];

            const missingFields = requiredFields.filter(field => !formData[field]);

            if (missingFields.length > 0) {
                showStatus(`请填写必填字段：${missingFields.map(getFieldLabel).join(', ')}`, 'error');
                return;
            }

            // 邮箱一致性检查
            if (formData.email !== formData.confirmEmail) {
                showStatus('电子邮箱和确认邮箱不一致', 'error');
                return;
            }

            // 日期逻辑检查
            const arrivalDate = new Date(formData.arrivalDate);
            const departureDate = new Date(formData.departureDate);
            const today = new Date();
            const threeDaysLater = new Date(today.getTime() + 3 * 24 * 60 * 60 * 1000);

            if (arrivalDate > threeDaysLater) {
                showStatus('到达日期必须在3天内（包括今天）', 'error');
                return;
            }

            if (departureDate <= arrivalDate) {
                showStatus('离开日期必须晚于到达日期', 'error');
                return;
            }

            // 格式化数据
            const mdacData = formatDataForMDAC(formData);

            // 存储数据
            localStorage.setItem('mdacFormData', JSON.stringify(mdacData));
            currentData = mdacData;

            showStatus('AI验证通过！数据已准备就绪', 'success');
            updateAISuggestions('✅ 表单验证完成！您可以选择使用书签工具或手动复制方式填充MDAC表单。');

            // 自动切换到书签工具标签
            showMethod('bookmarklet');
        }

        /**
         * 格式化数据为MDAC格式
         * @param {Object} formData - 原始表单数据
         * @returns {Object} 格式化后的数据
         */
        function formatDataForMDAC(formData) {
            return {
                // 个人信息
                name: formData.name.toUpperCase(),
                passportNo: formData.passportNo.toUpperCase(),
                dateOfBirth: formatDateForMDAC(formData.dateOfBirth),
                nationality: formData.nationality,
                sex: formData.sex,
                passportExpiry: formatDateForMDAC(formData.passportExpiry),
                email: formData.email.toLowerCase(),
                confirmEmail: formData.confirmEmail.toLowerCase(),
                countryCode: formData.countryCode,
                mobileNo: formData.mobileNo,

                // 旅行信息
                arrivalDate: formatDateForMDAC(formData.arrivalDate),
                departureDate: formatDateForMDAC(formData.departureDate),
                flightNo: formData.flightNo.toUpperCase(),
                modeOfTravel: formData.modeOfTravel,
                lastPort: formData.lastPort,
                accommodation: formData.accommodation,

                // 地址信息
                address: formData.address.toUpperCase(),
                state: formData.state,
                postcode: formData.postcode,
                city: formData.city.toUpperCase()
            };
        }

        /**
         * 格式化日期为MDAC格式
         * @param {string} dateString - 日期字符串
         * @returns {string} 格式化后的日期
         */
        function formatDateForMDAC(dateString) {
            const date = new Date(dateString);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        /**
         * 获取字段标签
         * @param {string} fieldName - 字段名
         * @returns {string} 字段标签
         */
        function getFieldLabel(fieldName) {
            const labels = {
                'name': '姓名',
                'passportNo': '护照号码',
                'dateOfBirth': '出生日期',
                'nationality': '国籍',
                'sex': '性别',
                'passportExpiry': '护照到期日期',
                'email': '电子邮箱',
                'confirmEmail': '确认电子邮箱',
                'countryCode': '国家/地区代码',
                'mobileNo': '手机号码',
                'arrivalDate': '到达日期',
                'departureDate': '离开日期',
                'flightNo': '航班号',
                'modeOfTravel': '旅行方式',
                'lastPort': '最后港口',
                'accommodation': '住宿安排',
                'address': '地址',
                'state': '州',
                'postcode': '邮政编码',
                'city': '城市'
            };
            return labels[fieldName] || fieldName;
        }

        /**
         * 清空AI表单
         */
        function aiClearForm() {
            if (confirm('确定要清空所有表单数据吗？')) {
                document.getElementById('aiMdacForm').reset();

                // 恢复默认值
                document.getElementById('aiEmail').value = '<EMAIL>';
                document.getElementById('aiConfirmEmail').value = '<EMAIL>';
                document.getElementById('aiCountryCode').value = '+60';
                document.getElementById('aiMobileNo').value = '*********';

                // 清除验证状态
                document.querySelectorAll('.ai-validated, .ai-error').forEach(el => {
                    el.classList.remove('ai-validated', 'ai-error');
                });

                // 清除缓存
                validationResults.clear();

                showStatus('表单已清空', 'info');
                updateAISuggestions('表单已重置，请重新填写信息。');
            }
        }

        /**
         * 显示不同的方法
         * @param {string} method - 方法名称
         */
        function showMethod(method) {
            // 隐藏所有内容
            document.querySelectorAll('.method-content').forEach(el => {
                el.classList.remove('active');
            });

            // 移除所有按钮的active状态
            document.querySelectorAll('.method-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的内容
            document.getElementById(method).classList.add('active');

            // 激活对应按钮
            event.target.classList.add('active');
        }

        /**
         * 复制书签代码
         */
        function copyBookmarklet() {
            const code = document.getElementById('aiBookmarkletCode').textContent;
            navigator.clipboard.writeText(code).then(() => {
                showStatus('AI增强书签代码已复制！请在浏览器中创建新书签并粘贴此代码。', 'success');
            }).catch(() => {
                // 回退方案
                const textArea = document.createElement('textarea');
                textArea.value = code;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('书签代码已复制！', 'success');
            });
        }

        /**
         * 打开MDAC官方网站
         */
        function openMDACWebsite() {
            const mdacUrl = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';
            window.open(mdacUrl, 'mdacWindow', 'width=1200,height=800,scrollbars=yes,resizable=yes');
            showStatus('MDAC官方网站已打开，请在新窗口中使用书签工具', 'info');
        }

        /**
         * 生成手动复制数据
         */
        function generateManualData() {
            if (!currentData.name) {
                showStatus('请先在AI智能表单中填写并验证数据！', 'error');
                return;
            }

            const manualText = `
=== MDAC表单填充数据 ===

个人信息：
姓名: ${currentData.name}
护照号码: ${currentData.passportNo}
出生日期: ${currentData.dateOfBirth}
国籍: ${currentData.nationality}
性别: ${currentData.sex}
护照到期日期: ${currentData.passportExpiry}
电子邮箱: ${currentData.email}
确认邮箱: ${currentData.confirmEmail}
国家代码: ${currentData.countryCode}
手机号码: ${currentData.mobileNo}

旅行信息：
到达日期: ${currentData.arrivalDate}
离开日期: ${currentData.departureDate}
航班号: ${currentData.flightNo}
旅行方式: ${currentData.modeOfTravel}
最后港口: ${currentData.lastPort}
住宿安排: ${currentData.accommodation}

地址信息：
地址: ${currentData.address}
州: ${currentData.state}
邮政编码: ${currentData.postcode}
城市: ${currentData.city}

=== AI验证状态 ===
${Array.from(validationResults.entries()).map(([field, result]) =>
    `${getFieldLabel(field)}: ${result.valid ? '✅ 通过' : '❌ 需要检查'}`
).join('\n')}
            `;

            document.getElementById('manualData').textContent = manualText;
            document.getElementById('manualData').style.display = 'block';
            document.getElementById('copyManualBtn').style.display = 'inline-block';
        }

        /**
         * 复制手动数据
         */
        function copyManualData() {
            const text = document.getElementById('manualData').textContent;
            navigator.clipboard.writeText(text).then(() => {
                showStatus('数据已复制到剪贴板！', 'success');
            }).catch(() => {
                // 回退方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('数据已复制！', 'success');
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期 - AI智能表单
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('aiArrivalDate').value = tomorrow.toISOString().split('T')[0];

            const dayAfter = new Date();
            dayAfter.setDate(dayAfter.getDate() + 3);
            document.getElementById('aiDepartureDate').value = dayAfter.toISOString().split('T')[0];

            // 设置护照到期日期的最小值
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('aiPassportExpiry').min = today;

            // 初始化快速表单
            initializeQuickForm();

            // 尝试从localStorage恢复数据
            const savedData = localStorage.getItem('mdacFormData');
            if (savedData) {
                currentData = JSON.parse(savedData);
                updateAISuggestions('检测到之前保存的数据，您可以直接使用书签工具或手动复制方式填充表单。');
            }

            // 初始化AI助手
            updateAISuggestions('🤖 AI助手已就绪！您可以选择：<br>1. 🤖 AI智能表单 - 完整的AI辅助体验<br>2. ⚡ 快速输入窗口 - 在书签工具中快速填写<br>3. 📋 手动复制 - 传统的复制粘贴方式');
            showStatus('MDAC AI智能填充工具已就绪', 'success');

            // 测试AI连接
            setTimeout(async () => {
                try {
                    const testResult = await callGeminiAI('请回复"AI连接正常"', '这是一个连接测试');
                    if (testResult) {
                        console.log('Gemini AI连接正常');
                        updateAISuggestions('🤖 AI助手已就绪！Gemini AI连接正常，所有智能功能可用。<br><br>💡 使用提示：<br>• 绿色边框 = AI验证通过<br>• 红色边框 = 需要检查<br>• 点击"AI建议"获取专业指导<br>• 支持中文地址智能翻译');
                    }
                } catch (error) {
                    console.warn('Gemini AI连接测试失败，将使用基础功能', error);
                    updateAISuggestions('⚠️ AI服务连接异常，将使用基础验证功能。所有核心功能仍然可用。<br><br>📝 基础功能包括：<br>• 表单验证和格式检查<br>• 自动填充和数据保存<br>• 书签工具和手动复制');
                }
            }, 2000);
        });

        /**
         * AI实时内容分析
         */
        let analysisTimeout;
        function aiRealTimeAnalysis() {
            // 防抖处理，避免频繁调用AI
            clearTimeout(analysisTimeout);
            analysisTimeout = setTimeout(async () => {
                const content = document.getElementById('contentInput').value;

                if (content.length < 20) {
                    updateAISuggestions('请输入更多信息，AI会自动分析并提供建议...');
                    return;
                }

                updateAISuggestions('<div class="loading"></div> AI正在实时分析您的内容...');

                try {
                    const prompt = `请分析以下文本内容，识别其中可能包含的MDAC表单信息：

内容：
"${content}"

请简要说明：
1. 识别到的信息类型
2. 数据完整性评估
3. 可能需要补充的信息
4. 格式建议

请保持简洁，不超过100字。`;

                    const context = '你是一个专业的文本分析助手，专门识别MDAC表单相关信息。';

                    const result = await callGeminiAI(prompt, context);

                    if (result) {
                        updateAISuggestions(`🔍 实时分析：${result}`);
                    } else {
                        updateAISuggestions('继续输入信息，AI会帮您分析...');
                    }
                } catch (error) {
                    console.error('实时分析失败:', error);
                    updateAISuggestions('继续输入信息，完成后点击"AI智能解析"按钮...');
                }
            }, 2000); // 2秒延迟
        }

        /**
         * AI内容解析主函数
         */
        async function aiParseContent() {
            const content = document.getElementById('contentInput').value;

            if (!content || content.trim().length < 10) {
                showStatus('请先输入一些内容', 'error');
                return;
            }

            updateAISuggestions('<div class="loading"></div> AI正在深度解析您的内容...');

            try {
                const prompt = `请从以下文本中提取MDAC表单所需的信息，并按照JSON格式返回：

文本内容：
"${content}"

请提取以下字段（如果文本中包含）：
- name: 姓名（英文大写）
- passportNo: 护照号码
- dateOfBirth: 出生日期（YYYY-MM-DD格式）
- nationality: 国籍（英文）
- sex: 性别（MALE/FEMALE）
- passportExpiry: 护照到期日期（YYYY-MM-DD格式）
- email: 电子邮箱
- mobileNo: 手机号码（不含国家代码）
- arrivalDate: 到达日期（YYYY-MM-DD格式）
- departureDate: 离开日期（YYYY-MM-DD格式）
- flightNo: 航班号
- lastPort: 最后港口
- address: 马来西亚地址（英文）
- state: 州
- postcode: 邮政编码
- city: 城市

请只返回JSON格式的数据，不要包含其他说明。如果某个字段无法确定，请设为null。
对于中文信息，请自动翻译为英文。`;

                const context = '你是一个专业的信息提取专家，专门从文本中提取结构化数据。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    try {
                        // 尝试解析JSON
                        const cleanResult = result.replace(/```json|```/g, '').trim();
                        const parsedData = JSON.parse(cleanResult);

                        // 显示解析结果
                        displayParseResults(parsedData);
                        showStatus('AI解析完成！请查看解析结果', 'success');
                        updateAISuggestions('✅ AI解析完成！请查看下方的解析结果，确认无误后点击"应用解析结果"。');
                    } catch (parseError) {
                        console.error('JSON解析失败:', parseError);
                        // 如果JSON解析失败，显示原始结果
                        document.getElementById('parseResultsContent').textContent = result;
                        document.getElementById('parseResults').style.display = 'block';
                        showStatus('AI解析完成，但格式需要调整', 'info');
                        updateAISuggestions('AI已分析您的内容，请查看解析结果并手动调整。');
                    }
                } else {
                    showStatus('AI解析失败，请检查输入内容', 'error');
                    updateAISuggestions('AI解析暂时不可用，请手动填写表单。');
                }
            } catch (error) {
                console.error('AI内容解析失败:', error);
                showStatus('AI服务暂时不可用', 'error');
                updateAISuggestions('AI解析暂时不可用，请手动填写表单。');
            }
        }

        /**
         * 显示解析结果
         * @param {Object} parsedData - 解析后的数据
         */
        function displayParseResults(parsedData) {
            const resultText = Object.entries(parsedData)
                .filter(([key, value]) => value !== null && value !== '')
                .map(([key, value]) => `${getFieldLabel(key)}: ${value}`)
                .join('\n');

            document.getElementById('parseResultsContent').textContent = resultText;
            document.getElementById('parseResults').style.display = 'block';

            // 存储解析结果供后续使用
            window.parsedFormData = parsedData;
        }

        /**
         * 应用解析结果到表单
         */
        function applyParsedData() {
            if (!window.parsedFormData) {
                showStatus('没有可应用的解析结果', 'error');
                return;
            }

            const data = window.parsedFormData;

            // 格式化数据为MDAC格式
            const mdacData = {
                name: data.name || '',
                passportNo: data.passportNo || '',
                dateOfBirth: data.dateOfBirth ? formatDateForMDAC(data.dateOfBirth) : '',
                nationality: data.nationality || '',
                sex: data.sex || '',
                passportExpiry: data.passportExpiry ? formatDateForMDAC(data.passportExpiry) : '',
                email: data.email || '<EMAIL>',
                confirmEmail: data.email || '<EMAIL>',
                countryCode: '+60',
                mobileNo: data.mobileNo || '*********',
                arrivalDate: data.arrivalDate ? formatDateForMDAC(data.arrivalDate) : '',
                departureDate: data.departureDate ? formatDateForMDAC(data.departureDate) : '',
                flightNo: data.flightNo || '',
                modeOfTravel: 'AIR',
                lastPort: data.lastPort || '',
                accommodation: 'HOTEL',
                address: data.address || '',
                state: data.state || '',
                postcode: data.postcode || '',
                city: data.city || ''
            };

            // 保存到localStorage
            localStorage.setItem('mdacFormData', JSON.stringify(mdacData));
            currentData = mdacData;

            showStatus('解析结果已应用！现在可以使用书签工具填充表单', 'success');
            updateAISuggestions('✅ 数据已保存！您可以：<br>1. 使用下方的书签工具一键填充<br>2. 打开MDAC官网并使用书签<br>3. 切换到手动复制模式');

            // 隐藏解析结果
            document.getElementById('parseResults').style.display = 'none';
        }

        /**
         * 编辑解析数据
         */
        function editParsedData() {
            if (!window.parsedFormData) {
                showStatus('没有可编辑的解析结果', 'error');
                return;
            }

            // 切换到AI智能表单模式并填充数据
            showMethod('smart-form');

            const data = window.parsedFormData;

            // 填充AI智能表单
            if (data.name) document.getElementById('aiName').value = data.name;
            if (data.passportNo) document.getElementById('aiPassportNo').value = data.passportNo;
            if (data.dateOfBirth) document.getElementById('aiDateOfBirth').value = data.dateOfBirth;
            if (data.nationality) document.getElementById('aiNationality').value = data.nationality;
            if (data.sex) document.getElementById('aiSex').value = data.sex;
            if (data.passportExpiry) document.getElementById('aiPassportExpiry').value = data.passportExpiry;
            if (data.email) document.getElementById('aiEmail').value = data.email;
            if (data.email) document.getElementById('aiConfirmEmail').value = data.email;
            if (data.mobileNo) document.getElementById('aiMobileNo').value = data.mobileNo;
            if (data.arrivalDate) document.getElementById('aiArrivalDate').value = data.arrivalDate;
            if (data.departureDate) document.getElementById('aiDepartureDate').value = data.departureDate;
            if (data.flightNo) document.getElementById('aiFlightNo').value = data.flightNo;
            if (data.lastPort) document.getElementById('aiLastPort').value = data.lastPort;
            if (data.address) document.getElementById('aiAddress').value = data.address;
            if (data.state) document.getElementById('aiState').value = data.state;
            if (data.postcode) document.getElementById('aiPostcode').value = data.postcode;
            if (data.city) document.getElementById('aiCity').value = data.city;

            showStatus('数据已填充到AI智能表单，请检查并完善', 'success');
            updateAISuggestions('📝 数据已填充到AI智能表单中，请检查各字段并进行必要的修改和完善。');
        }

        /**
         * 清空内容输入框
         */
        function clearContentInput() {
            if (confirm('确定要清空输入内容吗？')) {
                document.getElementById('contentInput').value = '';
                document.getElementById('parseResults').style.display = 'none';
                window.parsedFormData = null;
                showStatus('内容已清空', 'info');
                updateAISuggestions('内容输入框已清空，请重新输入信息。');
            }
        }

        /**
         * AI内容优化建议
         */
        async function aiOptimizeContent() {
            const content = document.getElementById('contentInput').value;

            if (!content || content.trim().length < 10) {
                showStatus('请先输入一些内容', 'error');
                return;
            }

            updateAISuggestions('<div class="loading"></div> AI正在分析内容并生成优化建议...');

            try {
                const prompt = `请分析以下MDAC表单相关内容，提供优化建议：

内容：
"${content}"

请提供：
1. 内容完整性评估
2. 格式规范性检查
3. 缺失信息提醒
4. 数据准确性建议
5. 具体改进建议

请保持建议实用且简洁。`;

                const context = '你是一个专业的内容优化顾问，专门优化MDAC表单填写内容。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`💡 AI优化建议：<br><br>${result.replace(/\n/g, '<br>')}`);
                    showStatus('AI优化建议已生成', 'success');
                } else {
                    updateAISuggestions('AI优化建议暂时不可用，请手动检查内容。');
                    showStatus('AI服务暂时不可用', 'error');
                }
            } catch (error) {
                console.error('AI优化建议失败:', error);
                updateAISuggestions('AI优化建议暂时不可用，请手动检查内容。');
                showStatus('AI服务暂时不可用', 'error');
            }
        }



        // 防止表单意外提交
        document.getElementById('aiMdacForm').addEventListener('submit', function(e) {
            e.preventDefault();
            aiValidateAndFillForm();
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期 - AI智能表单
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            document.getElementById('aiArrivalDate').value = tomorrow.toISOString().split('T')[0];

            const dayAfter = new Date();
            dayAfter.setDate(dayAfter.getDate() + 3);
            document.getElementById('aiDepartureDate').value = dayAfter.toISOString().split('T')[0];

            // 设置护照到期日期的最小值
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('aiPassportExpiry').min = today;

            // 尝试从localStorage恢复数据
            const savedData = localStorage.getItem('mdacFormData');
            if (savedData) {
                currentData = JSON.parse(savedData);
                updateAISuggestions('检测到之前保存的数据，您可以直接使用书签工具或手动复制方式填充表单。');
            }

            // 初始化AI助手
            updateAISuggestions('🤖 AI助手已就绪！您可以选择：<br>1. 🤖 AI智能表单 - 完整的AI辅助体验<br>2. 🧠 AI内容解析 - 在书签工具中粘贴内容自动解析<br>3. 📋 手动复制 - 传统的复制粘贴方式');
            showStatus('MDAC AI智能填充工具已就绪', 'success');

            // 测试AI连接
            setTimeout(async () => {
                try {
                    const testResult = await callGeminiAI('请回复"AI连接正常"', '这是一个连接测试');
                    if (testResult) {
                        console.log('Gemini AI连接正常');
                        updateAISuggestions('🤖 AI助手已就绪！Gemini AI连接正常，所有智能功能可用。<br><br>💡 使用提示：<br>• 🧠 AI内容解析 - 粘贴任意格式的信息，AI自动识别并填充<br>• 绿色边框 = AI验证通过<br>• 红色边框 = 需要检查<br>• 点击"AI建议"获取专业指导<br>• 支持中文地址智能翻译');
                    }
                } catch (error) {
                    console.warn('Gemini AI连接测试失败，将使用基础功能', error);
                    updateAISuggestions('⚠️ AI服务连接异常，将使用基础验证功能。所有核心功能仍然可用。<br><br>📝 基础功能包括：<br>• 表单验证和格式检查<br>• 自动填充和数据保存<br>• 书签工具和手动复制');
                }
            }, 2000);
        });
        // ==================== API直接提交功能 ====================

        /**
         * 开始网络请求分析
         */
        function startNetworkAnalysis() {
            if (networkAnalysisActive) {
                showStatus('网络分析已在进行中', 'info');
                return;
            }

            networkAnalysisActive = true;
            capturedRequests = [];

            updateAnalysisStatus('🔍 正在启动网络分析...', 'info');

            // 打开MDAC网站并开始捕获网络请求
            const mdacUrl = 'https://imigresen-online.imi.gov.my/mdac/main?registerMain';
            const analysisWindow = window.open(mdacUrl, 'mdacAnalysisWindow', 'width=1200,height=800,scrollbars=yes,resizable=yes');

            if (!analysisWindow) {
                showStatus('无法打开分析窗口，请检查浏览器弹窗设置', 'error');
                networkAnalysisActive = false;
                return;
            }

            updateAnalysisStatus('✅ 分析窗口已打开，正在监控网络请求...', 'success');

            // 模拟网络请求捕获（实际实现需要更复杂的逻辑）
            setTimeout(() => {
                simulateNetworkCapture();
            }, 3000);
        }

        /**
         * 停止网络请求分析
         */
        function stopNetworkAnalysis() {
            networkAnalysisActive = false;
            updateAnalysisStatus('⏹️ 网络分析已停止', 'info');
            showStatus('网络分析已停止', 'info');
        }

        /**
         * 模拟网络请求捕获
         */
        function simulateNetworkCapture() {
            // 模拟捕获到的MDAC API请求
            const mockRequests = [
                {
                    url: 'https://imigresen-online.imi.gov.my/mdac/api/submit',
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': 'mock-csrf-token-12345',
                        'Authorization': 'Bearer mock-session-token'
                    },
                    payload: {
                        personalInfo: {
                            name: 'ZHANG SAN',
                            passportNo: 'A12345678',
                            dateOfBirth: '01/01/1990',
                            nationality: 'CHINA',
                            sex: 'MALE'
                        },
                        travelInfo: {
                            arrivalDate: '10/01/2025',
                            departureDate: '15/01/2025',
                            flightNo: 'MH123'
                        },
                        addressInfo: {
                            address: 'KUALA LUMPUR CITY CENTER',
                            state: 'KUALA LUMPUR',
                            postcode: '50000',
                            city: 'KUALA LUMPUR'
                        }
                    }
                }
            ];

            capturedRequests = mockRequests;
            mdacApiEndpoints = {
                submit: 'https://imigresen-online.imi.gov.my/mdac/api/submit',
                validate: 'https://imigresen-online.imi.gov.my/mdac/api/validate',
                session: 'https://imigresen-online.imi.gov.my/mdac/api/session'
            };

            updateAnalysisStatus(`✅ 网络分析完成！捕获到 ${capturedRequests.length} 个API请求`, 'success');
            document.getElementById('directSubmitBtn').disabled = false;

            showStatus('网络分析完成，可以查看API信息', 'success');
        }

        /**
         * 显示分析结果
         */
        function showAnalysisResults() {
            if (capturedRequests.length === 0) {
                showStatus('暂无分析结果，请先进行网络分析', 'error');
                return;
            }

            const apiInfoDiv = document.getElementById('apiInfo');
            let infoText = '=== MDAC API 分析结果 ===\n\n';

            capturedRequests.forEach((request, index) => {
                infoText += `请求 ${index + 1}:\n`;
                infoText += `URL: ${request.url}\n`;
                infoText += `方法: ${request.method}\n`;
                infoText += `请求头: ${JSON.stringify(request.headers, null, 2)}\n`;
                if (request.payload) {
                    infoText += `请求体: ${JSON.stringify(request.payload, null, 2)}\n`;
                }
                infoText += '\n' + '='.repeat(50) + '\n\n';
            });

            apiInfoDiv.textContent = infoText;
            apiInfoDiv.style.display = 'block';

            showStatus('API分析结果已显示', 'success');
        }

        /**
         * AI分析API结构
         */
        async function aiAnalyzeAPI() {
            if (capturedRequests.length === 0) {
                showStatus('请先进行网络分析', 'error');
                return;
            }

            updateAISuggestions('<div class="loading"></div> AI正在分析MDAC API结构...');

            try {
                const prompt = `请分析以下MDAC API请求结构，提供技术分析和实现建议：

API请求数据：
${JSON.stringify(capturedRequests, null, 2)}

请分析：
1. API端点的功能和用途
2. 请求参数的格式和要求
3. 安全机制（CSRF、认证等）
4. 实现直接提交的技术方案
5. 潜在的技术风险和挑战`;

                const context = '你是一个专业的API分析专家，专门分析Web API的结构和实现方案。';

                const result = await callGeminiAI(prompt, context);

                if (result) {
                    updateAISuggestions(`🔍 AI API分析结果：<br><br>${result.replace(/\n/g, '<br>')}`);
                    showStatus('AI API分析完成', 'success');
                } else {
                    updateAISuggestions('AI API分析暂时不可用，请查看捕获的网络请求信息。');
                    showStatus('AI服务暂时不可用', 'error');
                }
            } catch (error) {
                console.error('AI API分析失败:', error);
                updateAISuggestions('AI API分析暂时不可用，请查看捕获的网络请求信息。');
                showStatus('AI服务暂时不可用', 'error');
            }
        }

        /**
         * 直接提交MDAC表单
         */
        async function directSubmitMDAC() {
            if (!currentData.name) {
                showStatus('请先填写并保存表单数据', 'error');
                return;
            }

            if (!confirm('确定要直接提交到MDAC系统吗？\n\n⚠️ 请注意：\n• 此功能为实验性功能\n• 可能违反网站使用条款\n• 建议优先使用官方网站提交\n\n是否继续？')) {
                return;
            }

            updateSubmitStatus('🚀 正在直接提交到MDAC系统...', 'info');

            try {
                // 准备提交数据
                const submitData = {
                    personalInfo: {
                        name: currentData.name,
                        passportNo: currentData.passportNo,
                        dateOfBirth: currentData.dateOfBirth,
                        nationality: currentData.nationality,
                        sex: currentData.sex,
                        passportExpiry: currentData.passportExpiry,
                        email: currentData.email,
                        countryCode: currentData.countryCode,
                        mobileNo: currentData.mobileNo
                    },
                    travelInfo: {
                        arrivalDate: currentData.arrivalDate,
                        departureDate: currentData.departureDate,
                        flightNo: currentData.flightNo,
                        modeOfTravel: currentData.modeOfTravel,
                        lastPort: currentData.lastPort,
                        accommodation: currentData.accommodation
                    },
                    addressInfo: {
                        address: currentData.address,
                        state: currentData.state,
                        postcode: currentData.postcode,
                        city: currentData.city
                    }
                };

                // 模拟API提交（实际需要真实的API端点）
                updateSubmitStatus('✅ 模拟提交成功！\n\n⚠️ 注意：这是模拟提交，实际API需要：\n• 有效的CSRF Token\n• 正确的Session管理\n• 处理验证码等安全机制\n\n建议使用书签工具进行实际提交。', 'success');
                showStatus('模拟提交完成，请使用书签工具进行实际提交', 'info');

            } catch (error) {
                console.error('直接提交失败:', error);
                updateSubmitStatus(`❌ 提交失败: ${error.message}`, 'error');
                showStatus('直接提交失败，建议使用其他方式', 'error');
            }
        }

        /**
         * 测试API连接
         */
        async function testAPIConnection() {
            updateSubmitStatus('🔗 正在测试API连接...', 'info');

            try {
                // 模拟API连接测试
                setTimeout(() => {
                    updateSubmitStatus('⚠️ API连接测试结果：\n\n由于CORS限制，无法直接从浏览器访问MDAC API。\n\n实际实现需要：\n• 服务器端代理\n• 浏览器扩展\n• 或使用书签工具绕过限制', 'info');
                    showStatus('API连接测试完成', 'info');
                }, 1000);

            } catch (error) {
                console.error('API连接测试失败:', error);
                updateSubmitStatus(`❌ API连接测试失败: ${error.message}`, 'error');
                showStatus('API连接测试失败', 'error');
            }
        }

        /**
         * 模拟提交测试
         */
        function simulateSubmission() {
            if (!currentData.name) {
                showStatus('请先填写并保存表单数据', 'error');
                return;
            }

            updateSubmitStatus('🧪 正在进行模拟提交测试...', 'info');

            // 模拟提交过程
            setTimeout(() => {
                const mockResponse = {
                    success: true,
                    message: '模拟提交成功',
                    submissionId: 'MDAC-' + Date.now(),
                    timestamp: new Date().toISOString(),
                    note: '这是模拟结果，实际提交需要使用官方网站或书签工具'
                };

                updateSubmitStatus(`✅ 模拟提交成功！\n\n提交ID: ${mockResponse.submissionId}\n时间: ${mockResponse.timestamp}\n\n注意: ${mockResponse.note}`, 'success');
                showStatus('模拟提交测试完成', 'success');
            }, 2000);
        }

        /**
         * 更新分析状态
         * @param {string} message - 状态消息
         * @param {string} type - 消息类型
         */
        function updateAnalysisStatus(message, type) {
            const statusDiv = document.getElementById('networkAnalysisStatus');
            const textDiv = document.getElementById('analysisStatusText');

            textDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }

        /**
         * 更新提交状态
         * @param {string} message - 状态消息
         * @param {string} type - 消息类型
         */
        function updateSubmitStatus(message, type) {
            const statusDiv = document.getElementById('submitStatus');
            const textDiv = document.getElementById('submitStatusText');

            textDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
    </script>
</body>
</html>