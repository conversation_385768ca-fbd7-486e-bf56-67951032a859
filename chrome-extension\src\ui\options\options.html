<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能填充工具 - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                <img src="../../../assets/icons/icon48.png" alt="MDAC AI">
                <h1>MDAC AI智能填充工具</h1>
            </div>
            <div class="version">版本 1.0.0</div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="ai">AI配置</button>
            <button class="nav-tab" data-tab="about">关于</button>
        </div>

        <!-- 设置内容 -->
        <div class="content">

            <!-- AI配置 -->
            <div class="tab-content active" id="ai">
                <div class="section">
                    <h2>Gemini AI配置</h2>
                    
                    <div class="setting-item">
                        <label for="geminiApiKey">Gemini API密钥</label>
                        <div class="input-group">
                            <input type="password" id="geminiApiKey" placeholder="输入您的Gemini API密钥">
                            <button type="button" id="toggleApiKey" class="toggle-btn">👁️</button>
                        </div>
                        <p class="description">
                            获取API密钥：<a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                        </p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="aiEnabled">
                            <span class="checkmark"></span>
                            启用AI功能
                        </label>
                        <p class="description">开启AI智能验证和建议功能</p>
                    </div>

                    <div class="setting-item">
                        <label for="aiModel">AI模型</label>
                        <select id="aiModel">
                            <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite (推荐)</option>
                            <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label for="aiTemperature">AI创造性</label>
                        <input type="range" id="aiTemperature" min="0" max="1" step="0.1" value="0.7">
                        <div class="range-labels">
                            <span>保守</span>
                            <span>创新</span>
                        </div>
                        <p class="description">调整AI回答的创造性程度</p>
                    </div>

                    <div class="setting-item">
                        <button type="button" id="testAI" class="btn primary">测试AI连接</button>
                        <div id="aiTestResult" class="test-result"></div>
                    </div>
                </div>
            </div>



            <!-- 关于 -->
            <div class="tab-content" id="about">
                <div class="section">
                    <h2>关于MDAC AI智能分析工具</h2>

                    <div class="about-info">
                        <div class="about-item">
                            <strong>版本：</strong> 1.0.0 (AI专版)
                        </div>
                        <div class="about-item">
                            <strong>开发者：</strong> MDAC AI Team
                        </div>
                        <div class="about-item">
                            <strong>更新时间：</strong> 2025年1月
                        </div>
                        <div class="about-item">
                            <strong>AI引擎：</strong> Google Gemini 2.5 Flash Lite
                        </div>
                    </div>

                    <div class="feature-list">
                        <h3>AI智能功能</h3>
                        <ul>
                            <li>🧠 智能内容解析 - 从任意文本提取表单数据</li>
                            <li>✅ AI数据验证 - 智能验证和优化建议</li>
                            <li>🌐 智能地址翻译 - 中英文地址自动翻译</li>
                            <li>🤖 实时AI助手 - 智能建议和错误诊断</li>
                            <li>🔍 格式智能检查 - 自动检测和修正数据格式</li>
                            <li>💡 上下文理解 - 基于语境的智能分析</li>
                        </ul>
                    </div>

                    <div class="links">
                        <h3>相关链接</h3>
                        <div class="link-buttons">
                            <a href="#" class="btn secondary" id="helpLink">AI功能帮助</a>
                            <a href="#" class="btn secondary" id="feedbackLink">问题反馈</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作 -->
        <div class="footer">
            <div class="status" id="saveStatus"></div>
            <div class="actions">
                <button type="button" id="resetSettings" class="btn secondary">重置设置</button>
                <button type="button" id="saveSettings" class="btn primary">保存设置</button>
            </div>
        </div>
    </div>



    <script src="options.js"></script>
</body>
</html>
