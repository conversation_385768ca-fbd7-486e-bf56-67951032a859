<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI智能填充工具 - 设置</title>
    <link rel="stylesheet" href="options.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <div class="logo">
                <img src="icons/icon48.png" alt="MDAC AI">
                <h1>MDAC AI智能填充工具</h1>
            </div>
            <div class="version">版本 1.0.0</div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="general">常规设置</button>
            <button class="nav-tab" data-tab="ai">AI配置</button>
            <button class="nav-tab" data-tab="data">数据管理</button>
            <button class="nav-tab" data-tab="security">安全隐私</button>
            <button class="nav-tab" data-tab="about">关于</button>
        </div>

        <!-- 设置内容 -->
        <div class="content">
            <!-- 常规设置 -->
            <div class="tab-content active" id="general">
                <div class="section">
                    <h2>基本设置</h2>
                    
                    <div class="setting-item">
                        <label for="defaultMode">默认填充模式</label>
                        <select id="defaultMode">
                            <option value="smart">AI智能模式</option>
                            <option value="template">模板填充</option>
                            <option value="api">API直接提交</option>
                        </select>
                        <p class="description">选择扩展启动时的默认填充模式</p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoFill">
                            <span class="checkmark"></span>
                            检测到MDAC页面时自动填充
                        </label>
                        <p class="description">在打开MDAC网站时自动开始填充流程</p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showNotifications">
                            <span class="checkmark"></span>
                            显示操作通知
                        </label>
                        <p class="description">在页面上显示填充状态和结果通知</p>
                    </div>

                    <div class="setting-item">
                        <label for="fillSpeed">填充速度</label>
                        <input type="range" id="fillSpeed" min="1" max="5" value="3">
                        <div class="range-labels">
                            <span>慢</span>
                            <span>快</span>
                        </div>
                        <p class="description">调整表单字段的填充速度</p>
                    </div>
                </div>

                <div class="section">
                    <h2>界面设置</h2>
                    
                    <div class="setting-item">
                        <label for="language">界面语言</label>
                        <select id="language">
                            <option value="zh-CN">简体中文</option>
                            <option value="en-US">English</option>
                            <option value="ms-MY">Bahasa Malaysia</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="darkMode">
                            <span class="checkmark"></span>
                            深色模式
                        </label>
                        <p class="description">使用深色主题界面</p>
                    </div>
                </div>
            </div>

            <!-- AI配置 -->
            <div class="tab-content" id="ai">
                <div class="section">
                    <h2>Gemini AI配置</h2>
                    
                    <div class="setting-item">
                        <label for="geminiApiKey">Gemini API密钥</label>
                        <div class="input-group">
                            <input type="password" id="geminiApiKey" placeholder="输入您的Gemini API密钥">
                            <button type="button" id="toggleApiKey" class="toggle-btn">👁️</button>
                        </div>
                        <p class="description">
                            获取API密钥：<a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
                        </p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="aiEnabled">
                            <span class="checkmark"></span>
                            启用AI功能
                        </label>
                        <p class="description">开启AI智能验证和建议功能</p>
                    </div>

                    <div class="setting-item">
                        <label for="aiModel">AI模型</label>
                        <select id="aiModel">
                            <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite (推荐)</option>
                            <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        </select>
                    </div>

                    <div class="setting-item">
                        <label for="aiTemperature">AI创造性</label>
                        <input type="range" id="aiTemperature" min="0" max="1" step="0.1" value="0.7">
                        <div class="range-labels">
                            <span>保守</span>
                            <span>创新</span>
                        </div>
                        <p class="description">调整AI回答的创造性程度</p>
                    </div>

                    <div class="setting-item">
                        <button type="button" id="testAI" class="btn primary">测试AI连接</button>
                        <div id="aiTestResult" class="test-result"></div>
                    </div>
                </div>
            </div>

            <!-- 数据管理 -->
            <div class="tab-content" id="data">
                <div class="section">
                    <h2>个人信息模板</h2>
                    
                    <div class="template-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="templateName">姓名</label>
                                <input type="text" id="templateName" placeholder="护照上的英文姓名">
                            </div>
                            <div class="form-group">
                                <label for="templatePassport">护照号码</label>
                                <input type="text" id="templatePassport" placeholder="护照号码">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="templateDOB">出生日期</label>
                                <input type="date" id="templateDOB">
                            </div>
                            <div class="form-group">
                                <label for="templateNationality">国籍</label>
                                <select id="templateNationality">
                                    <option value="CHN">中国 (CHN)</option>
                                    <option value="USA">美国 (USA)</option>
                                    <option value="SGP">新加坡 (SGP)</option>
                                    <option value="THA">泰国 (THA)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="templateEmail">电子邮箱</label>
                                <input type="email" id="templateEmail" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="templatePhone">手机号码</label>
                                <input type="tel" id="templatePhone" placeholder="不含国家代码">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" id="saveTemplate" class="btn primary">保存模板</button>
                            <button type="button" id="loadTemplate" class="btn secondary">加载模板</button>
                            <button type="button" id="clearTemplate" class="btn danger">清空</button>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h2>数据管理</h2>
                    
                    <div class="data-stats">
                        <div class="stat-item">
                            <div class="stat-number" id="templateCount">0</div>
                            <div class="stat-label">保存的模板</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="historyCount">0</div>
                            <div class="stat-label">填充历史</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="storageUsed">0KB</div>
                            <div class="stat-label">存储使用</div>
                        </div>
                    </div>

                    <div class="data-actions">
                        <button type="button" id="exportData" class="btn secondary">导出数据</button>
                        <button type="button" id="importData" class="btn secondary">导入数据</button>
                        <button type="button" id="clearHistory" class="btn danger">清空历史</button>
                        <button type="button" id="clearAllData" class="btn danger">清空所有数据</button>
                    </div>
                </div>
            </div>

            <!-- 安全隐私 -->
            <div class="tab-content" id="security">
                <div class="section">
                    <h2>隐私保护</h2>
                    
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="encryptData">
                            <span class="checkmark"></span>
                            加密存储敏感数据
                        </label>
                        <p class="description">使用加密算法保护存储的个人信息</p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoCleanHistory">
                            <span class="checkmark"></span>
                            自动清理历史记录
                        </label>
                        <p class="description">30天后自动删除填充历史记录</p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="anonymousUsage">
                            <span class="checkmark"></span>
                            匿名使用统计
                        </label>
                        <p class="description">帮助改进扩展功能（不包含个人信息）</p>
                    </div>
                </div>

                <div class="section">
                    <h2>安全设置</h2>
                    
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="debugMode">
                            <span class="checkmark"></span>
                            调试模式
                        </label>
                        <p class="description">启用详细的调试日志（仅供开发者使用）</p>
                    </div>

                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="strictMode">
                            <span class="checkmark"></span>
                            严格模式
                        </label>
                        <p class="description">启用额外的安全检查和验证</p>
                    </div>
                </div>
            </div>

            <!-- 关于 -->
            <div class="tab-content" id="about">
                <div class="section">
                    <h2>关于MDAC AI智能填充工具</h2>
                    
                    <div class="about-info">
                        <div class="about-item">
                            <strong>版本：</strong> 1.0.0
                        </div>
                        <div class="about-item">
                            <strong>开发者：</strong> MDAC AI Team
                        </div>
                        <div class="about-item">
                            <strong>更新时间：</strong> 2025年1月
                        </div>
                        <div class="about-item">
                            <strong>许可证：</strong> MIT License
                        </div>
                    </div>

                    <div class="feature-list">
                        <h3>主要功能</h3>
                        <ul>
                            <li>🤖 AI智能填充和验证</li>
                            <li>📋 多模板管理</li>
                            <li>⚡ API直接提交（实验性）</li>
                            <li>🔒 数据加密保护</li>
                            <li>📊 填充历史记录</li>
                            <li>🌐 多语言支持</li>
                        </ul>
                    </div>

                    <div class="links">
                        <h3>相关链接</h3>
                        <div class="link-buttons">
                            <a href="#" class="btn secondary" id="helpLink">使用帮助</a>
                            <a href="#" class="btn secondary" id="feedbackLink">问题反馈</a>
                            <a href="#" class="btn secondary" id="updateLink">检查更新</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作 -->
        <div class="footer">
            <div class="status" id="saveStatus"></div>
            <div class="actions">
                <button type="button" id="resetSettings" class="btn secondary">重置设置</button>
                <button type="button" id="saveSettings" class="btn primary">保存设置</button>
            </div>
        </div>
    </div>

    <!-- 文件输入（隐藏） -->
    <input type="file" id="fileInput" accept=".json" style="display: none;">

    <script src="options.js"></script>
</body>
</html>
