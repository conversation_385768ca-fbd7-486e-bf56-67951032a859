/**
 * MDAC AI智能填充工具 - AI配置文件
 * 从原始HTML文件提取的Gemini AI配置和功能
 */

// Gemini AI配置（从原文件提取）
const GEMINI_CONFIG = {
    // 默认API密钥（从原文件提取）
    DEFAULT_API_KEY: 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s',
    
    // 默认模型
    DEFAULT_MODEL: 'gemini-2.5-flash-lite-preview-06-17',
    
    // API基础URL
    API_BASE_URL: 'https://generativelanguage.googleapis.com/v1beta/models',
    
    // 默认生成配置
    DEFAULT_GENERATION_CONFIG: {
        temperature: 0.,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1024,
        candidateCount: 1
    },
    
    // 安全设置
    SAFETY_SETTINGS: [
        {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
};

// AI提示词模板（从原文件提取）
const AI_PROMPTS = {
    // 字段验证提示词
    FIELD_VALIDATION: {
        name: (value) => `请验证"${value}"是否是有效的英文姓名格式，适用于护照和官方文件。`,
        passportNo: (value) => `请验证"${value}"是否是有效的护照号码格式。`,
        email: (value) => `请验证"${value}"是否是有效的电子邮箱地址格式。`,
        mobileNo: (value) => `请验证"${value}"是否是有效的手机号码格式。`,
        postcode: (value) => `请验证"${value}"是否是马来西亚的有效邮政编码。`,
        address: (value) => `请验证"${value}"是否是合适的马来西亚地址格式。`,
        city: (value) => `确认"${value}"是否是马来西亚的有效城市名称，如果不是请提供建议。`
    },
    
    // 表单优化提示词
    FORM_OPTIMIZATION: `请全面分析以下MDAC表单数据，检查：

表单数据：
{formData}

请检查：
1. 数据格式是否正确
2. 必填字段是否完整
3. 逻辑关系是否合理（如到达日期应早于离开日期）
4. 完整性
5. 提供改进建议

请保持建议实用且简洁。`,

    // 内容解析提示词
    CONTENT_PARSING: `请从以下文本中提取MDAC表单所需的信息：

文本内容：
{content}

请提取以下字段（如果存在）：
- name: 姓名
- passportNo: 护照号码
- dateOfBirth: 出生日期
- nationality: 国籍
- sex: 性别
- email: 邮箱
- mobileNo: 手机号码
- arrivalDate: 到达日期
- departureDate: 离开日期
- flightNo: 航班号
- address: 地址

请只返回JSON格式的数据，不要包含其他说明。如果某个字段无法确定，请设为null。
对于中文信息，请自动翻译为英文。`,

    // 地址翻译提示词
    ADDRESS_TRANSLATION: `请将以下中文地址翻译为英文，适用于马来西亚入境卡填写：

中文地址：{address}

翻译要求：
1. 保持地址的准确性和完整性
2. 使用标准的英文地址格式
3. 确保只包含字母、数字、空格、逗号和句号
4. 适合官方表单填写

请只返回翻译后的英文地址，不要包含其他说明。`,

    // API分析提示词
    API_ANALYSIS: `请分析以下MDAC API请求结构，提供技术分析和实现建议：

API请求数据：
{requests}

请分析：
1. API端点的功能和用途
2. 请求参数的格式和要求
3. 安全机制（CSRF、认证等）
4. 实现直接提交的技术方案
5. 潜在的技术风险和挑战`,

    // 连接测试提示词
    CONNECTION_TEST: '请回复"AI连接正常"'
};

// AI上下文模板（从原文件提取）
const AI_CONTEXTS = {
    FORM_VALIDATOR: '你是一个专业的表单验证助手，专门验证马来西亚数字入境卡的数据。',
    FORM_AUDITOR: '你是一个专业的表单审核专家，专门检查马来西亚数字入境卡的填写质量。',
    DATA_EXTRACTOR: '你是一个专业的信息提取专家，专门从文本中提取结构化数据。',
    ADDRESS_TRANSLATOR: '你是一个专业的地址翻译助手，专门处理马来西亚地址的中英文翻译。',
    API_ANALYST: '你是一个专业的API分析专家，专门分析Web API的结构和实现方案。',
    CONTENT_OPTIMIZER: '你是一个专业的内容优化顾问，专门优化MDAC表单填写内容。',
    CONNECTION_TESTER: '这是一个连接测试'
};

// AI功能配置
const AI_FEATURES = {
    // 实时验证
    REALTIME_VALIDATION: {
        enabled: true,
        debounceTime: 500,
        minFields: 3
    },
    
    // 内容解析
    CONTENT_PARSING: {
        enabled: true,
        autoTranslate: true,
        supportedLanguages: ['zh-CN', 'en-US', 'ms-MY']
    },
    
    // 地址翻译
    ADDRESS_TRANSLATION: {
        enabled: true,
        autoDetect: true,
        cleanResult: true
    },
    
    // 表单优化
    FORM_OPTIMIZATION: {
        enabled: true,
        autoSuggest: true,
        detailedAnalysis: true
    },
    
    // API分析
    API_ANALYSIS: {
        enabled: true,
        experimental: true,
        requiresPermission: true
    }
};

// 缓存配置
const CACHE_CONFIG = {
    enabled: true,
    maxSize: 100,
    ttl: 3600000, // 1小时
    keyPrefix: 'mdac_ai_cache_'
};

// 错误处理配置
const ERROR_HANDLING = {
    retryAttempts: 3,
    retryDelay: 1000,
    fallbackToBasic: true,
    logErrors: true
};

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        GEMINI_CONFIG,
        AI_PROMPTS,
        AI_CONTEXTS,
        AI_FEATURES,
        CACHE_CONFIG,
        ERROR_HANDLING
    };
} else {
    // 浏览器环境
    window.MDAC_AI_CONFIG = {
        GEMINI_CONFIG,
        AI_PROMPTS,
        AI_CONTEXTS,
        AI_FEATURES,
        CACHE_CONFIG,
        ERROR_HANDLING
    };
}
