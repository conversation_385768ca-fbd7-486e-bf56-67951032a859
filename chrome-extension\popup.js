/**
 * MDAC AI智能填充工具 - Popup脚本
 * 处理扩展弹窗的用户界面和交互逻辑
 */

class MDACPopup {
    constructor() {
        this.currentTab = null;
        this.isMDACPage = false;
        this.aiStatus = 'ready';
        this.parsedData = null;
        this.dataPreviewManager = null;
        this.errorRecoveryManager = null;
        this.userGuideManager = null;

        this.init();
    }

    /**
     * 初始化弹窗
     */
    async init() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.setupEventListeners();
        await this.loadUserSettings();
        this.initializeDataPreviewManager();
        this.initializeErrorRecoveryManager();
        this.initializeUserGuideManager();
        this.updateUI();
        await this.testAIConnection();
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 检测是否为MDAC页面
     */
    async detectMDACPage() {
        if (!this.currentTab) return;

        const detectionStatus = document.getElementById('detectionStatus');
        
        if (this.currentTab.url && this.currentTab.url.includes('imigresen-online.imi.gov.my')) {
            this.isMDACPage = true;
            detectionStatus.className = 'detection-status detected';
            detectionStatus.innerHTML = '<span class="icon">✅</span><span class="text">已检测到MDAC网站</span>';
        } else {
            this.isMDACPage = false;
            detectionStatus.className = 'detection-status not-detected';
            detectionStatus.innerHTML = '<span class="icon">❌</span><span class="text">请先打开MDAC网站</span>';
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 快速操作按钮
        document.getElementById('quickFillBtn').addEventListener('click', () => this.quickFill());
        document.getElementById('openFormBtn').addEventListener('click', () => this.openFormEditor());
        document.getElementById('contentParseBtn').addEventListener('click', () => this.showContentParser());

        // 智能解析面板事件
        document.getElementById('closeParserBtn').addEventListener('click', () => this.hideContentParser());
        document.getElementById('parseContentBtn').addEventListener('click', () => this.parseContent());
        document.getElementById('clearContentBtn').addEventListener('click', () => this.clearContent());
        document.getElementById('editDataBtn').addEventListener('click', () => this.editParsedData());
        document.getElementById('saveTemplateFromParseBtn').addEventListener('click', () => this.copyParsedData());
        document.getElementById('previewAndFillBtn').addEventListener('click', () => this.showDataPreview());

        // AI功能状态更新
        this.updateAIFeatureStatus();

        // 底部按钮
        document.getElementById('aiSettingsBtn').addEventListener('click', () => this.openAISettings());
        document.getElementById('helpBtn').addEventListener('click', () => this.showHelp());

        // 模态对话框
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancel').addEventListener('click', () => this.closeModal());
        document.getElementById('modalConfirm').addEventListener('click', () => this.confirmModal());
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            // AI设置已在background.js中初始化
            console.log('AI设置已加载');
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    /**
     * 初始化数据预览管理器
     */
    initializeDataPreviewManager() {
        try {
            this.dataPreviewManager = new DataPreviewManager();
            console.log('✅ 数据预览管理器初始化完成');
        } catch (error) {
            console.error('❌ 数据预览管理器初始化失败:', error);
        }
    }

    /**
     * 初始化错误恢复管理器
     */
    async initializeErrorRecoveryManager() {
        try {
            this.errorRecoveryManager = new ErrorRecoveryManager();
            await this.errorRecoveryManager.loadErrorHistory();
            console.log('✅ 错误恢复管理器初始化完成');
        } catch (error) {
            console.error('❌ 错误恢复管理器初始化失败:', error);
        }
    }

    /**
     * 初始化用户引导管理器
     */
    async initializeUserGuideManager() {
        try {
            this.userGuideManager = new UserGuideManager();
            console.log('✅ 用户引导管理器初始化完成');
        } catch (error) {
            console.error('❌ 用户引导管理器初始化失败:', error);
        }
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新按钮状态
        const quickFillBtn = document.getElementById('quickFillBtn');
        const openFormBtn = document.getElementById('openFormBtn');

        if (this.isMDACPage) {
            quickFillBtn.disabled = false;
            openFormBtn.disabled = false;
        } else {
            quickFillBtn.disabled = true;
            openFormBtn.disabled = true;
        }

        // 更新AI状态
        this.updateAIStatus();
    }

    /**
     * 更新AI状态显示
     */
    updateAIStatus() {
        const aiStatus = document.getElementById('aiStatus');
        const aiIndicator = document.getElementById('aiIndicator');
        
        switch (this.aiStatus) {
            case 'ready':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI就绪</span>';
                aiIndicator.style.background = '#4ade80';
                break;
            case 'working':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI工作中</span>';
                aiIndicator.style.background = '#fbbf24';
                break;
            case 'error':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI错误</span>';
                aiIndicator.style.background = '#ef4444';
                break;
        }
    }

    /**
     * 更新AI功能状态
     */
    updateAIFeatureStatus() {
        const features = document.querySelectorAll('.feature-status');
        features.forEach(status => {
            status.classList.add('active');
        });
    }

    /**
     * 快速填充
     */
    async quickFill() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'error');
            return;
        }

        this.aiStatus = 'working';
        this.updateAIStatus();

        try {
            // 向content script发送AI智能填充指令
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'quickFill'
            });

            this.aiStatus = 'ready';
            this.updateAIStatus();
            this.showMessage('AI智能填充完成', 'success');
        } catch (error) {
            console.error('快速填充失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('填充失败，请重试', 'error');
        }
    }

    /**
     * 打开表单编辑器
     */
    openFormEditor() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('form-editor.html')
        });
    }

    /**
     * 打开AI设置页面
     */
    openAISettings() {
        chrome.runtime.openOptionsPage();
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        this.showModal('AI智能分析帮助', `
            <div class="help-content">
                <h4>🧠 智能内容解析</h4>
                <p>1. 点击"智能解析"按钮</p>
                <p>2. 粘贴任意格式的文本内容</p>
                <p>3. AI自动提取表单数据</p>
                <p>4. 验证完整性后一键填充</p>

                <h4>✅ AI数据验证</h4>
                <p>• 自动验证数据格式和逻辑</p>
                <p>• 提供智能优化建议</p>
                <p>• 实时错误检测和修正</p>

                <h4>🌐 智能地址翻译</h4>
                <p>• 自动识别中文地址</p>
                <p>• 翻译为标准英文格式</p>
                <p>• 适配马来西亚官方要求</p>

                <h4>🤖 AI助手功能</h4>
                <p>• 实时状态监控</p>
                <p>• 智能建议和提示</p>
                <p>• 错误诊断和解决方案</p>
            </div>
        `);
    }





    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        const aiSuggestions = document.getElementById('aiSuggestions');
        const icons = {
            'success': '✅',
            'error': '❌',
            'info': 'ℹ️',
            'warning': '⚠️'
        };
        
        aiSuggestions.innerHTML = `${icons[type]} ${message}`;
        
        // 3秒后恢复默认状态
        setTimeout(() => {
            aiSuggestions.innerHTML = '准备就绪，等待您的指令...';
        }, 3000);
    }

    /**
     * 显示模态对话框
     */
    showModal(title, content) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalBody').innerHTML = content;
        document.getElementById('modal').classList.add('show');
    }

    /**
     * 关闭模态对话框
     */
    closeModal() {
        document.getElementById('modal').classList.remove('show');
    }

    /**
     * 确认模态对话框
     */
    confirmModal() {
        // 处理确认逻辑
        this.closeModal();
    }



    /**
     * 测试AI连接
     */
    async testAIConnection() {
        try {
            this.aiStatus = 'working';
            this.updateAIStatus();

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: '请回复"AI连接正常"',
                context: '这是一个连接测试'
            });

            if (response.success && response.data) {
                this.aiStatus = 'ready';
                this.updateAIStatus();
                this.showMessage('🤖 AI助手已就绪！Gemini AI连接正常，所有智能功能可用。', 'success');
            } else {
                throw new Error('AI响应异常');
            }
        } catch (error) {
            console.warn('Gemini AI连接测试失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('⚠️ AI服务连接异常，将使用基础验证功能。', 'warning');
        }
    }

    /**
     * 获取AI状态描述
     */
    getAIStatusDescription() {
        switch (this.aiStatus) {
            case 'ready':
                return '🤖 AI助手已就绪！所有智能功能可用。\n\n💡 功能包括：\n• AI智能验证和建议\n• 实时内容解析\n• 智能地址翻译\n• 表单优化建议';
            case 'working':
                return '🔄 AI正在工作中...';
            case 'error':
                return '⚠️ AI服务暂时不可用\n\n📝 基础功能仍然可用：\n• 表单验证和格式检查\n• 自动填充和数据保存\n• 模板管理和历史记录';
            default:
                return '准备就绪，等待您的指令...';
        }
    }

    /**
     * 更新AI建议显示
     */
    updateAISuggestions(message) {
        const aiSuggestions = document.getElementById('aiSuggestions');
        if (aiSuggestions) {
            aiSuggestions.innerHTML = message || this.getAIStatusDescription();
        }
    }

    /**
     * 显示智能内容解析面板
     */
    showContentParser() {
        const parser = document.getElementById('contentParser');
        const mainContent = document.getElementById('mainContent');

        if (parser && mainContent) {
            parser.style.display = 'block';
            mainContent.style.display = 'none';
        }
    }

    /**
     * 隐藏智能内容解析面板
     */
    hideContentParser() {
        const parser = document.getElementById('contentParser');
        const mainContent = document.getElementById('mainContent');

        if (parser && mainContent) {
            parser.style.display = 'none';
            mainContent.style.display = 'block';
            this.clearParseResults();
        }
    }

    /**
     * 清空内容输入
     */
    clearContent() {
        const contentInput = document.getElementById('contentInput');
        if (contentInput) {
            contentInput.value = '';
            this.clearParseResults();
        }
    }

    /**
     * 清空解析结果
     */
    clearParseResults() {
        const parsingStatus = document.getElementById('parsingStatus');
        const parseResults = document.getElementById('parseResults');

        if (parsingStatus) parsingStatus.style.display = 'none';
        if (parseResults) parseResults.style.display = 'none';

        this.parsedData = null;
    }

    /**
     * 解析内容
     */
    async parseContent() {
        const contentInput = document.getElementById('contentInput');
        const content = contentInput.value.trim();

        if (!content) {
            this.showMessage('请先输入要解析的内容', 'warning');
            return;
        }

        this.showParsingStatus();

        try {
            // 调用AI解析内容
            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: this.buildContentParsePrompt(content),
                context: '你是一个专业的信息提取专家，专门从文本中提取结构化数据。'
            });

            if (response.success) {
                await this.processParseResult(response.data);

                // 触发首次解析成功引导
                if (this.userGuideManager) {
                    this.userGuideManager.triggerScenarioGuide('firstParseSuccess');
                }
            } else {
                throw new Error('AI解析失败');
            }
        } catch (error) {
            console.error('内容解析失败:', error);
            this.hideParsingStatus();

            // 使用错误恢复管理器处理错误
            if (this.errorRecoveryManager) {
                this.errorRecoveryManager.handleError(error, {
                    operation: 'parseContent',
                    data: content,
                    retryFunction: () => this.parseContent()
                });
            } else {
                this.showMessage('内容解析失败: ' + error.message, 'error');
            }
        }
    }

    /**
     * 构建内容解析提示词
     */
    buildContentParsePrompt(content) {
        return `请从以下文本中提取MDAC表单所需的信息：

文本内容：
${content}

请提取以下字段（如果存在）：
- name: 姓名（英文全名）
- passportNo: 护照号码
- dateOfBirth: 出生日期（DD/MM/YYYY格式）
- nationality: 国籍（3位代码，如CHN、USA等）
- sex: 性别（1=男性，2=女性）
- passportExpiry: 护照到期日期（DD/MM/YYYY格式）
- email: 电子邮箱
- confirmEmail: 确认邮箱（与email相同）
- countryCode: 国家代码（如+86、+60等）
- mobileNo: 手机号码（不含国家代码）
- arrivalDate: 到达日期（DD/MM/YYYY格式）
- departureDate: 离开日期（DD/MM/YYYY格式）
- flightNo: 航班号或交通工具编号
- modeOfTravel: 旅行方式（AIR=航空，LAND=陆路，SEA=海路）
- lastPort: 最后港口（3位代码）
- accommodation: 住宿类型（01=酒店，02=朋友家，99=其他）
- address: 马来西亚地址行1（英文）
- address2: 马来西亚地址行2（英文，可选）
- state: 州代码（如14=吉隆坡）
- postcode: 邮政编码（5位数字）
- city: 城市代码

请只返回JSON格式的数据，不要包含其他说明。如果某个字段无法确定，请设为null。
对于中文信息，请自动翻译为英文。确保日期格式为DD/MM/YYYY。`;
    }

    /**
     * 显示解析状态
     */
    showParsingStatus() {
        const parsingStatus = document.getElementById('parsingStatus');
        const parseResults = document.getElementById('parseResults');
        const progressFill = document.getElementById('parseProgress');

        if (parsingStatus) {
            parsingStatus.style.display = 'block';

            // 模拟进度条动画
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                if (progressFill) {
                    progressFill.style.width = progress + '%';
                }

                if (progress >= 90) {
                    clearInterval(progressInterval);
                }
            }, 200);

            this.progressInterval = progressInterval;
        }

        if (parseResults) parseResults.style.display = 'none';
    }

    /**
     * 隐藏解析状态
     */
    hideParsingStatus() {
        const parsingStatus = document.getElementById('parsingStatus');
        if (parsingStatus) parsingStatus.style.display = 'none';

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    /**
     * 处理解析结果
     */
    async processParseResult(aiResponse) {
        this.hideParsingStatus();

        try {
            // 清理AI返回的JSON
            const cleanResult = aiResponse.replace(/```json|```/g, '').trim();
            const extractedData = JSON.parse(cleanResult);

            this.parsedData = extractedData;
            this.displayParseResults(extractedData);

        } catch (parseError) {
            console.error('解析AI返回的JSON失败:', parseError);
            this.showMessage('AI解析结果格式异常，请重试', 'error');
        }
    }

    /**
     * 显示解析结果
     */
    displayParseResults(data) {
        const parseResults = document.getElementById('parseResults');
        const extractedData = document.getElementById('extractedData');

        if (!parseResults || !extractedData) return;

        // 定义字段映射和必填字段
        const fieldLabels = {
            name: '姓名',
            passportNo: '护照号码',
            dateOfBirth: '出生日期',
            nationality: '国籍',
            sex: '性别',
            passportExpiry: '护照到期日期',
            email: '电子邮箱',
            confirmEmail: '确认邮箱',
            countryCode: '国家代码',
            mobileNo: '手机号码',
            arrivalDate: '到达日期',
            departureDate: '离开日期',
            flightNo: '航班号',
            modeOfTravel: '旅行方式',
            lastPort: '最后港口',
            accommodation: '住宿类型',
            address: '地址行1',
            address2: '地址行2',
            state: '州',
            postcode: '邮政编码',
            city: '城市'
        };

        const requiredFields = [
            'name', 'passportNo', 'dateOfBirth', 'nationality', 'sex',
            'passportExpiry', 'email', 'countryCode', 'mobileNo',
            'arrivalDate', 'departureDate', 'flightNo', 'modeOfTravel',
            'lastPort', 'accommodation', 'address', 'state', 'postcode', 'city'
        ];

        // 生成数据显示
        let dataHtml = '';
        let filledCount = 0;
        const missingFields = [];

        Object.keys(fieldLabels).forEach(fieldId => {
            const label = fieldLabels[fieldId];
            const value = data[fieldId];
            const hasValue = value && value.toString().trim() !== '';

            if (hasValue) filledCount++;
            else if (requiredFields.includes(fieldId)) {
                missingFields.push(label);
            }

            dataHtml += `
                <div class="data-item">
                    <span class="data-label">${label}:</span>
                    <span class="data-value ${hasValue ? '' : 'empty'}">${hasValue ? value : '未提取'}</span>
                </div>
            `;
        });

        extractedData.innerHTML = dataHtml;

        // 更新完整度指示器
        const completeness = Math.round((filledCount / Object.keys(fieldLabels).length) * 100);
        this.updateCompletenessIndicator(completeness);

        // 显示缺失字段
        this.displayMissingFields(missingFields);

        // 更新预览填充按钮状态
        const previewFillBtn = document.getElementById('previewAndFillBtn');
        const canPreview = Object.keys(this.parsedData).length > 0;

        if (previewFillBtn) {
            previewFillBtn.disabled = !canPreview;
            previewFillBtn.title = canPreview ? '预览并确认数据后填充' : '没有可预览的数据';
        }

        parseResults.style.display = 'block';
        this.showMessage(`解析完成！提取了${filledCount}个字段`, 'success');
    }

    /**
     * 更新完整度指示器
     */
    updateCompletenessIndicator(percentage) {
        const completenessText = document.querySelector('.completeness-text');
        const completenessFill = document.getElementById('completenessFill');

        if (completenessText) {
            completenessText.textContent = `完整度: ${percentage}%`;
        }

        if (completenessFill) {
            completenessFill.style.width = percentage + '%';

            // 根据完整度改变颜色
            if (percentage >= 90) {
                completenessFill.style.background = 'linear-gradient(90deg, #28a745, #20c997)';
            } else if (percentage >= 70) {
                completenessFill.style.background = 'linear-gradient(90deg, #ffc107, #fd7e14)';
            } else {
                completenessFill.style.background = 'linear-gradient(90deg, #dc3545, #e74c3c)';
            }
        }
    }

    /**
     * 显示缺失字段
     */
    displayMissingFields(missingFields) {
        const missingFieldsDiv = document.getElementById('missingFields');
        const missingList = document.getElementById('missingList');

        if (!missingFieldsDiv || !missingList) return;

        if (missingFields.length > 0) {
            let missingHtml = '';
            missingFields.forEach(field => {
                missingHtml += `<span class="missing-item">${field}</span>`;
            });

            missingList.innerHTML = missingHtml;
            missingFieldsDiv.style.display = 'block';
        } else {
            missingFieldsDiv.style.display = 'none';
        }
    }

    /**
     * 编辑解析数据
     */
    editParsedData() {
        if (!this.parsedData) {
            this.showMessage('没有可编辑的数据', 'warning');
            return;
        }

        // 打开表单编辑器并预填数据
        chrome.tabs.create({
            url: chrome.runtime.getURL('form-editor.html') + '?data=' + encodeURIComponent(JSON.stringify(this.parsedData))
        });
    }

    /**
     * 复制解析数据到剪贴板
     */
    async copyParsedData() {
        if (!this.parsedData) {
            this.showMessage('没有可保存的数据', 'warning');
            return;
        }

        try {
            const dataText = JSON.stringify(this.parsedData, null, 2);
            await navigator.clipboard.writeText(dataText);
            this.showMessage('数据已复制到剪贴板', 'success');
        } catch (error) {
            console.error('复制数据失败:', error);
            this.showMessage('复制数据失败', 'error');
        }
    }

    /**
     * 显示数据预览界面
     */
    showDataPreview() {
        if (!this.parsedData) {
            this.showMessage('没有可预览的数据', 'warning');
            return;
        }

        if (!this.dataPreviewManager) {
            this.showMessage('数据预览功能未初始化', 'error');
            return;
        }

        console.log('🔍 显示数据预览界面');

        // 显示预览界面
        this.dataPreviewManager.showPreview(this.parsedData, {
            onConfirm: (confirmedData) => {
                console.log('✅ 用户确认数据:', confirmedData);
                this.handleConfirmedData(confirmedData);
            },
            onCancel: () => {
                console.log('❌ 用户取消预览');
                this.showMessage('已取消数据预览', 'info');
            },
            onChange: (fieldKey, value, allData) => {
                console.log(`📝 字段 ${fieldKey} 已修改: ${value}`);
                // 可以在这里添加实时保存逻辑
            }
        });

        // 触发数据预览引导
        if (this.userGuideManager) {
            setTimeout(() => {
                this.userGuideManager.triggerScenarioGuide('dataPreviewShown');
            }, 500);
        }
    }

    /**
     * 处理用户确认的数据
     */
    async handleConfirmedData(confirmedData) {
        try {
            // 更新解析数据
            this.parsedData = confirmedData;

            // 执行自动填充
            await this.autoFillFromParse();

            this.showMessage('数据确认完成，开始自动填充', 'success');
        } catch (error) {
            console.error('处理确认数据失败:', error);
            this.handleError(error, {
                operation: 'handleConfirmedData',
                data: confirmedData
            });
        }
    }

    /**
     * 统一错误处理方法
     */
    handleError(error, context = {}) {
        if (this.errorRecoveryManager) {
            this.errorRecoveryManager.handleError(error, context);
        } else {
            // 降级处理
            console.error('错误处理失败:', error);
            this.showMessage('操作失败: ' + error.message, 'error');
        }
    }

    /**
     * 从解析结果自动填充
     */
    async autoFillFromParse() {
        if (!this.parsedData) {
            this.showMessage('没有可填充的数据', 'warning');
            return;
        }

        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'error');
            return;
        }

        try {
            this.aiStatus = 'working';
            this.updateAIStatus();

            // 向content script发送填充指令
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'fillFormData',
                data: this.parsedData
            });

            this.aiStatus = 'ready';
            this.updateAIStatus();
            this.showMessage('AI智能填充完成', 'success');

        } catch (error) {
            console.error('自动填充失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.showMessage('自动填充失败: ' + error.message, 'error');
        }
    }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
    new MDACPopup();
});
