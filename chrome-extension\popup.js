/**
 * MDAC AI智能填充工具 - Popup脚本
 * 处理扩展弹窗的用户界面和交互逻辑
 */

class MDACPopup {
    constructor() {
        this.currentTab = null;
        this.isMDACPage = false;
        this.currentMode = 'smart';
        this.aiStatus = 'ready';
        
        this.init();
    }

    /**
     * 初始化弹窗
     */
    async init() {
        await this.getCurrentTab();
        await this.detectMDACPage();
        this.setupEventListeners();
        this.loadUserSettings();
        this.updateUI();
        this.logOperation('扩展已启动');
    }

    /**
     * 获取当前标签页
     */
    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
        } catch (error) {
            console.error('获取当前标签页失败:', error);
        }
    }

    /**
     * 检测是否为MDAC页面
     */
    async detectMDACPage() {
        if (!this.currentTab) return;

        const detectionStatus = document.getElementById('detectionStatus');
        
        if (this.currentTab.url && this.currentTab.url.includes('imigresen-online.imi.gov.my')) {
            this.isMDACPage = true;
            detectionStatus.className = 'detection-status detected';
            detectionStatus.innerHTML = '<span class="icon">✅</span><span class="text">已检测到MDAC网站</span>';
            this.logOperation('检测到MDAC网站');
        } else {
            this.isMDACPage = false;
            detectionStatus.className = 'detection-status not-detected';
            detectionStatus.innerHTML = '<span class="icon">❌</span><span class="text">请先打开MDAC网站</span>';
            this.logOperation('未检测到MDAC网站');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 快速操作按钮
        document.getElementById('quickFillBtn').addEventListener('click', () => this.quickFill());
        document.getElementById('openFormBtn').addEventListener('click', () => this.openFormEditor());

        // 模式选择
        document.querySelectorAll('.mode-option').forEach(option => {
            option.addEventListener('click', (e) => this.selectMode(e.currentTarget.dataset.mode));
        });

        // 数据管理按钮
        document.getElementById('loadTemplateBtn').addEventListener('click', () => this.loadTemplate());
        document.getElementById('saveTemplateBtn').addEventListener('click', () => this.saveTemplate());
        document.getElementById('historyBtn').addEventListener('click', () => this.showHistory());

        // 底部按钮
        document.getElementById('settingsBtn').addEventListener('click', () => this.openSettings());
        document.getElementById('helpBtn').addEventListener('click', () => this.showHelp());
        document.getElementById('aboutBtn').addEventListener('click', () => this.showAbout());

        // 日志清空按钮
        document.getElementById('clearLogBtn').addEventListener('click', () => this.clearLog());

        // 模态对话框
        document.getElementById('modalClose').addEventListener('click', () => this.closeModal());
        document.getElementById('modalCancel').addEventListener('click', () => this.closeModal());
        document.getElementById('modalConfirm').addEventListener('click', () => this.confirmModal());
    }

    /**
     * 加载用户设置
     */
    async loadUserSettings() {
        try {
            const settings = await chrome.storage.sync.get(['mdacSettings']);
            if (settings.mdacSettings) {
                this.currentMode = settings.mdacSettings.defaultMode || 'smart';
                this.updateModeSelection();
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    /**
     * 更新UI状态
     */
    updateUI() {
        // 更新按钮状态
        const quickFillBtn = document.getElementById('quickFillBtn');
        const openFormBtn = document.getElementById('openFormBtn');

        if (this.isMDACPage) {
            quickFillBtn.disabled = false;
            openFormBtn.disabled = false;
        } else {
            quickFillBtn.disabled = true;
            openFormBtn.disabled = true;
        }

        // 更新AI状态
        this.updateAIStatus();
    }

    /**
     * 更新AI状态显示
     */
    updateAIStatus() {
        const aiStatus = document.getElementById('aiStatus');
        const aiIndicator = document.getElementById('aiIndicator');
        
        switch (this.aiStatus) {
            case 'ready':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI就绪</span>';
                aiIndicator.style.background = '#4ade80';
                break;
            case 'working':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI工作中</span>';
                aiIndicator.style.background = '#fbbf24';
                break;
            case 'error':
                aiStatus.innerHTML = '<span class="status-dot"></span><span class="status-text">AI错误</span>';
                aiIndicator.style.background = '#ef4444';
                break;
        }
    }

    /**
     * 选择填充模式
     */
    selectMode(mode) {
        this.currentMode = mode;
        this.updateModeSelection();
        this.logOperation(`切换到${this.getModeDisplayName(mode)}模式`);
    }

    /**
     * 更新模式选择UI
     */
    updateModeSelection() {
        document.querySelectorAll('.mode-option').forEach(option => {
            option.classList.remove('active');
            option.querySelector('.mode-status').classList.remove('active');
        });

        const selectedOption = document.querySelector(`[data-mode="${this.currentMode}"]`);
        if (selectedOption) {
            selectedOption.classList.add('active');
            selectedOption.querySelector('.mode-status').classList.add('active');
        }
    }

    /**
     * 获取模式显示名称
     */
    getModeDisplayName(mode) {
        const modeNames = {
            'smart': 'AI智能',
            'template': '模板填充',
            'api': 'API直接提交'
        };
        return modeNames[mode] || mode;
    }

    /**
     * 快速填充
     */
    async quickFill() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'error');
            return;
        }

        this.aiStatus = 'working';
        this.updateAIStatus();
        this.logOperation('开始快速填充');

        try {
            // 向content script发送填充指令
            await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'quickFill',
                mode: this.currentMode
            });

            this.aiStatus = 'ready';
            this.updateAIStatus();
            this.logOperation('快速填充完成');
            this.showMessage('表单填充完成', 'success');
        } catch (error) {
            console.error('快速填充失败:', error);
            this.aiStatus = 'error';
            this.updateAIStatus();
            this.logOperation('快速填充失败: ' + error.message);
            this.showMessage('填充失败，请重试', 'error');
        }
    }

    /**
     * 打开表单编辑器
     */
    openFormEditor() {
        chrome.tabs.create({
            url: chrome.runtime.getURL('form-editor.html')
        });
        this.logOperation('打开表单编辑器');
    }

    /**
     * 加载模板
     */
    async loadTemplate() {
        try {
            const templates = await chrome.storage.local.get(['mdacTemplates']);
            if (templates.mdacTemplates && templates.mdacTemplates.length > 0) {
                this.showTemplateSelector(templates.mdacTemplates);
            } else {
                this.showMessage('暂无保存的模板', 'info');
            }
        } catch (error) {
            console.error('加载模板失败:', error);
            this.showMessage('加载模板失败', 'error');
        }
    }

    /**
     * 保存模板
     */
    async saveTemplate() {
        if (!this.isMDACPage) {
            this.showMessage('请先打开MDAC网站', 'error');
            return;
        }

        try {
            // 从content script获取当前表单数据
            const response = await chrome.tabs.sendMessage(this.currentTab.id, {
                action: 'getFormData'
            });

            if (response && response.data) {
                await this.saveTemplateData(response.data);
                this.showMessage('模板保存成功', 'success');
                this.logOperation('保存表单模板');
            } else {
                this.showMessage('无法获取表单数据', 'error');
            }
        } catch (error) {
            console.error('保存模板失败:', error);
            this.showMessage('保存模板失败', 'error');
        }
    }

    /**
     * 显示填充历史
     */
    async showHistory() {
        try {
            const history = await chrome.storage.local.get(['mdacHistory']);
            if (history.mdacHistory && history.mdacHistory.length > 0) {
                this.showHistoryModal(history.mdacHistory);
            } else {
                this.showMessage('暂无填充历史', 'info');
            }
        } catch (error) {
            console.error('加载历史失败:', error);
            this.showMessage('加载历史失败', 'error');
        }
    }

    /**
     * 打开设置页面
     */
    openSettings() {
        chrome.runtime.openOptionsPage();
        this.logOperation('打开设置页面');
    }

    /**
     * 显示帮助信息
     */
    showHelp() {
        this.showModal('帮助信息', `
            <div class="help-content">
                <h4>🚀 快速开始</h4>
                <p>1. 打开MDAC官方网站</p>
                <p>2. 点击"一键智能填充"按钮</p>
                <p>3. AI会自动填写表单并验证</p>
                
                <h4>🤖 AI智能模式</h4>
                <p>• 自动验证数据格式</p>
                <p>• 提供智能建议</p>
                <p>• 实时错误检测</p>
                
                <h4>📋 模板填充</h4>
                <p>• 使用预设模板快速填充</p>
                <p>• 支持多个模板管理</p>
                
                <h4>⚡ API直接提交</h4>
                <p>• 实验性功能</p>
                <p>• 绕过网页界面直接提交</p>
                <p>• 仅供技术研究使用</p>
            </div>
        `);
    }

    /**
     * 显示关于信息
     */
    showAbout() {
        this.showModal('关于', `
            <div class="about-content">
                <h4>MDAC AI智能填充工具</h4>
                <p>版本: 1.0.0</p>
                <p>基于Gemini AI的马来西亚数字入境卡智能填充工具</p>
                
                <h4>功能特性</h4>
                <p>• AI智能填充和验证</p>
                <p>• 多种填充模式</p>
                <p>• 模板管理</p>
                <p>• 填充历史记录</p>
                <p>• 网络请求分析</p>
                
                <h4>技术支持</h4>
                <p>如有问题请查看帮助文档或联系开发者</p>
            </div>
        `);
    }

    /**
     * 记录操作日志
     */
    logOperation(message) {
        const logContent = document.getElementById('logContent');
        const logItem = document.createElement('div');
        logItem.className = 'log-item';
        
        const now = new Date();
        const timeStr = now.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        logItem.innerHTML = `
            <span class="log-time">${timeStr}</span>
            <span class="log-message">${message}</span>
        `;
        
        logContent.insertBefore(logItem, logContent.firstChild);
        
        // 限制日志条数
        const logItems = logContent.querySelectorAll('.log-item');
        if (logItems.length > 10) {
            logContent.removeChild(logItems[logItems.length - 1]);
        }
    }

    /**
     * 清空操作日志
     */
    clearLog() {
        document.getElementById('logContent').innerHTML = '';
        this.logOperation('日志已清空');
    }

    /**
     * 显示消息提示
     */
    showMessage(message, type = 'info') {
        const aiSuggestions = document.getElementById('aiSuggestions');
        const icons = {
            'success': '✅',
            'error': '❌',
            'info': 'ℹ️',
            'warning': '⚠️'
        };
        
        aiSuggestions.innerHTML = `${icons[type]} ${message}`;
        
        // 3秒后恢复默认状态
        setTimeout(() => {
            aiSuggestions.innerHTML = '准备就绪，等待您的指令...';
        }, 3000);
    }

    /**
     * 显示模态对话框
     */
    showModal(title, content) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalBody').innerHTML = content;
        document.getElementById('modal').classList.add('show');
    }

    /**
     * 关闭模态对话框
     */
    closeModal() {
        document.getElementById('modal').classList.remove('show');
    }

    /**
     * 确认模态对话框
     */
    confirmModal() {
        // 处理确认逻辑
        this.closeModal();
    }

    /**
     * 显示模板选择器
     */
    showTemplateSelector(templates) {
        const templateList = templates.map((template, index) => `
            <div class="template-item" data-index="${index}">
                <div class="template-info">
                    <h4>${template.name}</h4>
                    <p>创建时间: ${new Date(template.created).toLocaleString()}</p>
                </div>
                <button class="template-load-btn" onclick="loadTemplate(${index})">加载</button>
            </div>
        `).join('');

        this.showModal('选择模板', `
            <div class="template-selector">
                ${templateList}
            </div>
        `);
    }

    /**
     * 保存模板数据
     */
    async saveTemplateData(data) {
        const templateName = prompt('请输入模板名称:');
        if (!templateName) return;

        const template = {
            name: templateName,
            data: data,
            created: Date.now()
        };

        const storage = await chrome.storage.local.get(['mdacTemplates']);
        const templates = storage.mdacTemplates || [];
        templates.push(template);

        await chrome.storage.local.set({ mdacTemplates: templates });
    }

    /**
     * 显示历史记录模态框
     */
    showHistoryModal(history) {
        const historyList = history.slice(0, 10).map((item, index) => `
            <div class="history-item">
                <div class="history-info">
                    <p><strong>姓名:</strong> ${item.name || '未知'}</p>
                    <p><strong>时间:</strong> ${new Date(item.timestamp).toLocaleString()}</p>
                    <p><strong>模式:</strong> ${this.getModeDisplayName(item.mode)}</p>
                </div>
                <button class="history-load-btn" onclick="loadHistoryItem(${index})">重新填充</button>
            </div>
        `).join('');

        this.showModal('填充历史', `
            <div class="history-list">
                ${historyList}
            </div>
        `);
    }
}

// 初始化弹窗
document.addEventListener('DOMContentLoaded', () => {
    new MDACPopup();
});
