/**
 * MDAC AI智能填充工具 - 内容脚本
 * 在MDAC官网页面中注入，处理表单填充和页面交互
 */

class MDACContentScript {
    constructor() {
        this.isInitialized = false;
        this.formData = {};
        this.fillMode = 'smart';
        this.aiAssistant = null;
        
        this.init();
    }

    /**
     * 初始化内容脚本
     */
    async init() {
        if (this.isInitialized) return;

        // 等待页面完全加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    /**
     * 设置内容脚本
     */
    async setup() {
        try {
            this.detectPageType();
            this.setupMessageListener();
            this.injectAIAssistant();
            this.observeFormChanges();
            this.isInitialized = true;
            
            console.log('MDAC AI内容脚本已初始化');
            this.notifyExtension('contentScriptReady');
        } catch (error) {
            console.error('内容脚本初始化失败:', error);
        }
    }

    /**
     * 检测页面类型
     */
    detectPageType() {
        const url = window.location.href;
        
        if (url.includes('registerMain')) {
            this.pageType = 'registration';
            this.setupRegistrationPage();
        } else if (url.includes('confirmation')) {
            this.pageType = 'confirmation';
        } else {
            this.pageType = 'unknown';
        }
    }

    /**
     * 设置注册页面
     */
    setupRegistrationPage() {
        // 检查表单是否存在
        const form = document.querySelector('form[name="permohonan"]');
        if (form) {
            this.form = form;
            this.identifyFormFields();
            this.addFormValidation();
        }
    }

    /**
     * 识别表单字段
     */
    identifyFormFields() {
        this.formFields = {
            // 个人信息
            name: document.getElementById('name'),
            passNo: document.getElementById('passNo'),
            dob: document.getElementById('dob'),
            nationality: document.getElementById('nationality'),
            sex: document.getElementById('sex'),
            passExpiry: document.getElementById('passExpiry'),
            email: document.getElementById('email'),
            confirmEmail: document.getElementById('confirmEmail'),
            countryCode: document.getElementById('countryCode'),
            mobileNo: document.getElementById('mobileNo'),
            
            // 旅行信息
            arrivalDate: document.getElementById('arrivalDate'),
            departureDate: document.getElementById('departureDate'),
            vesselNm: document.getElementById('vesselNm'),
            trvlMode: document.getElementById('trvlMode'),
            embark: document.getElementById('embark'),
            
            // 住宿信息
            accommodationStay: document.getElementById('accommodationStay'),
            accommodationAddress1: document.getElementById('accommodationAddress1'),
            accommodationAddress2: document.getElementById('accommodationAddress2'),
            accommodationState: document.getElementById('accommodationState'),
            accommodationPostcode: document.getElementById('accommodationPostcode'),
            accommodationCity: document.getElementById('accommodationCity'),
            
            // 提交按钮
            submitBtn: document.getElementById('submit'),
            resetBtn: document.getElementById('reset')
        };

        // 过滤掉不存在的字段
        Object.keys(this.formFields).forEach(key => {
            if (!this.formFields[key]) {
                delete this.formFields[key];
            }
        });
    }

    /**
     * 设置消息监听器
     */
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    /**
     * 处理来自扩展的消息
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'quickFill':
                    await this.quickFill(message.mode);
                    sendResponse({ success: true });
                    break;

                case 'getFormData':
                    const formData = this.getFormData();
                    sendResponse({ success: true, data: formData });
                    break;

                case 'fillFormData':
                    await this.fillFormData(message.data);
                    sendResponse({ success: true });
                    break;

                case 'validateForm':
                    const validation = await this.validateForm();
                    sendResponse({ success: true, data: validation });
                    break;

                case 'analyzeAPI':
                    await this.analyzeAPI();
                    sendResponse({ success: true });
                    break;

                case 'getPageInfo':
                    const pageInfo = this.getPageInfo();
                    sendResponse({ success: true, data: pageInfo });
                    break;

                default:
                    sendResponse({ success: false, error: '未知的操作类型' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * 快速填充表单
     */
    async quickFill(mode = 'smart') {
        this.fillMode = mode;
        
        try {
            // 获取用户的默认数据
            const userData = await this.getUserData();
            
            if (!userData) {
                this.showNotification('请先在扩展中设置默认数据', 'warning');
                return;
            }

            // 根据模式填充表单
            switch (mode) {
                case 'smart':
                    await this.smartFill(userData);
                    break;
                case 'template':
                    await this.templateFill(userData);
                    break;
                case 'api':
                    await this.apiFill(userData);
                    break;
                default:
                    await this.basicFill(userData);
            }

            this.showNotification('表单填充完成', 'success');
            
            // 保存填充历史
            await this.saveToHistory(userData, mode);
            
        } catch (error) {
            console.error('快速填充失败:', error);
            this.showNotification('填充失败: ' + error.message, 'error');
        }
    }

    /**
     * AI智能填充
     */
    async smartFill(userData) {
        // 首先进行基础填充
        await this.basicFill(userData);

        // 然后进行AI验证和优化
        if (this.fillMode === 'smart') {
            await this.aiValidateAndOptimize();
        }
    }

    /**
     * 模板填充
     */
    async templateFill(userData) {
        await this.basicFill(userData);
        this.showNotification('模板填充完成', 'success');
    }

    /**
     * API填充
     */
    async apiFill(userData) {
        try {
            // 首先进行基础填充
            await this.basicFill(userData);

            // 然后尝试API直接提交
            const response = await chrome.runtime.sendMessage({
                action: 'directAPISubmit',
                data: userData
            });

            if (response.success) {
                this.showNotification('API提交成功', 'success');
            } else {
                this.showNotification('API提交失败，已切换到普通填充', 'warning');
            }
        } catch (error) {
            console.error('API填充失败:', error);
            this.showNotification('API填充失败: ' + error.message, 'error');
        }
    }

    /**
     * 基础填充
     */
    async basicFill(userData) {
        const fieldMapping = {
            name: userData.name,
            passNo: userData.passportNo,
            dob: userData.dateOfBirth,
            nationality: userData.nationality,
            sex: userData.sex,
            passExpiry: userData.passportExpiry,
            email: userData.email,
            confirmEmail: userData.email,
            countryCode: userData.countryCode,
            mobileNo: userData.mobileNo,
            arrivalDate: userData.arrivalDate,
            departureDate: userData.departureDate,
            vesselNm: userData.flightNo,
            trvlMode: userData.modeOfTravel,
            embark: userData.lastPort,
            accommodationStay: userData.accommodation,
            accommodationAddress1: userData.address,
            accommodationAddress2: userData.address2,
            accommodationState: userData.state,
            accommodationPostcode: userData.postcode,
            accommodationCity: userData.city
        };

        for (const [fieldId, value] of Object.entries(fieldMapping)) {
            if (value && this.formFields[fieldId]) {
                await this.fillField(fieldId, value);
                await this.delay(100); // 避免填充过快
            }
        }
    }

    /**
     * 填充单个字段
     */
    async fillField(fieldId, value) {
        const field = this.formFields[fieldId];
        if (!field) return;

        try {
            if (field.tagName === 'SELECT') {
                // 下拉选择框
                field.value = value;
                field.dispatchEvent(new Event('change', { bubbles: true }));
            } else if (field.type === 'text' || field.type === 'email' || field.type === 'tel') {
                // 文本输入框
                field.focus();
                field.value = '';
                
                // 模拟逐字输入
                for (const char of value) {
                    field.value += char;
                    field.dispatchEvent(new Event('input', { bubbles: true }));
                    await this.delay(50);
                }
                
                field.dispatchEvent(new Event('change', { bubbles: true }));
                field.blur();
            }
        } catch (error) {
            console.error(`填充字段 ${fieldId} 失败:`, error);
        }
    }

    /**
     * AI验证和优化
     */
    async aiValidateAndOptimize() {
        try {
            const formData = this.getFormData();

            // 使用配置文件中的提示词模板
            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.FORM_OPTIMIZATION.replace(
                '{formData}',
                JSON.stringify(formData, null, 2)
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_AUDITOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                this.showAISuggestions(`✨ AI优化建议：<br><br>${response.data.replace(/\n/g, '<br>')}`);
                this.showNotification('AI分析完成，请查看建议', 'success');
            } else {
                this.showAISuggestions('AI分析暂时不可用，请检查网络连接和API配置。');
                this.showNotification('AI服务暂时不可用', 'error');
            }
        } catch (error) {
            console.error('AI验证失败:', error);
            this.showNotification('AI验证失败: ' + error.message, 'error');
        }
    }

    /**
     * 获取表单数据
     */
    getFormData() {
        const data = {};
        
        Object.keys(this.formFields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field && field.value) {
                data[fieldId] = field.value;
            }
        });

        return data;
    }

    /**
     * 获取用户数据
     */
    async getUserData() {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getUserData'
            });
            
            if (response.success) {
                return response.data;
            }
            
            // 如果没有用户数据，返回默认测试数据
            return this.getDefaultTestData();
        } catch (error) {
            console.error('获取用户数据失败:', error);
            return this.getDefaultTestData();
        }
    }

    /**
     * 获取默认测试数据
     */
    getDefaultTestData() {
        return {
            name: 'ZHANG SAN',
            passportNo: 'A12345678',
            dateOfBirth: '01/01/1990',
            nationality: 'CHN',
            sex: '1',
            passportExpiry: '31/12/2030',
            email: '<EMAIL>',
            countryCode: '+60',
            mobileNo: '*********',
            arrivalDate: '10/01/2025',
            departureDate: '15/01/2025',
            flightNo: 'MH123',
            modeOfTravel: 'AIR',
            lastPort: 'SGP',
            accommodation: '01',
            address: 'KUALA LUMPUR CITY CENTER HOTEL',
            address2: 'JALAN BUKIT BINTANG',
            state: '14',
            postcode: '50000',
            city: '1400'
        };
    }

    /**
     * 注入AI助手界面
     */
    injectAIAssistant() {
        // 创建AI助手浮动面板
        const aiPanel = document.createElement('div');
        aiPanel.id = 'mdac-ai-assistant';
        aiPanel.innerHTML = `
            <div class="ai-header">
                <span class="ai-icon">🤖</span>
                <span class="ai-title">MDAC AI助手</span>
                <button class="ai-toggle" id="aiToggle">−</button>
            </div>
            <div class="ai-content" id="aiContent">
                <div class="ai-status">准备就绪</div>
                <div class="ai-suggestions" id="aiSuggestions"></div>
                <div class="ai-actions">
                    <button class="ai-btn" id="aiValidate">验证表单</button>
                    <button class="ai-btn" id="aiOptimize">优化建议</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(aiPanel);
        this.setupAIAssistantEvents();
    }

    /**
     * 设置AI助手事件
     */
    setupAIAssistantEvents() {
        const toggle = document.getElementById('aiToggle');
        const content = document.getElementById('aiContent');
        const validateBtn = document.getElementById('aiValidate');
        const optimizeBtn = document.getElementById('aiOptimize');

        toggle.addEventListener('click', () => {
            const isVisible = content.style.display !== 'none';
            content.style.display = isVisible ? 'none' : 'block';
            toggle.textContent = isVisible ? '+' : '−';
        });

        validateBtn.addEventListener('click', () => this.validateForm());
        optimizeBtn.addEventListener('click', () => this.aiValidateAndOptimize());
    }

    /**
     * 显示AI建议
     */
    showAISuggestions(suggestions) {
        const suggestionsDiv = document.getElementById('aiSuggestions');
        if (suggestionsDiv) {
            suggestionsDiv.innerHTML = suggestions.replace(/\n/g, '<br>');
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `mdac-notification ${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    /**
     * 观察表单变化
     */
    observeFormChanges() {
        if (!this.form) return;

        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    this.handleFormChange();
                }
            });
        });

        observer.observe(this.form, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value', 'disabled']
        });
    }

    /**
     * 处理表单变化
     */
    handleFormChange() {
        // 实时验证和建议
        if (this.fillMode === 'smart') {
            this.debounce(() => {
                this.realtimeValidation();
            }, 500)();
        }
    }

    /**
     * 实时验证
     */
    async realtimeValidation() {
        // 获取所有已填写的字段
        const filledFields = {};
        Object.keys(this.formFields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field && field.value) {
                filledFields[fieldId] = field.value;
            }
        });

        // 如果有足够的字段，进行AI验证
        if (Object.keys(filledFields).length >= 3) {
            await this.validateFieldsWithAI(filledFields);
        }
    }

    /**
     * 使用AI验证字段
     */
    async validateFieldsWithAI(fields) {
        try {
            // 检查是否启用实时验证
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.REALTIME_VALIDATION.enabled) {
                return;
            }

            const prompt = `请验证以下MDAC表单字段的数据：

${Object.entries(fields).map(([key, value]) => `${key}: ${value}`).join('\n')}

请检查：
1. 数据格式是否正确
2. 字段间的逻辑关系
3. 是否符合马来西亚入境要求

请简洁回复验证结果和建议。`;

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_VALIDATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                this.showAISuggestions(`🔍 实时验证：${response.data}`);
                this.highlightValidatedFields(fields, response.data);
            }
        } catch (error) {
            console.error('AI实时验证失败:', error);
        }
    }

    /**
     * 验证单个字段
     */
    async validateSingleField(fieldId, value) {
        try {
            const promptTemplate = window.MDAC_AI_CONFIG.AI_PROMPTS.FIELD_VALIDATION[fieldId];
            if (!promptTemplate) {
                return; // 没有对应的验证模板
            }

            const prompt = promptTemplate(value);
            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.FORM_VALIDATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                const field = this.formFields[fieldId];
                if (field) {
                    this.showFieldTooltip(field, response.data);
                    this.updateFieldValidationStatus(field, response.data);
                }
            }
        } catch (error) {
            console.error(`字段 ${fieldId} AI验证失败:`, error);
        }
    }

    /**
     * 高亮验证过的字段
     */
    highlightValidatedFields(fields, aiResult) {
        const isPositive = aiResult.toLowerCase().includes('正确') ||
                          aiResult.toLowerCase().includes('有效') ||
                          aiResult.toLowerCase().includes('符合');

        Object.keys(fields).forEach(fieldId => {
            const field = this.formFields[fieldId];
            if (field) {
                field.classList.remove('mdac-field-success', 'mdac-field-error');
                field.classList.add(isPositive ? 'mdac-field-success' : 'mdac-field-error');
            }
        });
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 通知扩展
     */
    notifyExtension(event, data = {}) {
        chrome.runtime.sendMessage({
            action: 'contentScriptEvent',
            event: event,
            data: data
        });
    }

    /**
     * 保存到历史记录
     */
    async saveToHistory(userData, mode) {
        await chrome.runtime.sendMessage({
            action: 'saveFormData',
            data: {
                ...userData,
                mode: mode,
                url: window.location.href,
                timestamp: Date.now()
            }
        });
    }

    /**
     * 获取页面信息
     */
    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            pageType: this.pageType,
            formExists: !!this.form,
            fieldsCount: Object.keys(this.formFields).length
        };
    }

    /**
     * AI智能内容解析
     */
    async parseContentWithAI(content) {
        try {
            // 检查是否启用内容解析功能
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.CONTENT_PARSING.enabled) {
                this.showNotification('内容解析功能已禁用', 'warning');
                return null;
            }

            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.CONTENT_PARSING.replace(
                '{content}',
                content
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.DATA_EXTRACTOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                try {
                    // 尝试解析JSON
                    const cleanResult = response.data.replace(/```json|```/g, '').trim();
                    const extractedData = JSON.parse(cleanResult);

                    // 填充提取的数据
                    await this.fillExtractedData(extractedData);
                    this.showNotification('AI内容解析完成', 'success');

                    return extractedData;
                } catch (parseError) {
                    console.error('解析AI返回的JSON失败:', parseError);
                    this.showNotification('AI解析结果格式异常', 'error');
                }
            }
        } catch (error) {
            console.error('AI内容解析失败:', error);
            this.showNotification('AI内容解析失败', 'error');
        }
        return null;
    }

    /**
     * 填充提取的数据
     */
    async fillExtractedData(data) {
        for (const [fieldId, value] of Object.entries(data)) {
            if (value && this.formFields[fieldId]) {
                await this.fillField(fieldId, value);
                await this.delay(100);
            }
        }
    }

    /**
     * 智能地址翻译
     */
    async translateAddressWithAI(chineseAddress) {
        try {
            // 检查是否启用地址翻译功能
            if (!window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.enabled) {
                this.showNotification('地址翻译功能已禁用', 'warning');
                return chineseAddress;
            }

            const prompt = window.MDAC_AI_CONFIG.AI_PROMPTS.ADDRESS_TRANSLATION.replace(
                '{address}',
                chineseAddress
            );

            const context = window.MDAC_AI_CONFIG.AI_CONTEXTS.ADDRESS_TRANSLATOR;

            const response = await chrome.runtime.sendMessage({
                action: 'callGeminiAI',
                prompt: prompt,
                context: context
            });

            if (response.success) {
                let result = response.data;

                // 如果启用了结果清理
                if (window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.cleanResult) {
                    result = result.replace(/[^\w\s,.-]/g, '').trim();
                }

                this.showNotification('地址翻译完成', 'success');
                return result;
            }
        } catch (error) {
            console.error('地址翻译失败:', error);
            this.showNotification('地址翻译失败', 'error');
        }
        return chineseAddress; // 如果翻译失败，返回原地址
    }

    /**
     * 验证表单
     */
    async validateForm() {
        const formData = this.getFormData();
        const filledCount = Object.keys(formData).length;
        const totalFields = Object.keys(this.formFields).length;

        if (filledCount === 0) {
            this.showNotification('请先填写表单数据', 'warning');
            return;
        }

        // 使用AI进行全面验证
        await this.aiValidateAndOptimize();

        return {
            isValid: filledCount >= totalFields * 0.8, // 至少填写80%的字段
            filledCount: filledCount,
            totalFields: totalFields,
            completeness: (filledCount / totalFields * 100).toFixed(1)
        };
    }

    /**
     * 显示字段提示
     */
    showFieldTooltip(field, message) {
        // 移除现有的提示
        const existingTooltip = document.querySelector('.mdac-field-tooltip');
        if (existingTooltip) {
            existingTooltip.remove();
        }

        // 创建新的提示
        const tooltip = document.createElement('div');
        tooltip.className = 'mdac-field-tooltip';
        tooltip.textContent = message;

        // 确定提示类型
        const isPositive = message.toLowerCase().includes('正确') ||
                          message.toLowerCase().includes('有效') ||
                          message.toLowerCase().includes('符合');

        tooltip.classList.add(isPositive ? 'success' : 'warning');

        // 定位提示
        const rect = field.getBoundingClientRect();
        tooltip.style.position = 'absolute';
        tooltip.style.top = (rect.bottom + window.scrollY + 5) + 'px';
        tooltip.style.left = (rect.left + window.scrollX) + 'px';
        tooltip.style.zIndex = '10003';

        document.body.appendChild(tooltip);

        // 3秒后自动移除
        setTimeout(() => {
            tooltip.remove();
        }, 3000);
    }

    /**
     * 更新字段验证状态
     */
    updateFieldValidationStatus(field, aiResult) {
        const isPositive = aiResult.toLowerCase().includes('正确') ||
                          aiResult.toLowerCase().includes('有效') ||
                          aiResult.toLowerCase().includes('符合');

        field.classList.remove('mdac-field-success', 'mdac-field-error', 'mdac-field-highlight');
        field.classList.add(isPositive ? 'mdac-field-success' : 'mdac-field-error');
    }

    /**
     * 检测中文内容
     */
    containsChinese(text) {
        return /[\u4e00-\u9fff]/.test(text);
    }

    /**
     * 自动检测并翻译地址
     */
    async autoTranslateAddress(field) {
        if (!window.MDAC_AI_CONFIG.AI_FEATURES.ADDRESS_TRANSLATION.autoDetect) {
            return;
        }

        const value = field.value.trim();
        if (value && this.containsChinese(value)) {
            const translatedAddress = await this.translateAddressWithAI(value);
            if (translatedAddress !== value) {
                field.value = translatedAddress;
                field.dispatchEvent(new Event('change', { bubbles: true }));
                this.showNotification('地址已自动翻译为英文', 'success');
            }
        }
    }
}

// 初始化内容脚本
new MDACContentScript();
