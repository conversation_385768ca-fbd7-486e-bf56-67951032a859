<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC AI布局测试</title>
    <link rel="stylesheet" href="src/ui/sidepanel/sidepanel.css">
    <style>
        /* 调试样式 - 显示布局边框 */
        .debug .top-section {
            border: 2px solid #ff0000 !important;
        }
        
        .debug .middle-section {
            border: 2px solid #00ff00 !important;
        }
        
        .debug .bottom-section {
            border: 2px solid #0000ff !important;
        }
        
        .debug .personal-info-column {
            border-right: 2px solid #ff9900 !important;
        }
        
        .debug .travel-info-column {
            border-left: 1px solid #9900ff !important;
        }
        
        /* 测试按钮样式 */
        .test-controls {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 10000;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-btn {
            margin: 2px;
            padding: 5px 10px;
            border: 1px solid #ccc;
            background: #f5f5f5;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .test-btn:hover {
            background: #e5e5e5;
        }
    </style>
</head>
<body class="sidepanel-body">
    <!-- 测试控制面板 -->
    <div class="test-controls">
        <div>布局测试控制</div>
        <button class="test-btn" onclick="toggleDebug()">切换调试边框</button>
        <button class="test-btn" onclick="toggleCompact()">切换紧凑模式</button>
        <button class="test-btn" onclick="fillTestData()">填充测试数据</button>
    </div>

    <div class="sidepanel-container" id="container">
        <!-- 连接状态指示器 -->
        <div class="connection-status connected" id="connectionStatus">
            🟢 已连接到MDAC网站
        </div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="logo">
                <div style="width: 24px; height: 24px; background: #fff; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 12px;">AI</div>
                <h1>MDAC AI助手</h1>
            </div>
            <div class="ai-status" id="aiStatus">
                <span class="status-dot"></span>
                <span class="status-text">AI就绪</span>
            </div>
        </div>

        <!-- 网站检测区域 -->
        <div class="site-detection" id="siteDetection">
            <div class="detection-status detected" id="detectionStatus">
                <span class="icon">🔍</span>
                <span class="text">✅ 已检测到MDAC网站</span>
            </div>
        </div>

        <!-- 简洁2x2网格布局 -->
        <div class="grid-layout">
            <!-- 顶部区域：智能解析输入区 -->
            <div class="top-section">
                <div class="input-grid">
                    <!-- 左侧：文本输入 -->
                    <div class="text-input-area">
                        <div class="input-header">
                            <span class="input-icon">📝</span>
                            <span class="input-title">智能解析</span>
                        </div>
                        <textarea
                            id="contentInput"
                            class="smart-textarea"
                            placeholder="粘贴邮件、聊天记录或直接输入旅客信息..."
                            rows="4">
                        </textarea>
                        <button class="parse-btn" id="parseContentBtn">
                            <span class="btn-icon">🚀</span>
                            <span class="btn-text">AI解析</span>
                        </button>
                    </div>

                    <!-- 右侧：图片上传 -->
                    <div class="image-input-area">
                        <div class="input-header">
                            <span class="input-icon">📷</span>
                            <span class="input-title">图片识别</span>
                        </div>
                        <div class="upload-zone" id="uploadZone">
                            <div class="upload-content">
                                <div class="upload-icon">📁</div>
                                <div class="upload-text">点击上传或拖拽图片</div>
                                <div class="upload-hint">支持护照、身份证识别</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中部区域：字段映射显示区 -->
            <div class="middle-section">
                <div class="fields-grid">
                    <!-- 左列：个人信息 -->
                    <div class="personal-info-column">
                        <div class="column-header">
                            <span class="column-icon">👤</span>
                            <span class="column-title">Personal Information</span>
                        </div>

                        <!-- 邮箱和电话预设区域 -->
                        <div class="preset-section">
                            <div class="preset-header">
                                <span class="preset-icon">📧</span>
                                <span class="preset-title">常用信息</span>
                                <button class="preset-edit-btn" id="editPresetBtn">✏️</button>
                            </div>
                            <div class="preset-fields">
                                <div class="preset-field">
                                    <label>邮箱</label>
                                    <input type="email" id="presetEmail" class="preset-input" placeholder="<EMAIL>">
                                </div>
                                <div class="preset-field">
                                    <label>电话</label>
                                    <input type="tel" id="presetPhone" class="preset-input" placeholder="+60123456789">
                                </div>
                            </div>
                        </div>

                        <!-- 个人信息字段 -->
                        <div class="field-group" id="personalFields">
                            <div class="field-item">
                                <label>姓名</label>
                                <input type="text" id="name" class="field-input" placeholder="Full Name">
                                <div class="field-status" data-field="name"></div>
                            </div>
                            <div class="field-item">
                                <label>护照号码</label>
                                <input type="text" id="passportNo" class="field-input" placeholder="A12345678">
                                <div class="field-status" data-field="passportNo"></div>
                            </div>
                            <div class="field-item">
                                <label>出生日期</label>
                                <input type="date" id="dateOfBirth" class="field-input">
                                <div class="field-status" data-field="dateOfBirth"></div>
                            </div>
                            <div class="field-item">
                                <label>国籍</label>
                                <select id="nationality" class="field-input">
                                    <option value="">选择国籍</option>
                                    <option value="CHN">中国</option>
                                    <option value="USA">美国</option>
                                    <option value="SGP">新加坡</option>
                                    <option value="MYS">马来西亚</option>
                                </select>
                                <div class="field-status" data-field="nationality"></div>
                            </div>
                            <div class="field-item">
                                <label>性别</label>
                                <select id="sex" class="field-input">
                                    <option value="">选择性别</option>
                                    <option value="1">男</option>
                                    <option value="2">女</option>
                                </select>
                                <div class="field-status" data-field="sex"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 右列：旅行信息 -->
                    <div class="travel-info-column">
                        <div class="column-header">
                            <span class="column-icon">✈️</span>
                            <span class="column-title">Traveling Information</span>
                        </div>

                        <div class="field-group" id="travelFields">
                            <div class="field-item">
                                <label>到达日期</label>
                                <input type="date" id="arrivalDate" class="field-input">
                                <div class="field-status" data-field="arrivalDate"></div>
                            </div>
                            <div class="field-item">
                                <label>离开日期</label>
                                <input type="date" id="departureDate" class="field-input">
                                <div class="field-status" data-field="departureDate"></div>
                            </div>
                            <div class="field-item">
                                <label>航班号</label>
                                <input type="text" id="flightNo" class="field-input" placeholder="MH123">
                                <div class="field-status" data-field="flightNo"></div>
                            </div>
                            <div class="field-item">
                                <label>住宿类型</label>
                                <select id="accommodation" class="field-input">
                                    <option value="">选择住宿类型</option>
                                    <option value="01">酒店</option>
                                    <option value="02">民宿</option>
                                    <option value="03">朋友家</option>
                                    <option value="04">其他</option>
                                </select>
                                <div class="field-status" data-field="accommodation"></div>
                            </div>
                            <div class="field-item">
                                <label>住宿地址</label>
                                <textarea id="address" class="field-input address-input" placeholder="详细地址" rows="2"></textarea>
                                <div class="field-status" data-field="address"></div>
                            </div>
                            <div class="field-item">
                                <label>城市</label>
                                <select id="city" class="field-input">
                                    <option value="">选择城市</option>
                                    <option value="1400">吉隆坡</option>
                                    <option value="1000">槟城</option>
                                    <option value="0700">马六甲</option>
                                </select>
                                <div class="field-status" data-field="city"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部区域：操作按钮区 -->
            <div class="bottom-section">
                <div class="action-buttons">
                    <!-- 主要按钮 -->
                    <button class="primary-action-btn" id="updateToMDACBtn">
                        <span class="btn-icon">🚀</span>
                        <span class="btn-text">更新到MDAC页面</span>
                    </button>

                    <!-- 辅助按钮 -->
                    <div class="secondary-actions">
                        <button class="secondary-btn" id="previewBtn">
                            <span class="btn-icon">👁️</span>
                            <span class="btn-text">预览</span>
                        </button>
                        <button class="secondary-btn" id="saveBtn">
                            <span class="btn-icon">💾</span>
                            <span class="btn-text">保存</span>
                        </button>
                        <button class="secondary-btn" id="clearBtn">
                            <span class="btn-icon">🗑️</span>
                            <span class="btn-text">清除</span>
                        </button>
                        <button class="secondary-btn" id="settingsBtn">
                            <span class="btn-icon">⚙️</span>
                            <span class="btn-text">设置</span>
                        </button>
                    </div>
                </div>

                <!-- 状态指示器 -->
                <div class="status-indicators">
                    <div class="status-item" id="confidenceStatus">
                        <span class="status-icon">🎯</span>
                        <span class="status-text">置信度: --</span>
                    </div>
                    <div class="status-item" id="completenessStatus">
                        <span class="status-icon">📊</span>
                        <span class="status-text">完整度: --</span>
                    </div>
                    <div class="status-item" id="fieldsStatus">
                        <span class="status-icon">📝</span>
                        <span class="status-text">字段: 0/12</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 简化的footer -->
        <div class="footer">
            <div class="footer-info">
                <span class="version">MDAC AI v2.0</span>
                <span class="status">智能增强版</span>
            </div>
        </div>
    </div>

    <script>
        // 测试功能
        function toggleDebug() {
            document.getElementById('container').classList.toggle('debug');
        }
        
        function toggleCompact() {
            document.getElementById('container').classList.toggle('compact-mode');
        }
        
        function fillTestData() {
            document.getElementById('name').value = '张三';
            document.getElementById('passportNo').value = 'A12345678';
            document.getElementById('dateOfBirth').value = '1990-01-01';
            document.getElementById('nationality').value = 'CHN';
            document.getElementById('sex').value = '1';
            document.getElementById('arrivalDate').value = '2024-01-15';
            document.getElementById('departureDate').value = '2024-01-25';
            document.getElementById('flightNo').value = 'MH123';
            document.getElementById('accommodation').value = '01';
            document.getElementById('address').value = '吉隆坡市中心酒店';
            document.getElementById('city').value = '1400';
            document.getElementById('presetEmail').value = '<EMAIL>';
            document.getElementById('presetPhone').value = '+60123456789';
            
            // 添加填充状态
            document.querySelectorAll('.field-input').forEach(input => {
                if (input.value) {
                    input.classList.add('filled');
                }
            });
        }
    </script>
</body>
</html>
