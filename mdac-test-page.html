<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MDAC测试页面 - 模拟官方表单</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        
        .section h3 {
            color: #666;
            margin-bottom: 15px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .required {
            color: red;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        input:focus, select:focus, textarea:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .test-controls {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #e9ecef;
            border-radius: 5px;
        }
        
        .note {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>🧪 MDAC表单测试页面</h1>
        
        <div class="note">
            <strong>测试说明：</strong>这是一个模拟MDAC官方表单的测试页面，用于验证自动填充脚本的功能。
            请使用我们的自动填充工具在此页面进行测试。
        </div>
        
        <div class="test-controls">
            <h3>🔧 测试控制</h3>
            <button class="btn btn-success" onclick="testBookmarklet()">测试书签工具</button>
            <button class="btn btn-warning" onclick="clearAllFields()">清空所有字段</button>
            <button class="btn" onclick="showFilledData()">显示已填充数据</button>
        </div>

        <form id="mdacTestForm">
            <!-- 个人信息部分 -->
            <div class="section">
                <h3>Personal Information 个人信息</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Name 姓名 <span class="required">*</span></label>
                        <input type="text" id="name" name="name" placeholder="Enter your full name as in passport">
                    </div>
                    <div class="form-group">
                        <label for="passportNo">Passport No. 护照号码 <span class="required">*</span></label>
                        <input type="text" id="passportNo" name="passport" placeholder="Enter passport number">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth 出生日期 <span class="required">*</span></label>
                        <input type="text" id="dateOfBirth" name="birth" placeholder="DD/MM/YYYY">
                    </div>
                    <div class="form-group">
                        <label for="nationality">Nationality 国籍 <span class="required">*</span></label>
                        <select id="nationality" name="nationality">
                            <option value="">Select Nationality</option>
                            <option value="CHINA">China</option>
                            <option value="SINGAPORE">Singapore</option>
                            <option value="THAILAND">Thailand</option>
                            <option value="INDONESIA">Indonesia</option>
                            <option value="PHILIPPINES">Philippines</option>
                            <option value="VIETNAM">Vietnam</option>
                            <option value="INDIA">India</option>
                            <option value="JAPAN">Japan</option>
                            <option value="SOUTH KOREA">South Korea</option>
                            <option value="UNITED STATES">United States</option>
                            <option value="UNITED KINGDOM">United Kingdom</option>
                            <option value="AUSTRALIA">Australia</option>
                            <option value="CANADA">Canada</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="sex">Sex 性别 <span class="required">*</span></label>
                        <select id="sex" name="sex">
                            <option value="">Select Sex</option>
                            <option value="MALE">Male</option>
                            <option value="FEMALE">Female</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="passportExpiry">Date of Passport Expiry 护照到期日期 <span class="required">*</span></label>
                        <input type="text" id="passportExpiry" name="expiry" placeholder="DD/MM/YYYY">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email Address 电子邮箱 <span class="required">*</span></label>
                        <input type="email" id="email" name="email" placeholder="Enter email address">
                    </div>
                    <div class="form-group">
                        <label for="confirmEmail">Confirm Email Address 确认电子邮箱 <span class="required">*</span></label>
                        <input type="email" id="confirmEmail" name="confirmEmail" placeholder="Confirm email address">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="countryCode">Country/Region Code 国家/地区代码 <span class="required">*</span></label>
                        <input type="text" id="countryCode" name="countryCode" placeholder="+60">
                    </div>
                    <div class="form-group">
                        <label for="mobileNo">Mobile No. 手机号码 <span class="required">*</span></label>
                        <input type="tel" id="mobileNo" name="mobile" placeholder="Enter mobile number">
                    </div>
                </div>
            </div>

            <!-- 旅行信息部分 -->
            <div class="section">
                <h3>Traveling Information 旅行信息</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="arrivalDate">Date of Arrival 到达日期 <span class="required">*</span></label>
                        <input type="text" id="arrivalDate" name="arrival" placeholder="DD/MM/YYYY">
                    </div>
                    <div class="form-group">
                        <label for="departureDate">Date of Departure 离开日期 <span class="required">*</span></label>
                        <input type="text" id="departureDate" name="departure" placeholder="DD/MM/YYYY">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="flightNo">Flight/Vessel/Transportation No. 航班号 <span class="required">*</span></label>
                        <input type="text" id="flightNo" name="flight" placeholder="Enter flight number">
                    </div>
                    <div class="form-group">
                        <label for="modeOfTravel">Mode of Travel 旅行方式 <span class="required">*</span></label>
                        <select id="modeOfTravel" name="modeOfTravel">
                            <option value="">Select Mode</option>
                            <option value="AIR">Air</option>
                            <option value="SEA">Sea</option>
                            <option value="LAND">Land</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="lastPort">Last Port of Embarkation before Malaysia 最后港口 <span class="required">*</span></label>
                    <input type="text" id="lastPort" name="lastPort" placeholder="Enter last port">
                </div>

                <div class="form-group">
                    <label for="accommodation">Accommodation of Stay 住宿安排 <span class="required">*</span></label>
                    <select id="accommodation" name="accommodation">
                        <option value="">Select Accommodation</option>
                        <option value="HOTEL">Hotel</option>
                        <option value="PRIVATE">Private</option>
                        <option value="HOSTEL">Hostel</option>
                        <option value="APARTMENT">Apartment</option>
                        <option value="OTHERS">Others</option>
                    </select>
                </div>
            </div>

            <!-- 地址信息部分 -->
            <div class="section">
                <h3>Address Information 地址信息</h3>
                
                <div class="form-group">
                    <label for="address">Address (In Malaysia) 地址 <span class="required">*</span></label>
                    <textarea id="address" name="address" rows="3" placeholder="Enter address in Malaysia"></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="state">State 州 <span class="required">*</span></label>
                        <select id="state" name="state">
                            <option value="">Select State</option>
                            <option value="KUALA LUMPUR">Kuala Lumpur</option>
                            <option value="SELANGOR">Selangor</option>
                            <option value="JOHOR">Johor</option>
                            <option value="PENANG">Penang</option>
                            <option value="PERAK">Perak</option>
                            <option value="NEGERI SEMBILAN">Negeri Sembilan</option>
                            <option value="MALACCA">Malacca</option>
                            <option value="PAHANG">Pahang</option>
                            <option value="TERENGGANU">Terengganu</option>
                            <option value="KELANTAN">Kelantan</option>
                            <option value="KEDAH">Kedah</option>
                            <option value="PERLIS">Perlis</option>
                            <option value="SABAH">Sabah</option>
                            <option value="SARAWAK">Sarawak</option>
                            <option value="PUTRAJAYA">Putrajaya</option>
                            <option value="LABUAN">Labuan</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="postcode">Postcode 邮政编码 <span class="required">*</span></label>
                        <input type="text" id="postcode" name="postcode" placeholder="Enter postcode">
                    </div>
                </div>

                <div class="form-group">
                    <label for="city">City 城市 <span class="required">*</span></label>
                    <input type="text" id="city" name="city" placeholder="Enter city">
                </div>
            </div>

            <div class="test-controls">
                <button type="button" class="btn btn-success" onclick="submitForm()">Submit 提交</button>
                <button type="reset" class="btn btn-warning">Reset 重置</button>
            </div>
        </form>
    </div>

    <script>
        // 测试书签工具功能
        function testBookmarklet() {
            // 模拟书签工具的功能
            const data = JSON.parse(localStorage.getItem('mdacFormData') || '{}');
            
            if (!data.name) {
                alert('请先在MDAC工具中填写并保存数据！');
                return;
            }

            // 填充字段的函数
            function fillField(selector, value) {
                const el = document.querySelector(selector);
                if (el) {
                    if (el.tagName === 'SELECT') {
                        // 处理下拉选择框
                        for (let i = 0; i < el.options.length; i++) {
                            if (el.options[i].value === value || el.options[i].text.includes(value)) {
                                el.value = el.options[i].value;
                                break;
                            }
                        }
                    } else {
                        // 处理输入框
                        el.value = value;
                    }
                    
                    // 触发事件
                    el.dispatchEvent(new Event('change', {bubbles: true}));
                    el.dispatchEvent(new Event('input', {bubbles: true}));
                    
                    // 添加视觉反馈
                    el.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        el.style.backgroundColor = '';
                    }, 1000);
                    
                    return true;
                }
                return false;
            }

            // 开始填充
            setTimeout(() => {
                let count = 0;
                
                // 填充个人信息
                count += fillField('input[name*="name"]', data.name) ? 1 : 0;
                count += fillField('input[name*="passport"]', data.passportNo) ? 1 : 0;
                count += fillField('input[name*="birth"]', data.dateOfBirth) ? 1 : 0;
                count += fillField('select[name*="nationality"]', data.nationality) ? 1 : 0;
                count += fillField('select[name*="sex"]', data.sex) ? 1 : 0;
                count += fillField('input[name*="expiry"]', data.passportExpiry) ? 1 : 0;
                count += fillField('input[name*="email"]', data.email) ? 1 : 0;
                count += fillField('input[name*="mobile"]', data.mobileNo) ? 1 : 0;
                
                // 填充旅行信息
                count += fillField('input[name*="arrival"]', data.arrivalDate) ? 1 : 0;
                count += fillField('input[name*="departure"]', data.departureDate) ? 1 : 0;
                count += fillField('input[name*="flight"]', data.flightNo) ? 1 : 0;
                
                // 填充地址信息
                count += fillField('textarea[name*="address"]', data.address) ? 1 : 0;
                count += fillField('input[name*="postcode"]', data.postcode) ? 1 : 0;
                count += fillField('input[name*="city"]', data.city) ? 1 : 0;
                
                alert(`测试完成！已填充 ${count} 个字段。\n\n这证明自动填充脚本工作正常！`);
                
            }, 500);
        }

        // 清空所有字段
        function clearAllFields() {
            document.getElementById('mdacTestForm').reset();
            alert('所有字段已清空！');
        }

        // 显示已填充的数据
        function showFilledData() {
            const form = document.getElementById('mdacTestForm');
            const formData = new FormData(form);
            let data = '';
            
            for (let [key, value] of formData.entries()) {
                if (value) {
                    data += `${key}: ${value}\n`;
                }
            }
            
            if (data) {
                alert('已填充的数据：\n\n' + data);
            } else {
                alert('当前没有填充任何数据！');
            }
        }

        // 提交表单
        function submitForm() {
            const form = document.getElementById('mdacTestForm');
            const formData = new FormData(form);
            let filledFields = 0;
            let totalFields = 0;
            
            for (let [key, value] of formData.entries()) {
                totalFields++;
                if (value) {
                    filledFields++;
                }
            }
            
            alert(`表单提交测试：\n\n已填充字段：${filledFields}\n总字段数：${totalFields}\n完成度：${Math.round(filledFields/totalFields*100)}%\n\n这是测试页面，实际不会提交数据。`);
        }

        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MDAC测试页面已加载完成');
            console.log('请使用自动填充工具进行测试');
        });
    </script>
</body>
</html>
